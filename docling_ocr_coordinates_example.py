#!/usr/bin/env python3
"""
Example showing how to extract text with bounding box coordinates from documents using Docling.
This demonstrates OCR capabilities similar to PaddleOCR with text boxes and coordinates.
"""

from pathlib import Path
from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions, EasyOcrOptions


def extract_text_with_coordinates(document_path: str):
    """
    Extract text with bounding box coordinates from a document using Docling.
    
    Args:
        document_path: Path to the document (PDF, image, etc.)
    
    Returns:
        List of dictionaries containing text and coordinate information
    """
    
    # Configure OCR options
    pipeline_options = PdfPipelineOptions()
    pipeline_options.do_ocr = True  # Enable OCR
    pipeline_options.force_full_page_ocr = True  # Force OCR on entire page
    pipeline_options.ocr_options = EasyOcrOptions()  # Use EasyOCR engine
    
    # Create document converter with OCR enabled
    converter = DocumentConverter(
        format_options={
            InputFormat.PDF: PdfFormatOption(
                pipeline_options=pipeline_options
            )
        }
    )
    
    # Convert the document
    result = converter.convert(document_path)
    
    # Extract text with coordinates from all pages
    text_with_coords = []
    
    # Access the internal page structure to get text cells with coordinates
    for page_num, page in enumerate(result.input._backend.pages):
        print(f"\n=== Page {page_num + 1} ===")
        
        # Get text cells from the page
        for cell in page.cells:
            # Extract bounding box coordinates
            bbox = cell.rect.to_bounding_box()
            
            # Convert coordinates to a more standard format (top-left origin)
            if hasattr(page, 'size') and page.size:
                # Ensure coordinates are in top-left origin
                bbox_top_left = bbox.to_top_left_origin(page.size.height)
            else:
                bbox_top_left = bbox
            
            # Create result similar to PaddleOCR format
            text_info = {
                'text': cell.text,
                'confidence': getattr(cell, 'confidence', 1.0),
                'from_ocr': getattr(cell, 'from_ocr', False),
                'bbox': {
                    'left': bbox_top_left.l,
                    'top': bbox_top_left.t, 
                    'right': bbox_top_left.r,
                    'bottom': bbox_top_left.b,
                    'width': bbox_top_left.width,
                    'height': bbox_top_left.height
                },
                'page': page_num + 1
            }
            
            text_with_coords.append(text_info)
            
            # Print in PaddleOCR-like format
            print(f"Text: '{cell.text}'")
            print(f"  Bbox: ({bbox_top_left.l:.1f}, {bbox_top_left.t:.1f}, {bbox_top_left.r:.1f}, {bbox_top_left.b:.1f})")
            print(f"  Confidence: {getattr(cell, 'confidence', 1.0):.3f}")
            print(f"  From OCR: {getattr(cell, 'from_ocr', False)}")
            print()
    
    return text_with_coords


def extract_text_with_coordinates_from_conversion_result(document_path: str):
    """
    Alternative method: Extract text with coordinates from the conversion result.
    This method works with the high-level Docling document structure.
    """
    
    # Configure OCR options
    pipeline_options = PdfPipelineOptions()
    pipeline_options.do_ocr = True
    pipeline_options.force_full_page_ocr = True
    pipeline_options.ocr_options = EasyOcrOptions()
    
    converter = DocumentConverter(
        format_options={
            InputFormat.PDF: PdfFormatOption(
                pipeline_options=pipeline_options
            )
        }
    )
    
    # Convert document
    result = converter.convert(document_path)
    doc = result.document
    
    print(f"\n=== Document: {doc.name} ===")
    print(f"Pages: {len(doc.pages)}")
    
    # Extract text with coordinates from document items
    text_with_coords = []
    
    for item, level in doc.iterate_items():
        # Check if item has provenance (location information)
        if hasattr(item, 'prov') and item.prov:
            for prov in item.prov:
                if hasattr(prov, 'bbox') and prov.bbox:
                    text_info = {
                        'text': getattr(item, 'text', str(item)),
                        'label': getattr(item, 'label', 'unknown'),
                        'bbox': {
                            'left': prov.bbox.l,
                            'top': prov.bbox.t,
                            'right': prov.bbox.r, 
                            'bottom': prov.bbox.b,
                            'width': prov.bbox.width,
                            'height': prov.bbox.height
                        },
                        'page': getattr(prov, 'page_no', 1)
                    }
                    
                    text_with_coords.append(text_info)
                    
                    print(f"Text: '{getattr(item, 'text', str(item))}'")
                    print(f"  Label: {getattr(item, 'label', 'unknown')}")
                    print(f"  Bbox: ({prov.bbox.l:.1f}, {prov.bbox.t:.1f}, {prov.bbox.r:.1f}, {prov.bbox.b:.1f})")
                    print(f"  Page: {getattr(prov, 'page_no', 1)}")
                    print()
    
    return text_with_coords


def main():
    """
    Example usage of text extraction with coordinates.
    """
    
    # Example document path - replace with your document
    document_path = "sample_document.pdf"  # Change this to your document path
    
    if not Path(document_path).exists():
        print(f"Document not found: {document_path}")
        print("Please provide a valid document path.")
        return
    
    print("=== Method 1: Extract from page cells (low-level) ===")
    try:
        text_coords_1 = extract_text_with_coordinates(document_path)
        print(f"Extracted {len(text_coords_1)} text elements with coordinates")
    except Exception as e:
        print(f"Error with method 1: {e}")
    
    print("\n" + "="*60)
    print("=== Method 2: Extract from document items (high-level) ===")
    try:
        text_coords_2 = extract_text_with_coordinates_from_conversion_result(document_path)
        print(f"Extracted {len(text_coords_2)} text elements with coordinates")
    except Exception as e:
        print(f"Error with method 2: {e}")


if __name__ == "__main__":
    main()
