{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "wiki_duck", "origin": {"mimetype": "text/html", "binary_hash": 8165458525377019424, "filename": "wiki_duck.html"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/8"}, {"$ref": "#/groups/1"}, {"$ref": "#/pictures/0"}, {"$ref": "#/pictures/1"}, {"$ref": "#/pictures/2"}, {"$ref": "#/groups/2"}, {"$ref": "#/groups/3"}, {"$ref": "#/groups/4"}, {"$ref": "#/groups/5"}, {"$ref": "#/groups/6"}, {"$ref": "#/texts/19"}, {"$ref": "#/groups/7"}, {"$ref": "#/groups/8"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/216"}, {"$ref": "#/texts/220"}, {"$ref": "#/texts/221"}, {"$ref": "#/texts/224"}, {"$ref": "#/texts/228"}, {"$ref": "#/texts/232"}, {"$ref": "#/texts/234"}, {"$ref": "#/texts/238"}, {"$ref": "#/texts/239"}, {"$ref": "#/texts/247"}, {"$ref": "#/texts/253"}, {"$ref": "#/texts/261"}, {"$ref": "#/texts/264"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}, {"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}], "content_layer": "furniture", "name": "list", "label": "list"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/texts/13"}], "content_layer": "furniture", "name": "list", "label": "list"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "name": "list", "label": "list"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/14"}], "content_layer": "furniture", "name": "list", "label": "list"}, {"self_ref": "#/groups/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "name": "list", "label": "list"}, {"self_ref": "#/groups/5", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}], "content_layer": "furniture", "name": "list", "label": "list"}, {"self_ref": "#/groups/6", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/17"}, {"$ref": "#/texts/18"}], "content_layer": "furniture", "name": "list", "label": "list"}, {"self_ref": "#/groups/7", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}], "content_layer": "furniture", "name": "list", "label": "list"}, {"self_ref": "#/groups/8", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/22"}], "content_layer": "body", "name": "header-1", "label": "section"}, {"self_ref": "#/groups/9", "parent": {"$ref": "#/texts/22"}, "children": [{"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/texts/27"}, {"$ref": "#/texts/28"}, {"$ref": "#/texts/33"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/42"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/10", "parent": {"$ref": "#/texts/24"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/11", "parent": {"$ref": "#/texts/25"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/12", "parent": {"$ref": "#/texts/26"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/13", "parent": {"$ref": "#/texts/27"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/14", "parent": {"$ref": "#/texts/28"}, "children": [{"$ref": "#/texts/29"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/15", "parent": {"$ref": "#/texts/29"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/16", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/17", "parent": {"$ref": "#/texts/31"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/18", "parent": {"$ref": "#/texts/32"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/19", "parent": {"$ref": "#/texts/33"}, "children": [{"$ref": "#/texts/34"}, {"$ref": "#/texts/35"}, {"$ref": "#/texts/36"}, {"$ref": "#/texts/37"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/20", "parent": {"$ref": "#/texts/34"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/21", "parent": {"$ref": "#/texts/35"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/22", "parent": {"$ref": "#/texts/36"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/23", "parent": {"$ref": "#/texts/37"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/24", "parent": {"$ref": "#/texts/38"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/25", "parent": {"$ref": "#/texts/39"}, "children": [{"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/26", "parent": {"$ref": "#/texts/40"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/27", "parent": {"$ref": "#/texts/41"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/28", "parent": {"$ref": "#/texts/42"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/29", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/44"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}, {"$ref": "#/texts/47"}, {"$ref": "#/texts/48"}, {"$ref": "#/texts/49"}, {"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}, {"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}, {"$ref": "#/texts/54"}, {"$ref": "#/texts/55"}, {"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/texts/60"}, {"$ref": "#/texts/61"}, {"$ref": "#/texts/62"}, {"$ref": "#/texts/63"}, {"$ref": "#/texts/64"}, {"$ref": "#/texts/65"}, {"$ref": "#/texts/66"}, {"$ref": "#/texts/67"}, {"$ref": "#/texts/68"}, {"$ref": "#/texts/69"}, {"$ref": "#/texts/70"}, {"$ref": "#/texts/71"}, {"$ref": "#/texts/72"}, {"$ref": "#/texts/73"}, {"$ref": "#/texts/74"}, {"$ref": "#/texts/75"}, {"$ref": "#/texts/76"}, {"$ref": "#/texts/77"}, {"$ref": "#/texts/78"}, {"$ref": "#/texts/79"}, {"$ref": "#/texts/80"}, {"$ref": "#/texts/81"}, {"$ref": "#/texts/82"}, {"$ref": "#/texts/83"}, {"$ref": "#/texts/84"}, {"$ref": "#/texts/85"}, {"$ref": "#/texts/86"}, {"$ref": "#/texts/87"}, {"$ref": "#/texts/88"}, {"$ref": "#/texts/89"}, {"$ref": "#/texts/90"}, {"$ref": "#/texts/91"}, {"$ref": "#/texts/92"}, {"$ref": "#/texts/93"}, {"$ref": "#/texts/94"}, {"$ref": "#/texts/95"}, {"$ref": "#/texts/96"}, {"$ref": "#/texts/97"}, {"$ref": "#/texts/98"}, {"$ref": "#/texts/99"}, {"$ref": "#/texts/100"}, {"$ref": "#/texts/101"}, {"$ref": "#/texts/102"}, {"$ref": "#/texts/103"}, {"$ref": "#/texts/104"}, {"$ref": "#/texts/105"}, {"$ref": "#/texts/106"}, {"$ref": "#/texts/107"}, {"$ref": "#/texts/108"}, {"$ref": "#/texts/109"}, {"$ref": "#/texts/110"}, {"$ref": "#/texts/111"}, {"$ref": "#/texts/112"}, {"$ref": "#/texts/113"}, {"$ref": "#/texts/114"}, {"$ref": "#/texts/115"}, {"$ref": "#/texts/116"}, {"$ref": "#/texts/117"}, {"$ref": "#/texts/118"}, {"$ref": "#/texts/119"}, {"$ref": "#/texts/120"}, {"$ref": "#/texts/121"}, {"$ref": "#/texts/122"}, {"$ref": "#/texts/123"}, {"$ref": "#/texts/124"}, {"$ref": "#/texts/125"}, {"$ref": "#/texts/126"}, {"$ref": "#/texts/127"}, {"$ref": "#/texts/128"}, {"$ref": "#/texts/129"}, {"$ref": "#/texts/130"}, {"$ref": "#/texts/131"}, {"$ref": "#/texts/132"}, {"$ref": "#/texts/133"}, {"$ref": "#/texts/134"}, {"$ref": "#/texts/135"}, {"$ref": "#/texts/136"}, {"$ref": "#/texts/137"}, {"$ref": "#/texts/138"}, {"$ref": "#/texts/139"}, {"$ref": "#/texts/140"}, {"$ref": "#/texts/141"}, {"$ref": "#/texts/142"}, {"$ref": "#/texts/143"}, {"$ref": "#/texts/144"}, {"$ref": "#/texts/145"}, {"$ref": "#/texts/146"}, {"$ref": "#/texts/147"}, {"$ref": "#/texts/148"}, {"$ref": "#/texts/149"}, {"$ref": "#/texts/150"}, {"$ref": "#/texts/151"}, {"$ref": "#/texts/152"}, {"$ref": "#/texts/153"}, {"$ref": "#/texts/154"}, {"$ref": "#/texts/155"}, {"$ref": "#/texts/156"}, {"$ref": "#/texts/157"}, {"$ref": "#/texts/158"}, {"$ref": "#/texts/159"}, {"$ref": "#/texts/160"}, {"$ref": "#/texts/161"}, {"$ref": "#/texts/162"}, {"$ref": "#/texts/163"}, {"$ref": "#/texts/164"}, {"$ref": "#/texts/165"}, {"$ref": "#/texts/166"}, {"$ref": "#/texts/167"}, {"$ref": "#/texts/168"}, {"$ref": "#/texts/169"}, {"$ref": "#/texts/170"}, {"$ref": "#/texts/171"}, {"$ref": "#/texts/172"}, {"$ref": "#/texts/173"}, {"$ref": "#/texts/174"}, {"$ref": "#/texts/175"}, {"$ref": "#/texts/176"}, {"$ref": "#/texts/177"}, {"$ref": "#/texts/178"}, {"$ref": "#/texts/179"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/30", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/180"}, {"$ref": "#/texts/181"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/31", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/32", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/182"}, {"$ref": "#/texts/183"}, {"$ref": "#/texts/184"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/33", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/187"}, {"$ref": "#/texts/188"}, {"$ref": "#/texts/189"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/34", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/191"}, {"$ref": "#/texts/192"}, {"$ref": "#/texts/193"}, {"$ref": "#/texts/194"}, {"$ref": "#/texts/195"}, {"$ref": "#/texts/196"}, {"$ref": "#/texts/197"}, {"$ref": "#/texts/198"}, {"$ref": "#/texts/199"}, {"$ref": "#/texts/200"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/35", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/202"}, {"$ref": "#/texts/203"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/36", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/205"}, {"$ref": "#/texts/206"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/37", "parent": {"$ref": "#/texts/269"}, "children": [{"$ref": "#/texts/270"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/38", "parent": {"$ref": "#/texts/269"}, "children": [{"$ref": "#/texts/271"}, {"$ref": "#/texts/272"}, {"$ref": "#/texts/273"}, {"$ref": "#/texts/274"}, {"$ref": "#/texts/275"}, {"$ref": "#/texts/276"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/39", "parent": {"$ref": "#/texts/278"}, "children": [{"$ref": "#/texts/279"}, {"$ref": "#/texts/280"}, {"$ref": "#/texts/281"}, {"$ref": "#/texts/282"}, {"$ref": "#/texts/283"}, {"$ref": "#/texts/284"}, {"$ref": "#/texts/285"}, {"$ref": "#/texts/286"}, {"$ref": "#/texts/287"}, {"$ref": "#/texts/288"}, {"$ref": "#/texts/289"}, {"$ref": "#/texts/290"}, {"$ref": "#/texts/291"}, {"$ref": "#/texts/292"}, {"$ref": "#/texts/293"}, {"$ref": "#/texts/294"}, {"$ref": "#/texts/295"}, {"$ref": "#/texts/296"}, {"$ref": "#/texts/297"}, {"$ref": "#/texts/298"}, {"$ref": "#/texts/299"}, {"$ref": "#/texts/300"}, {"$ref": "#/texts/301"}, {"$ref": "#/texts/302"}, {"$ref": "#/texts/303"}, {"$ref": "#/texts/304"}, {"$ref": "#/texts/305"}, {"$ref": "#/texts/306"}, {"$ref": "#/texts/307"}, {"$ref": "#/texts/308"}, {"$ref": "#/texts/309"}, {"$ref": "#/texts/310"}, {"$ref": "#/texts/311"}, {"$ref": "#/texts/312"}, {"$ref": "#/texts/313"}, {"$ref": "#/texts/314"}, {"$ref": "#/texts/315"}, {"$ref": "#/texts/316"}, {"$ref": "#/texts/317"}, {"$ref": "#/texts/318"}, {"$ref": "#/texts/319"}, {"$ref": "#/texts/320"}, {"$ref": "#/texts/321"}, {"$ref": "#/texts/322"}, {"$ref": "#/texts/323"}, {"$ref": "#/texts/324"}, {"$ref": "#/texts/325"}, {"$ref": "#/texts/326"}, {"$ref": "#/texts/327"}, {"$ref": "#/texts/328"}, {"$ref": "#/texts/329"}, {"$ref": "#/texts/330"}, {"$ref": "#/texts/331"}, {"$ref": "#/texts/332"}, {"$ref": "#/texts/333"}], "content_layer": "body", "name": "ordered list", "label": "list"}, {"self_ref": "#/groups/40", "parent": {"$ref": "#/texts/334"}, "children": [{"$ref": "#/texts/335"}, {"$ref": "#/texts/336"}, {"$ref": "#/texts/337"}, {"$ref": "#/texts/338"}, {"$ref": "#/texts/339"}, {"$ref": "#/texts/340"}, {"$ref": "#/texts/341"}, {"$ref": "#/texts/342"}, {"$ref": "#/texts/343"}, {"$ref": "#/texts/344"}, {"$ref": "#/texts/345"}, {"$ref": "#/texts/346"}, {"$ref": "#/texts/347"}, {"$ref": "#/texts/348"}, {"$ref": "#/texts/349"}, {"$ref": "#/texts/350"}, {"$ref": "#/texts/351"}, {"$ref": "#/texts/352"}, {"$ref": "#/texts/353"}, {"$ref": "#/texts/354"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/41", "parent": {"$ref": "#/texts/355"}, "children": [{"$ref": "#/texts/356"}, {"$ref": "#/texts/357"}, {"$ref": "#/texts/358"}, {"$ref": "#/texts/359"}, {"$ref": "#/texts/360"}, {"$ref": "#/texts/361"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/42", "parent": {"$ref": "#/texts/355"}, "children": [{"$ref": "#/texts/362"}, {"$ref": "#/texts/363"}, {"$ref": "#/texts/364"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/43", "parent": {"$ref": "#/texts/355"}, "children": [{"$ref": "#/texts/367"}, {"$ref": "#/texts/368"}, {"$ref": "#/texts/369"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/44", "parent": {"$ref": "#/texts/355"}, "children": [{"$ref": "#/texts/371"}, {"$ref": "#/texts/372"}, {"$ref": "#/texts/373"}, {"$ref": "#/texts/374"}, {"$ref": "#/texts/375"}, {"$ref": "#/texts/376"}, {"$ref": "#/texts/377"}, {"$ref": "#/texts/378"}, {"$ref": "#/texts/379"}, {"$ref": "#/texts/380"}, {"$ref": "#/texts/381"}, {"$ref": "#/texts/382"}, {"$ref": "#/texts/383"}, {"$ref": "#/texts/384"}, {"$ref": "#/texts/385"}, {"$ref": "#/texts/386"}, {"$ref": "#/texts/387"}, {"$ref": "#/texts/388"}, {"$ref": "#/texts/389"}, {"$ref": "#/texts/390"}, {"$ref": "#/texts/391"}, {"$ref": "#/texts/392"}, {"$ref": "#/texts/393"}, {"$ref": "#/texts/394"}, {"$ref": "#/texts/395"}, {"$ref": "#/texts/396"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/45", "parent": {"$ref": "#/texts/355"}, "children": [{"$ref": "#/texts/397"}, {"$ref": "#/texts/398"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/46", "parent": {"$ref": "#/texts/355"}, "children": [{"$ref": "#/texts/399"}, {"$ref": "#/texts/400"}, {"$ref": "#/texts/401"}, {"$ref": "#/texts/402"}, {"$ref": "#/texts/403"}, {"$ref": "#/texts/404"}, {"$ref": "#/texts/405"}, {"$ref": "#/texts/406"}, {"$ref": "#/texts/407"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/47", "parent": {"$ref": "#/texts/355"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/48", "parent": {"$ref": "#/texts/355"}, "children": [], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "text", "prov": [], "orig": "Main menu", "text": "Main menu"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "text", "prov": [], "orig": "Navigation", "text": "Navigation"}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Main page", "text": "Main page", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Contents", "text": "Contents", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Current events", "text": "Current events", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Random article", "text": "Random article", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "About Wikipedia", "text": "About Wikipedia", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Contact us", "text": "Contact us", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "text", "prov": [], "orig": "Contribute", "text": "Contribute"}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Help", "text": "Help", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Learn to edit", "text": "Learn to edit", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Community portal", "text": "Community portal", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Recent changes", "text": "Recent changes", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Upload file", "text": "Upload file", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Donate", "text": "Donate", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Create account", "text": "Create account", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Log in", "text": "Log in", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Create account", "text": "Create account", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Log in", "text": "Log in", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "text", "prov": [], "orig": "Pages for logged out editors", "text": "Pages for logged out editors"}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Contributions", "text": "Contributions", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "furniture", "label": "list_item", "prov": [], "orig": "Talk", "text": "Talk", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/groups/8"}, "children": [{"$ref": "#/groups/9"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Contents", "text": "Contents", "level": 1}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "(Top)", "text": "(Top)", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/groups/10"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "1 Etymology", "text": "1 Etymology", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/groups/11"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "2 Taxonomy", "text": "2 Taxonomy", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/groups/12"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "3 Morphology", "text": "3 Morphology", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/groups/13"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "4 Distribution and habitat", "text": "4 Distribution and habitat", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/groups/14"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "5 Behaviour Toggle Behaviour subsection", "text": "5 Behaviour Toggle Behaviour subsection", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/groups/14"}, "children": [{"$ref": "#/groups/15"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "5.1 Feeding", "text": "5.1 Feeding", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/groups/14"}, "children": [{"$ref": "#/groups/16"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "5.2 Breeding", "text": "5.2 Breeding", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/groups/14"}, "children": [{"$ref": "#/groups/17"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "5.3 Communication", "text": "5.3 Communication", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/groups/14"}, "children": [{"$ref": "#/groups/18"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "5.4 Predators", "text": "5.4 Predators", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/groups/19"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "6 Relationship with humans Toggle Relationship with humans subsection", "text": "6 Relationship with humans Toggle Relationship with humans subsection", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/groups/19"}, "children": [{"$ref": "#/groups/20"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "6.1 Hunting", "text": "6.1 Hunting", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/groups/19"}, "children": [{"$ref": "#/groups/21"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "6.2 Domestication", "text": "6.2 Domestication", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/groups/19"}, "children": [{"$ref": "#/groups/22"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "6.3 Heraldry", "text": "6.3 Heraldry", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/groups/19"}, "children": [{"$ref": "#/groups/23"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "6.4 Cultural references", "text": "6.4 Cultural references", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/groups/24"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "7 See also", "text": "7 See also", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/groups/25"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "8 Notes Toggle Notes subsection", "text": "8 Notes Toggle Notes subsection", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/groups/25"}, "children": [{"$ref": "#/groups/26"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "8.1 Citations", "text": "8.1 Citations", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/groups/25"}, "children": [{"$ref": "#/groups/27"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "8.2 Sources", "text": "8.2 Sources", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/groups/28"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "9 External links", "text": "9 External links", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/groups/29"}, {"$ref": "#/groups/30"}, {"$ref": "#/groups/31"}, {"$ref": "#/groups/32"}, {"$ref": "#/texts/185"}, {"$ref": "#/texts/186"}, {"$ref": "#/groups/33"}, {"$ref": "#/texts/190"}, {"$ref": "#/groups/34"}, {"$ref": "#/texts/201"}, {"$ref": "#/groups/35"}, {"$ref": "#/texts/204"}, {"$ref": "#/groups/36"}, {"$ref": "#/texts/207"}, {"$ref": "#/pictures/3"}, {"$ref": "#/texts/208"}, {"$ref": "#/texts/209"}, {"$ref": "#/texts/210"}, {"$ref": "#/texts/211"}, {"$ref": "#/tables/0"}, {"$ref": "#/texts/212"}, {"$ref": "#/texts/213"}, {"$ref": "#/texts/214"}, {"$ref": "#/texts/222"}, {"$ref": "#/texts/227"}, {"$ref": "#/texts/231"}, {"$ref": "#/texts/236"}, {"$ref": "#/texts/256"}, {"$ref": "#/texts/269"}, {"$ref": "#/texts/277"}, {"$ref": "#/texts/355"}], "content_layer": "body", "label": "title", "prov": [], "orig": "<PERSON>", "text": "<PERSON>"}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Afrikaans", "text": "Afrikaans", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Alemannisch", "text": "Alemannisch", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "አማርኛ", "text": "አማርኛ", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Ænglisc", "text": "Ænglisc", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "العربية", "text": "العربية", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Aragonés", "text": "Aragonés", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "ܐܪܡܝܐ", "text": "ܐܪܡܝܐ", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Atikamekw", "text": "Atikamekw", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "تۆرکجه", "text": "تۆرکجه", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Basa Bali", "text": "Basa Bali", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "বাংলা", "text": "বাংলা", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "閩南語 / Bân-lâm-gú", "text": "閩南語 / Bân-lâm-gú", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Беларуская", "text": "Беларуская", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Беларуская (тарашкевіца)", "text": "Беларуская (тарашкевіца)", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Bikol Central", "text": "Bikol Central", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Български", "text": "Български", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Català", "text": "Català", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/68", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Чӑвашла", "text": "Чӑвашла", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/69", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Čeština", "text": "Čeština", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/70", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/71", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/72", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/73", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Dansk", "text": "Dansk", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/74", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/75", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "De<PERSON>ch", "text": "De<PERSON>ch", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/76", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "डोटेली", "text": "डोटेली", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/77", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Ελληνικά", "text": "Ελληνικά", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/78", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Emiliàn e rumagnòl", "text": "Emiliàn e rumagnòl", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/79", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Español", "text": "Español", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/80", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Esperanto", "text": "Esperanto", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/81", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Euskara", "text": "Euskara", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/82", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "فار<PERSON>ی", "text": "فار<PERSON>ی", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/83", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Français", "text": "Français", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/84", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/85", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Galego", "text": "Galego", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/86", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "ГӀалгӀай", "text": "ГӀалгӀай", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/87", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "贛語", "text": "贛語", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/88", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "گیلکی", "text": "گیلکی", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/89", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "𐌲𐌿𐍄𐌹𐍃𐌺", "text": "𐌲𐌿𐍄𐌹𐍃𐌺", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/90", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "गोंयची कोंकणी / <PERSON><PERSON><PERSON>i <PERSON>i", "text": "गोंयची कोंकणी / <PERSON><PERSON><PERSON>i <PERSON>i", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/91", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "客家語 / Hak-kâ-ngî", "text": "客家語 / Hak-kâ-ngî", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/92", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "한국어", "text": "한국어", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/93", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Hausa", "text": "Hausa", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/94", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Հայերեն", "text": "Հայերեն", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/95", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "हिन्दी", "text": "हिन्दी", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/96", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Hrvatski", "text": "Hrvatski", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/97", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Ido", "text": "Ido", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/98", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Bahasa Indonesia", "text": "Bahasa Indonesia", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/99", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/100", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Íslenska", "text": "Íslenska", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/101", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Italiano", "text": "Italiano", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/102", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "עברית", "text": "עברית", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/103", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/104", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "ಕನ್ನಡ", "text": "ಕನ್ನಡ", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/105", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Kapampangan", "text": "Kapampangan", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/106", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "ქართული", "text": "ქართული", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/107", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "कॉशुर / کٲشُر", "text": "कॉशुर / کٲشُر", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/108", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Қазақша", "text": "Қазақша", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/109", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/110", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Kong<PERSON>", "text": "Kong<PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/111", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/112", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Кырык мары", "text": "Кырык мары", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/113", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "ລາວ", "text": "ລາວ", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/114", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Latina", "text": "Latina", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/115", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/116", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Lietuvių", "text": "Lietuvių", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/117", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON>", "text": "<PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/118", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Ligure", "text": "Ligure", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/119", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Limburgs", "text": "Limburgs", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/120", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/121", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Malagasy", "text": "Malagasy", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/122", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "മലയാളം", "text": "മലയാളം", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/123", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "मराठी", "text": "मराठी", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/124", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "مازِرونی", "text": "مازِرونی", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/125", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Bahasa Melayu", "text": "Bahasa Melayu", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/126", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "ꯃꯤꯇꯩ ꯂꯣꯟ", "text": "ꯃꯤꯇꯩ ꯂꯣꯟ", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/127", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "閩東語 / Mìng-dĕ̤ng-ngṳ̄", "text": "閩東語 / Mìng-dĕ̤ng-ngṳ̄", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/128", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Мокшень", "text": "Мокшень", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/129", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Монгол", "text": "Монгол", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/130", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "မြန်မာဘာသာ", "text": "မြန်မာဘာသာ", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/131", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Nederlands", "text": "Nederlands", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/132", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Nedersaksies", "text": "Nedersaksies", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/133", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "नेपाली", "text": "नेपाली", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/134", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "नेपाल भाषा", "text": "नेपाल भाषा", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/135", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "日本語", "text": "日本語", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/136", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Нох<PERSON><PERSON>н", "text": "Нох<PERSON><PERSON>н", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/137", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Norsk nynorsk", "text": "Norsk nynorsk", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/138", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Occitan", "text": "Occitan", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/139", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Oromoo", "text": "Oromoo", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/140", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "ਪੰਜਾਬੀ", "text": "ਪੰਜਾਬੀ", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/141", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Picard", "text": "Picard", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/142", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Plattdü<PERSON>tsch", "text": "Plattdü<PERSON>tsch", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/143", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/144", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Português", "text": "Português", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/145", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Qırımtatarca", "text": "Qırımtatarca", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/146", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Română", "text": "Română", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/147", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Русский", "text": "Русский", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/148", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Саха тыла", "text": "Саха тыла", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/149", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "ᱥᱟᱱᱛᱟᱲᱤ", "text": "ᱥᱟᱱᱛᱟᱲᱤ", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/150", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Sardu", "text": "Sardu", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/151", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Scots", "text": "Scots", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/152", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Seeltersk", "text": "Seeltersk", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/153", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Shqip", "text": "Shqip", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/154", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/155", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "සිංහල", "text": "සිංහල", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/156", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Simple English", "text": "Simple English", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/157", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "سنڌي", "text": "سنڌي", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/158", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "کوردی", "text": "کوردی", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/159", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Српски / srpski", "text": "Српски / srpski", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/160", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Srpskohrvatski / српскохрватски", "text": "Srpskohrvatski / српскохрватски", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/161", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Sunda", "text": "Sunda", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/162", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Svenska", "text": "Svenska", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/163", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Tagalog", "text": "Tagalog", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/164", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "தமிழ்", "text": "தமிழ்", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/165", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/166", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Татарча / tatarça", "text": "Татарча / tatarça", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/167", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "ไทย", "text": "ไทย", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/168", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Türkçe", "text": "Türkçe", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/169", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Українська", "text": "Українська", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/170", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "ئۇيغۇرچە / Uyghurche", "text": "ئۇيغۇرچە / Uyghurche", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/171", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Vahcuengh", "text": "Vahcuengh", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/172", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Tiếng <PERSON>", "text": "Tiếng <PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/173", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/174", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "文言", "text": "文言", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/175", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Winara<PERSON>", "text": "Winara<PERSON>", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/176", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "吴语", "text": "吴语", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/177", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "粵語", "text": "粵語", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/178", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Žemaitėška", "text": "Žemaitėška", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/179", "parent": {"$ref": "#/groups/29"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "中文", "text": "中文", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/180", "parent": {"$ref": "#/groups/30"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Article", "text": "Article", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/181", "parent": {"$ref": "#/groups/30"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Talk", "text": "Talk", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/182", "parent": {"$ref": "#/groups/32"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Read", "text": "Read", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/183", "parent": {"$ref": "#/groups/32"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "View source", "text": "View source", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/184", "parent": {"$ref": "#/groups/32"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "View history", "text": "View history", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/185", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Tools", "text": "Tools"}, {"self_ref": "#/texts/186", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Actions", "text": "Actions"}, {"self_ref": "#/texts/187", "parent": {"$ref": "#/groups/33"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Read", "text": "Read", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/188", "parent": {"$ref": "#/groups/33"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "View source", "text": "View source", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/189", "parent": {"$ref": "#/groups/33"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "View history", "text": "View history", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/190", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "General", "text": "General"}, {"self_ref": "#/texts/191", "parent": {"$ref": "#/groups/34"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "What links here", "text": "What links here", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/192", "parent": {"$ref": "#/groups/34"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Related changes", "text": "Related changes", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/193", "parent": {"$ref": "#/groups/34"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Upload file", "text": "Upload file", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/194", "parent": {"$ref": "#/groups/34"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Special pages", "text": "Special pages", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/195", "parent": {"$ref": "#/groups/34"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Permanent link", "text": "Permanent link", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/196", "parent": {"$ref": "#/groups/34"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Page information", "text": "Page information", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/197", "parent": {"$ref": "#/groups/34"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Cite this page", "text": "Cite this page", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/198", "parent": {"$ref": "#/groups/34"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Get shortened URL", "text": "Get shortened URL", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/199", "parent": {"$ref": "#/groups/34"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Download QR code", "text": "Download QR code", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/200", "parent": {"$ref": "#/groups/34"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Wikidata item", "text": "Wikidata item", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/201", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Print/export", "text": "Print/export"}, {"self_ref": "#/texts/202", "parent": {"$ref": "#/groups/35"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Download as PDF", "text": "Download as PDF", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/203", "parent": {"$ref": "#/groups/35"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Printable version", "text": "Printable version", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/204", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "In other projects", "text": "In other projects"}, {"self_ref": "#/texts/205", "parent": {"$ref": "#/groups/36"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Wikimedia Commons", "text": "Wikimedia Commons", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/206", "parent": {"$ref": "#/groups/36"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Wikiquote", "text": "Wikiquote", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/207", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Appearance", "text": "Appearance"}, {"self_ref": "#/texts/208", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "From Wikipedia, the free encyclopedia", "text": "From Wikipedia, the free encyclopedia"}, {"self_ref": "#/texts/209", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Common name for many species of bird", "text": "Common name for many species of bird"}, {"self_ref": "#/texts/210", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "This article is about the bird. For duck as a food, see . For other uses, see .", "text": "This article is about the bird. For duck as a food, see . For other uses, see ."}, {"self_ref": "#/texts/211", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "\"Duckling\" redirects here. For other uses, see .", "text": "\"Duckling\" redirects here. For other uses, see ."}, {"self_ref": "#/texts/212", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Duck is the common name for numerous species of waterfowl in the family Anatidae. Ducks are generally smaller and shorter-necked than swans and geese, which are members of the same family. Divided among several subfamilies, they are a form taxon; they do not represent a monophyletic group (the group of all descendants of a single common ancestral species), since swans and geese are not considered ducks. Ducks are mostly aquatic birds, and may be found in both fresh water and sea water.", "text": "Duck is the common name for numerous species of waterfowl in the family Anatidae. Ducks are generally smaller and shorter-necked than swans and geese, which are members of the same family. Divided among several subfamilies, they are a form taxon; they do not represent a monophyletic group (the group of all descendants of a single common ancestral species), since swans and geese are not considered ducks. Ducks are mostly aquatic birds, and may be found in both fresh water and sea water."}, {"self_ref": "#/texts/213", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Ducks are sometimes confused with several types of unrelated water birds with similar forms, such as loons or divers, grebes, gallinules and coots.", "text": "Ducks are sometimes confused with several types of unrelated water birds with similar forms, such as loons or divers, grebes, gallinules and coots."}, {"self_ref": "#/texts/214", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/215"}, {"$ref": "#/pictures/4"}, {"$ref": "#/texts/217"}, {"$ref": "#/texts/218"}, {"$ref": "#/texts/219"}, {"$ref": "#/pictures/5"}, {"$ref": "#/pictures/6"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Etymology", "text": "Etymology", "level": 1}, {"self_ref": "#/texts/215", "parent": {"$ref": "#/texts/214"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "The word duck comes from Old English dūce 'diver', a derivative of the verb *dūcan 'to duck, bend down low as if to get under something, or dive', because of the way many species in the dabbling duck group feed by upending; compare with Dutch duiken and German tauchen 'to dive'.", "text": "The word duck comes from Old English dūce 'diver', a derivative of the verb *dūcan 'to duck, bend down low as if to get under something, or dive', because of the way many species in the dabbling duck group feed by upending; compare with Dutch duiken and German tauchen 'to dive'."}, {"self_ref": "#/texts/216", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Pacific black duck displaying the characteristic upending \"duck\"", "text": "Pacific black duck displaying the characteristic upending \"duck\""}, {"self_ref": "#/texts/217", "parent": {"$ref": "#/texts/214"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "This word replaced Old English ened /ænid 'duck', possibly to avoid confusion with other words, such as ende 'end' with similar forms. Other Germanic languages still have similar words for duck, for example, Dutch eend, German Ente and Norwegian and. The word ened /ænid was inherited from Proto-Indo-European; cf. Latin anas \"duck\", Lithuanian ántis 'duck', Ancient Greek νῆσσα /νῆττα (nēssa /nētta) 'duck', and Sanskrit ātí 'water bird', among others.", "text": "This word replaced Old English ened /ænid 'duck', possibly to avoid confusion with other words, such as ende 'end' with similar forms. Other Germanic languages still have similar words for duck, for example, Dutch eend, German Ente and Norwegian and. The word ened /ænid was inherited from Proto-Indo-European; cf. Latin anas \"duck\", Lithuanian ántis 'duck', Ancient Greek νῆσσα /νῆττα (nēssa /nētta) 'duck', and Sanskrit ātí 'water bird', among others."}, {"self_ref": "#/texts/218", "parent": {"$ref": "#/texts/214"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "A duckling is a young duck in downy plumage[1] or baby duck,[2] but in the food trade a young domestic duck which has just reached adult size and bulk and its meat is still fully tender, is sometimes labelled as a duckling.", "text": "A duckling is a young duck in downy plumage[1] or baby duck,[2] but in the food trade a young domestic duck which has just reached adult size and bulk and its meat is still fully tender, is sometimes labelled as a duckling."}, {"self_ref": "#/texts/219", "parent": {"$ref": "#/texts/214"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "A male is called a drake and the female is called a duck, or in ornithology a hen.[3][4]", "text": "A male is called a drake and the female is called a duck, or in ornithology a hen.[3][4]"}, {"self_ref": "#/texts/220", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Male mallard.", "text": "Male mallard."}, {"self_ref": "#/texts/221", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Wood ducks.", "text": "Wood ducks."}, {"self_ref": "#/texts/222", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/223"}, {"$ref": "#/pictures/7"}, {"$ref": "#/texts/225"}, {"$ref": "#/texts/226"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Taxonomy", "text": "Taxonomy", "level": 1}, {"self_ref": "#/texts/223", "parent": {"$ref": "#/texts/222"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "All ducks belong to the biological order Anseriformes, a group that contains the ducks, geese and swans, as well as the screamers, and the magpie goose.[5] All except the screamers belong to the biological family Anatidae.[5] Within the family, ducks are split into a variety of subfamilies and 'tribes'. The number and composition of these subfamilies and tribes is the cause of considerable disagreement among taxonomists.[5] Some base their decisions on morphological characteristics, others on shared behaviours or genetic studies.[6][7] The number of suggested subfamilies containing ducks ranges from two to five.[8][9] The significant level of hybridisation that occurs among wild ducks complicates efforts to tease apart the relationships between various species.[9]", "text": "All ducks belong to the biological order Anseriformes, a group that contains the ducks, geese and swans, as well as the screamers, and the magpie goose.[5] All except the screamers belong to the biological family Anatidae.[5] Within the family, ducks are split into a variety of subfamilies and 'tribes'. The number and composition of these subfamilies and tribes is the cause of considerable disagreement among taxonomists.[5] Some base their decisions on morphological characteristics, others on shared behaviours or genetic studies.[6][7] The number of suggested subfamilies containing ducks ranges from two to five.[8][9] The significant level of hybridisation that occurs among wild ducks complicates efforts to tease apart the relationships between various species.[9]"}, {"self_ref": "#/texts/224", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Mallard landing in approach", "text": "Mallard landing in approach"}, {"self_ref": "#/texts/225", "parent": {"$ref": "#/texts/222"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "In most modern classifications, the so-called 'true ducks' belong to the subfamily Anatinae, which is further split into a varying number of tribes.[10] The largest of these, the Anatini, contains the 'dabbling' or 'river' ducks – named for their method of feeding primarily at the surface of fresh water.[11] The 'diving ducks', also named for their primary feeding method, make up the tribe Aythyini.[12] The 'sea ducks' of the tribe Mergini are diving ducks which specialise on fish and shellfish and spend a majority of their lives in saltwater.[13] The tribe Oxyurini contains the 'stifftails', diving ducks notable for their small size and stiff, upright tails.[14]", "text": "In most modern classifications, the so-called 'true ducks' belong to the subfamily Anatinae, which is further split into a varying number of tribes.[10] The largest of these, the Anatini, contains the 'dabbling' or 'river' ducks – named for their method of feeding primarily at the surface of fresh water.[11] The 'diving ducks', also named for their primary feeding method, make up the tribe Aythyini.[12] The 'sea ducks' of the tribe Mergini are diving ducks which specialise on fish and shellfish and spend a majority of their lives in saltwater.[13] The tribe Oxyurini contains the 'stifftails', diving ducks notable for their small size and stiff, upright tails.[14]"}, {"self_ref": "#/texts/226", "parent": {"$ref": "#/texts/222"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "A number of other species called ducks are not considered to be 'true ducks', and are typically placed in other subfamilies or tribes. The whistling ducks are assigned either to a tribe (Dendrocygnini) in the subfamily Anatinae or the subfamily Anserinae,[15] or to their own subfamily (Dendrocygninae) or family (Dendrocyganidae).[9][16] The freckled duck of Australia is either the sole member of the tribe Stictonettini in the subfamily Anserinae,[15] or in its own family, the Stictonettinae.[9] The shelducks make up the tribe Tadornini in the family Anserinae in some classifications,[15] and their own subfamily, Tadorninae, in others,[17] while the steamer ducks are either placed in the family Anserinae in the tribe Tachyerini[15] or lumped with the shelducks in the tribe Tadorini.[9] The perching ducks make up in the tribe Cairinini in the subfamily Anserinae in some classifications, while that tribe is eliminated in other classifications and its members assigned to the tribe Anatini.[9] The torrent duck is generally included in the subfamily Anserinae in the monotypic tribe Merganettini,[15] but is sometimes included in the tribe Tadornini.[18] The pink-eared duck is sometimes included as a true duck either in the tribe Anatini[15] or the tribe Malacorhynchini,[19] and other times is included with the shelducks in the tribe Tadornini.[15]", "text": "A number of other species called ducks are not considered to be 'true ducks', and are typically placed in other subfamilies or tribes. The whistling ducks are assigned either to a tribe (Dendrocygnini) in the subfamily Anatinae or the subfamily Anserinae,[15] or to their own subfamily (Dendrocygninae) or family (Dendrocyganidae).[9][16] The freckled duck of Australia is either the sole member of the tribe Stictonettini in the subfamily Anserinae,[15] or in its own family, the Stictonettinae.[9] The shelducks make up the tribe Tadornini in the family Anserinae in some classifications,[15] and their own subfamily, Tadorninae, in others,[17] while the steamer ducks are either placed in the family Anserinae in the tribe Tachyerini[15] or lumped with the shelducks in the tribe Tadorini.[9] The perching ducks make up in the tribe Cairinini in the subfamily Anserinae in some classifications, while that tribe is eliminated in other classifications and its members assigned to the tribe Anatini.[9] The torrent duck is generally included in the subfamily Anserinae in the monotypic tribe Merganettini,[15] but is sometimes included in the tribe Tadornini.[18] The pink-eared duck is sometimes included as a true duck either in the tribe Anatini[15] or the tribe Malacorhynchini,[19] and other times is included with the shelducks in the tribe Tadornini.[15]"}, {"self_ref": "#/texts/227", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/pictures/8"}, {"$ref": "#/texts/229"}, {"$ref": "#/texts/230"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Morphology", "text": "Morphology", "level": 1}, {"self_ref": "#/texts/228", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Male Mandarin duck", "text": "Male Mandarin duck"}, {"self_ref": "#/texts/229", "parent": {"$ref": "#/texts/227"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "The overall body plan of ducks is elongated and broad, and they are also relatively long-necked, albeit not as long-necked as the geese and swans. The body shape of diving ducks varies somewhat from this in being more rounded. The bill is usually broad and contains serrated pectens, which are particularly well defined in the filter-feeding species. In the case of some fishing species the bill is long and strongly serrated. The scaled legs are strong and well developed, and generally set far back on the body, more so in the highly aquatic species. The wings are very strong and are generally short and pointed, and the flight of ducks requires fast continuous strokes, requiring in turn strong wing muscles. Three species of steamer duck are almost flightless, however. Many species of duck are temporarily flightless while moulting; they seek out protected habitat with good food supplies during this period. This moult typically precedes migration.", "text": "The overall body plan of ducks is elongated and broad, and they are also relatively long-necked, albeit not as long-necked as the geese and swans. The body shape of diving ducks varies somewhat from this in being more rounded. The bill is usually broad and contains serrated pectens, which are particularly well defined in the filter-feeding species. In the case of some fishing species the bill is long and strongly serrated. The scaled legs are strong and well developed, and generally set far back on the body, more so in the highly aquatic species. The wings are very strong and are generally short and pointed, and the flight of ducks requires fast continuous strokes, requiring in turn strong wing muscles. Three species of steamer duck are almost flightless, however. Many species of duck are temporarily flightless while moulting; they seek out protected habitat with good food supplies during this period. This moult typically precedes migration."}, {"self_ref": "#/texts/230", "parent": {"$ref": "#/texts/227"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "The drakes of northern species often have extravagant plumage, but that is moulted in summer to give a more female-like appearance, the \"eclipse\" plumage. Southern resident species typically show less sexual dimorphism, although there are exceptions such as the paradise shelduck of New Zealand, which is both strikingly sexually dimorphic and in which the female's plumage is brighter than that of the male. The plumage of juvenile birds generally resembles that of the female. Female ducks have evolved to have a corkscrew shaped vagina to prevent rape.", "text": "The drakes of northern species often have extravagant plumage, but that is moulted in summer to give a more female-like appearance, the \"eclipse\" plumage. Southern resident species typically show less sexual dimorphism, although there are exceptions such as the paradise shelduck of New Zealand, which is both strikingly sexually dimorphic and in which the female's plumage is brighter than that of the male. The plumage of juvenile birds generally resembles that of the female. Female ducks have evolved to have a corkscrew shaped vagina to prevent rape."}, {"self_ref": "#/texts/231", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/pictures/9"}, {"$ref": "#/texts/233"}, {"$ref": "#/pictures/10"}, {"$ref": "#/texts/235"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Distribution and habitat", "text": "Distribution and habitat", "level": 1}, {"self_ref": "#/texts/232", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Flying steamer ducks in Ushuaia, Argentina", "text": "Flying steamer ducks in Ushuaia, Argentina"}, {"self_ref": "#/texts/233", "parent": {"$ref": "#/texts/231"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Ducks have a cosmopolitan distribution, and are found on every continent except Antarctica.[5] Several species manage to live on subantarctic islands, including South Georgia and the Auckland Islands.[20] Ducks have reached a number of isolated oceanic islands, including the Hawaiian Islands, Micronesia and the Galápagos Islands, where they are often vagrants and less often residents.[21][22] A handful are endemic to such far-flung islands.[21]", "text": "Ducks have a cosmopolitan distribution, and are found on every continent except Antarctica.[5] Several species manage to live on subantarctic islands, including South Georgia and the Auckland Islands.[20] Ducks have reached a number of isolated oceanic islands, including the Hawaiian Islands, Micronesia and the Galápagos Islands, where they are often vagrants and less often residents.[21][22] A handful are endemic to such far-flung islands.[21]"}, {"self_ref": "#/texts/234", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Female mallard in Cornwall, England", "text": "Female mallard in Cornwall, England"}, {"self_ref": "#/texts/235", "parent": {"$ref": "#/texts/231"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Some duck species, mainly those breeding in the temperate and Arctic Northern Hemisphere, are migratory; those in the tropics are generally not. Some ducks, particularly in Australia where rainfall is erratic, are nomadic, seeking out the temporary lakes and pools that form after localised heavy rain.[23]", "text": "Some duck species, mainly those breeding in the temperate and Arctic Northern Hemisphere, are migratory; those in the tropics are generally not. Some ducks, particularly in Australia where rainfall is erratic, are nomadic, seeking out the temporary lakes and pools that form after localised heavy rain.[23]"}, {"self_ref": "#/texts/236", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/237"}, {"$ref": "#/texts/246"}, {"$ref": "#/texts/249"}, {"$ref": "#/texts/252"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Behaviour", "text": "Behaviour", "level": 1}, {"self_ref": "#/texts/237", "parent": {"$ref": "#/texts/236"}, "children": [{"$ref": "#/pictures/11"}, {"$ref": "#/pictures/12"}, {"$ref": "#/texts/240"}, {"$ref": "#/texts/241"}, {"$ref": "#/texts/242"}, {"$ref": "#/texts/243"}, {"$ref": "#/texts/244"}, {"$ref": "#/texts/245"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Feeding", "text": "Feeding", "level": 2}, {"self_ref": "#/texts/238", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "<PERSON><PERSON><PERSON> along the bill", "text": "<PERSON><PERSON><PERSON> along the bill"}, {"self_ref": "#/texts/239", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Mallard duckling preening", "text": "Mallard duckling preening"}, {"self_ref": "#/texts/240", "parent": {"$ref": "#/texts/237"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Ducks eat food sources such as grasses, aquatic plants, fish, insects, small amphibians, worms, and small molluscs.", "text": "Ducks eat food sources such as grasses, aquatic plants, fish, insects, small amphibians, worms, and small molluscs."}, {"self_ref": "#/texts/241", "parent": {"$ref": "#/texts/237"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Dabbling ducks feed on the surface of water or on land, or as deep as they can reach by up-ending without completely submerging.[24] Along the edge of the bill, there is a comb-like structure called a pecten. This strains the water squirting from the side of the bill and traps any food. The pecten is also used to preen feathers and to hold slippery food items.", "text": "Dabbling ducks feed on the surface of water or on land, or as deep as they can reach by up-ending without completely submerging.[24] Along the edge of the bill, there is a comb-like structure called a pecten. This strains the water squirting from the side of the bill and traps any food. The pecten is also used to preen feathers and to hold slippery food items."}, {"self_ref": "#/texts/242", "parent": {"$ref": "#/texts/237"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Diving ducks and sea ducks forage deep underwater. To be able to submerge more easily, the diving ducks are heavier than dabbling ducks, and therefore have more difficulty taking off to fly.", "text": "Diving ducks and sea ducks forage deep underwater. To be able to submerge more easily, the diving ducks are heavier than dabbling ducks, and therefore have more difficulty taking off to fly."}, {"self_ref": "#/texts/243", "parent": {"$ref": "#/texts/237"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "A few specialized species such as the mergansers are adapted to catch and swallow large fish.", "text": "A few specialized species such as the mergansers are adapted to catch and swallow large fish."}, {"self_ref": "#/texts/244", "parent": {"$ref": "#/texts/237"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "The others have the characteristic wide flat bill adapted to dredging-type jobs such as pulling up waterweed, pulling worms and small molluscs out of mud, searching for insect larvae, and bulk jobs such as dredging out, holding, turning head first, and swallowing a squirming frog. To avoid injury when digging into sediment it has no cere, but the nostrils come out through hard horn.", "text": "The others have the characteristic wide flat bill adapted to dredging-type jobs such as pulling up waterweed, pulling worms and small molluscs out of mud, searching for insect larvae, and bulk jobs such as dredging out, holding, turning head first, and swallowing a squirming frog. To avoid injury when digging into sediment it has no cere, but the nostrils come out through hard horn."}, {"self_ref": "#/texts/245", "parent": {"$ref": "#/texts/237"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "The Guardian published an article advising that ducks should not be fed with bread because it damages the health of the ducks and pollutes waterways.[25]", "text": "The Guardian published an article advising that ducks should not be fed with bread because it damages the health of the ducks and pollutes waterways.[25]"}, {"self_ref": "#/texts/246", "parent": {"$ref": "#/texts/236"}, "children": [{"$ref": "#/pictures/13"}, {"$ref": "#/texts/248"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Breeding", "text": "Breeding", "level": 2}, {"self_ref": "#/texts/247", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "A Muscovy duckling", "text": "A Muscovy duckling"}, {"self_ref": "#/texts/248", "parent": {"$ref": "#/texts/246"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Ducks generally only have one partner at a time, although the partnership usually only lasts one year.[26] Larger species and the more sedentary species (like fast-river specialists) tend to have pair-bonds that last numerous years.[27] Most duck species breed once a year, choosing to do so in favourable conditions (spring/summer or wet seasons). Ducks also tend to make a nest before breeding, and, after hatching, lead their ducklings to water. Mother ducks are very caring and protective of their young, but may abandon some of their ducklings if they are physically stuck in an area they cannot get out of (such as nesting in an enclosed courtyard) or are not prospering due to genetic defects or sickness brought about by hypothermia, starvation, or disease. Ducklings can also be orphaned by inconsistent late hatching where a few eggs hatch after the mother has abandoned the nest and led her ducklings to water.[28]", "text": "Ducks generally only have one partner at a time, although the partnership usually only lasts one year.[26] Larger species and the more sedentary species (like fast-river specialists) tend to have pair-bonds that last numerous years.[27] Most duck species breed once a year, choosing to do so in favourable conditions (spring/summer or wet seasons). Ducks also tend to make a nest before breeding, and, after hatching, lead their ducklings to water. Mother ducks are very caring and protective of their young, but may abandon some of their ducklings if they are physically stuck in an area they cannot get out of (such as nesting in an enclosed courtyard) or are not prospering due to genetic defects or sickness brought about by hypothermia, starvation, or disease. Ducklings can also be orphaned by inconsistent late hatching where a few eggs hatch after the mother has abandoned the nest and led her ducklings to water.[28]"}, {"self_ref": "#/texts/249", "parent": {"$ref": "#/texts/236"}, "children": [{"$ref": "#/texts/250"}, {"$ref": "#/texts/251"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Communication", "text": "Communication", "level": 2}, {"self_ref": "#/texts/250", "parent": {"$ref": "#/texts/249"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Female mallard ducks (as well as several other species in the genus Anas, such as the American and Pacific black ducks, spot-billed duck, northern pintail and common teal) make the classic \"quack\" sound while males make a similar but raspier sound that is sometimes written as \"breeeeze\",[29][self-published source?] but, despite widespread misconceptions, most species of duck do not \"quack\".[30] In general, ducks make a range of calls, including whistles, cooing, yodels and grunts. For example, the scaup – which are diving ducks – make a noise like \"scaup\" (hence their name). Calls may be loud displaying calls or quieter contact calls.", "text": "Female mallard ducks (as well as several other species in the genus Anas, such as the American and Pacific black ducks, spot-billed duck, northern pintail and common teal) make the classic \"quack\" sound while males make a similar but raspier sound that is sometimes written as \"breeeeze\",[29][self-published source?] but, despite widespread misconceptions, most species of duck do not \"quack\".[30] In general, ducks make a range of calls, including whistles, cooing, yodels and grunts. For example, the scaup – which are diving ducks – make a noise like \"scaup\" (hence their name). Calls may be loud displaying calls or quieter contact calls."}, {"self_ref": "#/texts/251", "parent": {"$ref": "#/texts/249"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "A common urban legend claims that duck quacks do not echo; however, this has been proven to be false. This myth was first debunked by the Acoustics Research Centre at the University of Salford in 2003 as part of the British Association's Festival of Science.[31] It was also debunked in one of the earlier episodes of the popular Discovery Channel television show MythBusters.[32]", "text": "A common urban legend claims that duck quacks do not echo; however, this has been proven to be false. This myth was first debunked by the Acoustics Research Centre at the University of Salford in 2003 as part of the British Association's Festival of Science.[31] It was also debunked in one of the earlier episodes of the popular Discovery Channel television show MythBusters.[32]"}, {"self_ref": "#/texts/252", "parent": {"$ref": "#/texts/236"}, "children": [{"$ref": "#/pictures/14"}, {"$ref": "#/texts/254"}, {"$ref": "#/texts/255"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Predators", "text": "Predators", "level": 2}, {"self_ref": "#/texts/253", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Ringed teal", "text": "Ringed teal"}, {"self_ref": "#/texts/254", "parent": {"$ref": "#/texts/252"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Ducks have many predators. Ducklings are particularly vulnerable, since their inability to fly makes them easy prey not only for predatory birds but also for large fish like pike, crocodilians, predatory testudines such as the alligator snapping turtle, and other aquatic hunters, including fish-eating birds such as herons. Ducks' nests are raided by land-based predators, and brooding females may be caught unaware on the nest by mammals, such as foxes, or large birds, such as hawks or owls.", "text": "Ducks have many predators. Ducklings are particularly vulnerable, since their inability to fly makes them easy prey not only for predatory birds but also for large fish like pike, crocodilians, predatory testudines such as the alligator snapping turtle, and other aquatic hunters, including fish-eating birds such as herons. Ducks' nests are raided by land-based predators, and brooding females may be caught unaware on the nest by mammals, such as foxes, or large birds, such as hawks or owls."}, {"self_ref": "#/texts/255", "parent": {"$ref": "#/texts/252"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Adult ducks are fast fliers, but may be caught on the water by large aquatic predators including big fish such as the North American muskie and the European pike. In flight, ducks are safe from all but a few predators such as humans and the peregrine falcon, which uses its speed and strength to catch ducks.", "text": "Adult ducks are fast fliers, but may be caught on the water by large aquatic predators including big fish such as the North American muskie and the European pike. In flight, ducks are safe from all but a few predators such as humans and the peregrine falcon, which uses its speed and strength to catch ducks."}, {"self_ref": "#/texts/256", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/257"}, {"$ref": "#/texts/260"}, {"$ref": "#/texts/263"}, {"$ref": "#/texts/266"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Relationship with humans", "text": "Relationship with humans", "level": 1}, {"self_ref": "#/texts/257", "parent": {"$ref": "#/texts/256"}, "children": [{"$ref": "#/texts/258"}, {"$ref": "#/texts/259"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Hunting", "text": "Hunting", "level": 2}, {"self_ref": "#/texts/258", "parent": {"$ref": "#/texts/257"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Humans have hunted ducks since prehistoric times. Excavations of middens in California dating to 7800 – 6400 BP have turned up bones of ducks, including at least one now-extinct flightless species.[33] Ducks were captured in \"significant numbers\" by Holocene inhabitants of the lower Ohio River valley, suggesting they took advantage of the seasonal bounty provided by migrating waterfowl.[34] Neolithic hunters in locations as far apart as the Caribbean,[35] Scandinavia,[36] Egypt,[37] Switzerland,[38] and China relied on ducks as a source of protein for some or all of the year.[39] Archeological evidence shows that Māori people in New Zealand hunted the flightless <PERSON><PERSON>'s duck, possibly to extinction, though rat predation may also have contributed to its fate.[40] A similar end awaited the Chatham duck, a species with reduced flying capabilities which went extinct shortly after its island was colonised by Polynesian settlers.[41] It is probable that duck eggs were gathered by Neolithic hunter-gathers as well, though hard evidence of this is uncommon.[35][42]", "text": "Humans have hunted ducks since prehistoric times. Excavations of middens in California dating to 7800 – 6400 BP have turned up bones of ducks, including at least one now-extinct flightless species.[33] Ducks were captured in \"significant numbers\" by Holocene inhabitants of the lower Ohio River valley, suggesting they took advantage of the seasonal bounty provided by migrating waterfowl.[34] Neolithic hunters in locations as far apart as the Caribbean,[35] Scandinavia,[36] Egypt,[37] Switzerland,[38] and China relied on ducks as a source of protein for some or all of the year.[39] Archeological evidence shows that Māori people in New Zealand hunted the flightless <PERSON><PERSON>'s duck, possibly to extinction, though rat predation may also have contributed to its fate.[40] A similar end awaited the Chatham duck, a species with reduced flying capabilities which went extinct shortly after its island was colonised by Polynesian settlers.[41] It is probable that duck eggs were gathered by Neolithic hunter-gathers as well, though hard evidence of this is uncommon.[35][42]"}, {"self_ref": "#/texts/259", "parent": {"$ref": "#/texts/257"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "In many areas, wild ducks (including ducks farmed and released into the wild) are hunted for food or sport,[43] by shooting, or by being trapped using duck decoys. Because an idle floating duck or a duck squatting on land cannot react to fly or move quickly, \"a sitting duck\" has come to mean \"an easy target\". These ducks may be contaminated by pollutants such as PCBs.[44]", "text": "In many areas, wild ducks (including ducks farmed and released into the wild) are hunted for food or sport,[43] by shooting, or by being trapped using duck decoys. Because an idle floating duck or a duck squatting on land cannot react to fly or move quickly, \"a sitting duck\" has come to mean \"an easy target\". These ducks may be contaminated by pollutants such as PCBs.[44]"}, {"self_ref": "#/texts/260", "parent": {"$ref": "#/texts/256"}, "children": [{"$ref": "#/pictures/15"}, {"$ref": "#/texts/262"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Domestication", "text": "Domestication", "level": 2}, {"self_ref": "#/texts/261", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Indian Runner ducks, a common breed of domestic ducks", "text": "Indian Runner ducks, a common breed of domestic ducks"}, {"self_ref": "#/texts/262", "parent": {"$ref": "#/texts/260"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Ducks have many economic uses, being farmed for their meat, eggs, and feathers (particularly their down). Approximately 3 billion ducks are slaughtered each year for meat worldwide.[45] They are also kept and bred by aviculturists and often displayed in zoos. Almost all the varieties of domestic ducks are descended from the mallard (Ana<PERSON> platyrhynchos), apart from the Muscovy duck (<PERSON><PERSON><PERSON> moschata).[46][47] The Call duck is another example of a domestic duck breed. Its name comes from its original use established by hunters, as a decoy to attract wild mallards from the sky, into traps set for them on the ground. The call duck is the world's smallest domestic duck breed, as it weighs less than 1 kg (2.2 lb).[48]", "text": "Ducks have many economic uses, being farmed for their meat, eggs, and feathers (particularly their down). Approximately 3 billion ducks are slaughtered each year for meat worldwide.[45] They are also kept and bred by aviculturists and often displayed in zoos. Almost all the varieties of domestic ducks are descended from the mallard (Ana<PERSON> platyrhynchos), apart from the Muscovy duck (<PERSON><PERSON><PERSON> moschata).[46][47] The Call duck is another example of a domestic duck breed. Its name comes from its original use established by hunters, as a decoy to attract wild mallards from the sky, into traps set for them on the ground. The call duck is the world's smallest domestic duck breed, as it weighs less than 1 kg (2.2 lb).[48]"}, {"self_ref": "#/texts/263", "parent": {"$ref": "#/texts/256"}, "children": [{"$ref": "#/pictures/16"}, {"$ref": "#/texts/265"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Heraldry", "text": "Heraldry", "level": 2}, {"self_ref": "#/texts/264", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [], "orig": "Three black-colored ducks in the coat of arms of Maaninka[49]", "text": "Three black-colored ducks in the coat of arms of Maaninka[49]"}, {"self_ref": "#/texts/265", "parent": {"$ref": "#/texts/263"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Ducks appear on several coats of arms, including the coat of arms of Lubāna (Latvia)[50] and the coat of arms of Föglö (Åland).[51]", "text": "Ducks appear on several coats of arms, including the coat of arms of Lubāna (Latvia)[50] and the coat of arms of Föglö (Åland).[51]"}, {"self_ref": "#/texts/266", "parent": {"$ref": "#/texts/256"}, "children": [{"$ref": "#/texts/267"}, {"$ref": "#/texts/268"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Cultural references", "text": "Cultural references", "level": 2}, {"self_ref": "#/texts/267", "parent": {"$ref": "#/texts/266"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "In 2002, psychologist <PERSON> and colleagues at the University of Hertfordshire, UK, finished a year-long LaughLab experiment, concluding that of all animals, ducks attract the most humor and silliness; he said, \"If you're going to tell a joke involving an animal, make it a duck.\"[52] The word \"duck\" may have become an inherently funny word in many languages, possibly because ducks are seen as silly in their looks or behavior. Of the many ducks in fiction, many are cartoon characters, such as Walt Disney's <PERSON>, and Warner Bros.' Daffy Duck. <PERSON> the Duck started as a comic book character in 1973[53][54] and was made into a movie in 1986.", "text": "In 2002, psychologist <PERSON> and colleagues at the University of Hertfordshire, UK, finished a year-long LaughLab experiment, concluding that of all animals, ducks attract the most humor and silliness; he said, \"If you're going to tell a joke involving an animal, make it a duck.\"[52] The word \"duck\" may have become an inherently funny word in many languages, possibly because ducks are seen as silly in their looks or behavior. Of the many ducks in fiction, many are cartoon characters, such as Walt Disney's <PERSON>, and Warner Bros.' Daffy Duck. <PERSON> the Duck started as a comic book character in 1973[53][54] and was made into a movie in 1986."}, {"self_ref": "#/texts/268", "parent": {"$ref": "#/texts/266"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "The 1992 Disney film The Mighty Ducks, starring <PERSON>, chose the duck as the mascot for the fictional youth hockey team who are protagonists of the movie, based on the duck being described as a fierce fighter. This led to the duck becoming the nickname and mascot for the eventual National Hockey League professional team of the Anaheim Ducks, who were founded with the name the Mighty Ducks of Anaheim.[citation needed] The duck is also the nickname of the University of Oregon sports teams as well as the Long Island Ducks minor league baseball team.[55]", "text": "The 1992 Disney film The Mighty Ducks, starring <PERSON>, chose the duck as the mascot for the fictional youth hockey team who are protagonists of the movie, based on the duck being described as a fierce fighter. This led to the duck becoming the nickname and mascot for the eventual National Hockey League professional team of the Anaheim Ducks, who were founded with the name the Mighty Ducks of Anaheim.[citation needed] The duck is also the nickname of the University of Oregon sports teams as well as the Long Island Ducks minor league baseball team.[55]"}, {"self_ref": "#/texts/269", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/groups/37"}, {"$ref": "#/groups/38"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "See also", "text": "See also", "level": 1}, {"self_ref": "#/texts/270", "parent": {"$ref": "#/groups/37"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Birds portal", "text": "Birds portal", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/271", "parent": {"$ref": "#/groups/38"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Domestic duck", "text": "Domestic duck", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/272", "parent": {"$ref": "#/groups/38"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Duck as food", "text": "Duck as food", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/273", "parent": {"$ref": "#/groups/38"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Duck test", "text": "Duck test", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/274", "parent": {"$ref": "#/groups/38"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Duck breeds", "text": "Duck breeds", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/275", "parent": {"$ref": "#/groups/38"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Fictional ducks", "text": "Fictional ducks", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/276", "parent": {"$ref": "#/groups/38"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Rubber duck", "text": "Rubber duck", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/277", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/texts/278"}, {"$ref": "#/texts/334"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Notes", "text": "Notes", "level": 1}, {"self_ref": "#/texts/278", "parent": {"$ref": "#/texts/277"}, "children": [{"$ref": "#/groups/39"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Citations", "text": "Citations", "level": 2}, {"self_ref": "#/texts/279", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"Duckling\". The American Heritage Dictionary of the English Language, Fourth Edition. Houghton Mifflin Company. 2006. Retrieved 2015-05-22.", "text": "^ \"Duckling\". The American Heritage Dictionary of the English Language, Fourth Edition. Houghton Mifflin Company. 2006. Retrieved 2015-05-22.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/280", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"Duckling\". Kernerman English Multilingual Dictionary (Beta Version). K. Dictionaries Ltd. 2000–2006. Retrieved 2015-05-22.", "text": "^ \"Duckling\". Kernerman English Multilingual Dictionary (Beta Version). K. Dictionaries Ltd. 2000–2006. Retrieved 2015-05-22.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/281", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON><PERSON>, <PERSON> (2001). The Encyclopedia of Historic and Endangered Livestock and Poultry Breeds. Yale University Press. ISBN 978-0300138139.", "text": "^ <PERSON><PERSON><PERSON>, <PERSON> (2001). The Encyclopedia of Historic and Endangered Livestock and Poultry Breeds. Yale University Press. ISBN 978-0300138139.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/282", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON><PERSON>, <PERSON><PERSON>; <PERSON><PERSON><PERSON>, <PERSON> (2003). How to Draw Cartoon Birds. The Rosen Publishing Group. ISBN 9780823961566.", "text": "^ <PERSON><PERSON><PERSON>, <PERSON><PERSON>; <PERSON><PERSON><PERSON>, <PERSON> (2003). How to Draw Cartoon Birds. The Rosen Publishing Group. ISBN 9780823961566.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/283", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ a b c d <PERSON><PERSON><PERSON> 1992, p. 536.", "text": "^ a b c d <PERSON><PERSON><PERSON> 1992, p. 536.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/284", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ Livezey 1986, pp. 737–738.", "text": "^ Livezey 1986, pp. 737–738.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/285", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON>, <PERSON><PERSON> & de K<PERSON> 1988, p. 452.", "text": "^ <PERSON><PERSON>, <PERSON><PERSON> & de K<PERSON> 1988, p. 452.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/286", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Lau<PERSON> & <PERSON>ä<PERSON> 2002, pp. 353–354.", "text": "^ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Lau<PERSON> & <PERSON>ä<PERSON> 2002, pp. 353–354.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/287", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ a b c d e f <PERSON> 1992, p. 540.", "text": "^ a b c d e f <PERSON> 1992, p. 540.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/288", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ Elphick, Dunning & Sibley 2001, p. 191.", "text": "^ Elphick, Dunning & Sibley 2001, p. 191.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/289", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON> 2005, p. 448.", "text": "^ <PERSON><PERSON> 2005, p. 448.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/290", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON> 2005, p. 622–623.", "text": "^ <PERSON><PERSON> 2005, p. 622–623.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/291", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON> 2005, p. 686.", "text": "^ <PERSON><PERSON> 2005, p. 686.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/292", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ Elphick, Dunning & Sibley 2001, p. 193.", "text": "^ Elphick, Dunning & Sibley 2001, p. 193.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/293", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ a b c d e f g Carbonera<PERSON> 1992, p. 537.", "text": "^ a b c d e f g Carbonera<PERSON> 1992, p. 537.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/294", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ American Ornithologists' Union 1998, p. xix.", "text": "^ American Ornithologists' Union 1998, p. xix.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/295", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ American Ornithologists' Union 1998.", "text": "^ American Ornithologists' Union 1998.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/296", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ Carbon<PERSON><PERSON> 1992, p. 538.", "text": "^ Carbon<PERSON><PERSON> 1992, p. 538.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/297", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ Christidis & Boles 2008, p. 62.", "text": "^ Christidis & Boles 2008, p. 62.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/298", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON><PERSON> 2008, pp. 239, 245.", "text": "^ <PERSON><PERSON><PERSON> 2008, pp. 239, 245.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/299", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ a b <PERSON>, <PERSON> & <PERSON> 1987, pp. 98–107.", "text": "^ a b <PERSON>, <PERSON> & <PERSON> 1987, pp. 98–107.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/300", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON>, Fi<PERSON> & Ho<PERSON> 2000, pp. 52–3.", "text": "^ <PERSON><PERSON>, Fi<PERSON> & Ho<PERSON> 2000, pp. 52–3.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/301", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"Pacific Black Duck\". www.wiresnr.org. Retrieved 2018-04-27.", "text": "^ \"Pacific Black Duck\". www.wiresnr.org. Retrieved 2018-04-27.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/302", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON>, <PERSON>. \"Dabbling Ducks\". CWE. Retrieved 2006-11-02.", "text": "^ <PERSON>, <PERSON>. \"Dabbling Ducks\". CWE. Retrieved 2006-11-02.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/303", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON> (16 March 2015). \"Don't feed the ducks bread, say conservationists\". The Guardian. Retrieved 13 November 2016.", "text": "^ <PERSON> (16 March 2015). \"Don't feed the ducks bread, say conservationists\". The Guardian. Retrieved 13 November 2016.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/304", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON> (1988). \"Female-Biased Philopatry, Monogamy, and the Timing of Pair Formation in Migratory Waterfowl\". Current Ornithology. pp. 187–221. doi:10.1007/978-1-4615-6787-5_4. ISBN 978-1-4615-6789-9.", "text": "^ <PERSON><PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON> (1988). \"Female-Biased Philopatry, Monogamy, and the Timing of Pair Formation in Migratory Waterfowl\". Current Ornithology. pp. 187–221. doi:10.1007/978-1-4615-6787-5_4. ISBN 978-1-4615-6789-9.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/305", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON><PERSON> (2000). \"Long-Term Pair Bonds in Harlequin Ducks\". The Condor. 102 (1): 201–205. doi:10.1093/condor/102.1.201. hdl:10315/13797.", "text": "^ <PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON><PERSON> (2000). \"Long-Term Pair Bonds in Harlequin Ducks\". The Condor. 102 (1): 201–205. doi:10.1093/condor/102.1.201. hdl:10315/13797.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/306", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"If You Find An Orphaned Duckling - Wildlife Rehabber\". wildliferehabber.com. Archived from the original on 2018-09-23. Retrieved 2018-12-22.", "text": "^ \"If You Find An Orphaned Duckling - Wildlife Rehabber\". wildliferehabber.com. Archived from the original on 2018-09-23. Retrieved 2018-12-22.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/307", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON>, <PERSON> (2011). The Duck Bible. Lulu.com. ISBN 9780557901562.[self-published source]", "text": "^ <PERSON>, <PERSON> (2011). The Duck Bible. Lulu.com. ISBN 9780557901562.[self-published source]", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/308", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2013-09-03). Bird Brains: Inside the Strange Minds of Our Fine Feathered Friends. Rowman & Littlefield. ISBN 9780762797707.", "text": "^ <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2013-09-03). Bird Brains: Inside the Strange Minds of Our Fine Feathered Friends. Rowman & Littlefield. ISBN 9780762797707.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/309", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON>, <PERSON> (2003-09-08). \"Sound science is quackers\". BBC News. Retrieved 2006-11-02.", "text": "^ <PERSON>, <PERSON> (2003-09-08). \"Sound science is quackers\". BBC News. Retrieved 2006-11-02.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/310", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"Mythbusters Episode 8\". 12 December 2003.", "text": "^ \"Mythbusters Episode 8\". 12 December 2003.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/311", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON><PERSON> 1994, p. 171.", "text": "^ <PERSON><PERSON><PERSON> 1994, p. 171.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/312", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON> 2008, pp. 168, 243.", "text": "^ <PERSON><PERSON> 2008, pp. 168, 243.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/313", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ a b <PERSON><PERSON><PERSON><PERSON><PERSON> 2003, p. 65.", "text": "^ a b <PERSON><PERSON><PERSON><PERSON><PERSON> 2003, p. 65.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/314", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON> 1996, p. 68.", "text": "^ <PERSON> 1996, p. 68.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/315", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON><PERSON> 1999, p. 42.", "text": "^ <PERSON><PERSON><PERSON> 1999, p. 42.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/316", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON> 1876, p. 133.", "text": "^ <PERSON><PERSON> 1876, p. 133.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/317", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON> 2012, p. 23.", "text": "^ <PERSON><PERSON> 2012, p. 23.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/318", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ Hume 2012, p. 53.", "text": "^ Hume 2012, p. 53.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/319", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ Hume 2012, p. 52.", "text": "^ Hume 2012, p. 52.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/320", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ Fieldhouse 2002, p. 167.", "text": "^ Fieldhouse 2002, p. 167.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/321", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ Livingston, A. D. (1998-01-01). Guide to Edible Plants and Animals. Wordsworth Editions, Limited. ISBN 9781853263774.", "text": "^ Livingston, A. D. (1998-01-01). Guide to Edible Plants and Animals. Wordsworth Editions, Limited. ISBN 9781853263774.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/322", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"Study plan for waterfowl injury assessment: Determining PCB concentrations in Hudson river resident waterfowl\" (PDF). New York State Department of Environmental Conservation. US Department of Commerce. December 2008. p. 3. Archived (PDF) from the original on 2022-10-09. Retrieved 2 July 2019.", "text": "^ \"Study plan for waterfowl injury assessment: Determining PCB concentrations in Hudson river resident waterfowl\" (PDF). New York State Department of Environmental Conservation. US Department of Commerce. December 2008. p. 3. Archived (PDF) from the original on 2022-10-09. Retrieved 2 July 2019.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/323", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"FAOSTAT\". www.fao.org. Retrieved 2019-10-25.", "text": "^ \"FAOSTAT\". www.fao.org. Retrieved 2019-10-25.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/324", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"<PERSON><PERSON>, <PERSON> Duck; <PERSON>giMorph Staff - The University of Texas at Austin\". Digimorph.org. Retrieved 2012-12-23.", "text": "^ \"<PERSON><PERSON>, <PERSON> Duck; <PERSON>giMorph Staff - The University of Texas at Austin\". Digimorph.org. Retrieved 2012-12-23.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/325", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON>. \"<PERSON><PERSON>; Encyclopædia Britannica\". Britannica.com. Retrieved 2012-12-23.", "text": "^ <PERSON><PERSON>. \"<PERSON><PERSON>; Encyclopædia Britannica\". Britannica.com. Retrieved 2012-12-23.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/326", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON>, <PERSON> (2014). Guinness World Records. Guinness World Records Limited. pp. 135. ISBN 978-1-908843-15-9.", "text": "^ <PERSON><PERSON>, <PERSON> (2014). Guinness World Records. Guinness World Records Limited. pp. 135. ISBN 978-1-908843-15-9.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/327", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON><PERSON> (in Finnish). Suomen Ku<PERSON>llis<PERSON>itto. 1982. p. 147. ISBN 951-773-085-3.", "text": "^ <PERSON><PERSON><PERSON> (in Finnish). Suomen Ku<PERSON>llis<PERSON>itto. 1982. p. 147. ISBN 951-773-085-3.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/328", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"Lubānas simbolika\" (in Latvian). Retrieved September 9, 2021.", "text": "^ \"Lubānas simbolika\" (in Latvian). Retrieved September 9, 2021.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/329", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"Föglö\" (in Swedish). Retrieved September 9, 2021.", "text": "^ \"Föglö\" (in Swedish). Retrieved September 9, 2021.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/330", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON>, <PERSON>. \"World's funniest joke revealed\". New Scientist. Retrieved 7 January 2019.", "text": "^ <PERSON>, <PERSON>. \"World's funniest joke revealed\". New Scientist. Retrieved 7 January 2019.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/331", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"<PERSON> (character)\". Grand Comics Database.", "text": "^ \"<PERSON> (character)\". Grand Comics Database.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/332", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ <PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON> (2008). \"1970s\". Marvel Chronicle A Year by Year History. London, United Kingdom: Dorling Kindersley. p. 161. ISBN 978-0756641238. December saw the debut of the cigar-smoking <PERSON> the Duck. In this story by writer <PERSON> and artist <PERSON>, various beings from different realities had begun turning up in the Man-Thing's Florida swamp, including this bad-tempered talking duck.", "text": "^ <PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON> (2008). \"1970s\". Marvel Chronicle A Year by Year History. London, United Kingdom: Dorling Kindersley. p. 161. ISBN 978-0756641238. December saw the debut of the cigar-smoking <PERSON> the Duck. In this story by writer <PERSON> and artist <PERSON>, various beings from different realities had begun turning up in the Man-Thing's Florida swamp, including this bad-tempered talking duck.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/333", "parent": {"$ref": "#/groups/39"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "^ \"The Duck\". University of Oregon Athletics. Retrieved 2022-01-20.", "text": "^ \"The Duck\". University of Oregon Athletics. Retrieved 2022-01-20.", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/334", "parent": {"$ref": "#/texts/277"}, "children": [{"$ref": "#/groups/40"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Sources", "text": "Sources", "level": 2}, {"self_ref": "#/texts/335", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "American Ornithologists' Union (1998). Checklist of North American Birds (PDF). Washington, DC: American Ornithologists' Union. ISBN 978-1-891276-00-2. Archived (PDF) from the original on 2022-10-09.", "text": "American Ornithologists' Union (1998). Checklist of North American Birds (PDF). Washington, DC: American Ornithologists' Union. ISBN 978-1-891276-00-2. Archived (PDF) from the original on 2022-10-09.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/336", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON>, <PERSON> (1992). <PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON> (eds.). Handbook of the Birds of the World. Vol. 1: Ostrich to Ducks. Barcelona: Lynx Edicions. ISBN 978-84-87334-10-8.", "text": "<PERSON><PERSON><PERSON>, <PERSON> (1992). <PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON> (eds.). Handbook of the Birds of the World. Vol. 1: Ostrich to Ducks. Barcelona: Lynx Edicions. ISBN 978-84-87334-10-8.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/337", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON>, eds. (2008). Systematics and Taxonomy of Australian Birds. Collingwood, VIC: Csiro Publishing. ISBN 978-0-643-06511-6.", "text": "<PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON>, eds. (2008). Systematics and Taxonomy of Australian Birds. Collingwood, VIC: Csiro Publishing. ISBN 978-0-643-06511-6.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/338", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON><PERSON><PERSON>, <PERSON> (July 2002). \"A molecular phylogeny of Anseriformes based on mitochondrial DNA analysis\". Molecular Phylogenetics and Evolution. 23 (3): 339–356. Bibcode:2002MolPE..23..339D. doi:10.1016/S1055-7903(02)00019-2. PMID 12099792.", "text": "<PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON><PERSON><PERSON>, <PERSON> (July 2002). \"A molecular phylogeny of Anseriformes based on mitochondrial DNA analysis\". Molecular Phylogenetics and Evolution. 23 (3): 339–356. Bibcode:2002MolPE..23..339D. doi:10.1016/S1055-7903(02)00019-2. PMID 12099792.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/339", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON>, <PERSON>; <PERSON><PERSON>, <PERSON>; <PERSON><PERSON>, <PERSON>, eds. (2001). The Sibley Guide to Bird Life and Behaviour. London: <PERSON>. ISBN 978-0-7136-6250-4.", "text": "<PERSON><PERSON><PERSON>, <PERSON>; <PERSON><PERSON>, <PERSON>; <PERSON><PERSON>, <PERSON>, eds. (2001). The Sibley Guide to Bird Life and Behaviour. London: <PERSON>. ISBN 978-0-7136-6250-4.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/340", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON>, <PERSON> (1994). Early Hunter-Gatherers of the California Coast. New York, NY: Springer Science & Business Media. ISBN 978-1-4419-3231-0.", "text": "<PERSON><PERSON><PERSON>, <PERSON> (1994). Early Hunter-Gatherers of the California Coast. New York, NY: Springer Science & Business Media. ISBN 978-1-4419-3231-0.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/341", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>, <PERSON> (2002). Food, Feasts, and Faith: An Encyclopedia of Food Culture in World Religions. Vol. I: A–K. Santa Barbara: ABC-CLIO. ISBN 978-1-61069-412-4.", "text": "<PERSON><PERSON>, <PERSON> (2002). Food, Feasts, and Faith: An Encyclopedia of Food Culture in World Religions. Vol. I: A–K. Santa Barbara: ABC-CLIO. ISBN 978-1-61069-412-4.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/342", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>, <PERSON>; <PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON> (2000). Wildlife of the Galápagos. Princeton, NJ: Princeton University Press. ISBN 978-0-691-10295-5.", "text": "<PERSON><PERSON>, <PERSON>; <PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON> (2000). Wildlife of the Galápagos. Princeton, NJ: Princeton University Press. ISBN 978-0-691-10295-5.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/343", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> (2012). How Food Made History. Chichester, UK: John Wiley & Sons. ISBN 978-1-4051-8947-7.", "text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> (2012). How Food Made History. Chichester, UK: John Wiley & Sons. ISBN 978-1-4051-8947-7.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/344", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON>, <PERSON> (2012). Extinct Birds. London: <PERSON>. ISBN 978-1-4729-3744-5.", "text": "<PERSON>, <PERSON> (2012). Extinct Birds. London: <PERSON>. ISBN 978-1-4729-3744-5.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/345", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>, <PERSON> (2008). Holocene Hunter-Gatherers of the Lower Ohio River Valley. Tuscaloosa: University of Alabama Press. ISBN 978-0-8173-1658-7.", "text": "<PERSON><PERSON>, <PERSON> (2008). Holocene Hunter-Gatherers of the Lower Ohio River Valley. Tuscaloosa: University of Alabama Press. ISBN 978-0-8173-1658-7.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/346", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>, <PERSON>, ed. (2005). Ducks, Geese and Swans: Species Accounts (Cairina to Mergus). Bird Families of the World. Oxford: Oxford University Press. ISBN 978-0-19-861009-0.", "text": "<PERSON><PERSON>, <PERSON>, ed. (2005). Ducks, Geese and Swans: Species Accounts (Cairina to Mergus). Bird Families of the World. Oxford: Oxford University Press. ISBN 978-0-19-861009-0.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/347", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON>, <PERSON> (October 1986). \"A phylogenetic analysis of recent Anseriform genera using morphological characters\" (PDF). The Auk. 103 (4): 737–754. doi:10.1093/auk/103.4.737. Archived (PDF) from the original on 2022-10-09.", "text": "<PERSON><PERSON><PERSON>, <PERSON> (October 1986). \"A phylogenetic analysis of recent Anseriform genera using morphological characters\" (PDF). The Auk. 103 (4): 737–754. doi:10.1093/auk/103.4.737. Archived (PDF) from the original on 2022-10-09.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/348", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON><PERSON> (July 1988). \"A partial classification of waterfowl (Anatidae) based on single-copy DNA\" (PDF). The Auk. 105 (3): 452–459. doi:10.1093/auk/105.3.452. Archived (PDF) from the original on 2022-10-09.", "text": "<PERSON><PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON><PERSON> (July 1988). \"A partial classification of waterfowl (Anatidae) based on single-copy DNA\" (PDF). The Auk. 105 (3): 452–459. doi:10.1093/auk/105.3.452. Archived (PDF) from the original on 2022-10-09.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/349", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON>, <PERSON> (1999). Early Civilizations of the Old World. London: Routledge. ISBN 978-0-415-10975-8.", "text": "<PERSON><PERSON><PERSON>, <PERSON> (1999). Early Civilizations of the Old World. London: Routledge. ISBN 978-0-415-10975-8.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/350", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON>, <PERSON><PERSON>; <PERSON><PERSON><PERSON>, <PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON> G<PERSON> (1987). A Field Guide to the Birds of Hawaii and the Tropical Pacific. Princeton, NJ: Princeton University Press. ISBN 0-691-02399-9.", "text": "<PERSON>, <PERSON><PERSON>; <PERSON><PERSON><PERSON>, <PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON> G<PERSON> (1987). A Field Guide to the Birds of Hawaii and the Tropical Pacific. Princeton, NJ: Princeton University Press. ISBN 0-691-02399-9.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/351", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON>, <PERSON> (1876). Early Man in Europe. New York: Harper & Brothers. LCCN 05040168.", "text": "<PERSON><PERSON>, <PERSON> (1876). Early Man in Europe. New York: Harper & Brothers. LCCN 05040168.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/352", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2008). A Complete Guide to Antarctic Wildlife. Princeton, NJ, US: Princeton University Press. ISBN 978-0-691-13666-0.", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2008). A Complete Guide to Antarctic Wildlife. Princeton, NJ, US: Princeton University Press. ISBN 978-0-691-13666-0.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/353", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2003). Autochthonous Societies. General History of the Caribbean. Paris: UNESCO. ISBN 978-92-3-103832-7.", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2003). Autochthonous Societies. General History of the Caribbean. Paris: UNESCO. ISBN 978-92-3-103832-7.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/354", "parent": {"$ref": "#/groups/40"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> (1996). The Origins of Agriculture in Europe. New York: Routledge. ISBN 978-0-415-08009-5.", "text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> (1996). The Origins of Agriculture in Europe. New York: Routledge. ISBN 978-0-415-08009-5.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/355", "parent": {"$ref": "#/texts/43"}, "children": [{"$ref": "#/groups/41"}, {"$ref": "#/groups/42"}, {"$ref": "#/tables/1"}, {"$ref": "#/pictures/17"}, {"$ref": "#/texts/365"}, {"$ref": "#/texts/366"}, {"$ref": "#/groups/43"}, {"$ref": "#/texts/370"}, {"$ref": "#/groups/44"}, {"$ref": "#/groups/45"}, {"$ref": "#/groups/46"}, {"$ref": "#/groups/47"}, {"$ref": "#/groups/48"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "External links", "text": "External links", "level": 1}, {"self_ref": "#/texts/356", "parent": {"$ref": "#/groups/41"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Definitions from Wiktionary", "text": "Definitions from Wiktionary", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/357", "parent": {"$ref": "#/groups/41"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Media from Commons", "text": "Media from Commons", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/358", "parent": {"$ref": "#/groups/41"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Quotations from Wikiquote", "text": "Quotations from Wikiquote", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/359", "parent": {"$ref": "#/groups/41"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Recipes from Wikibooks", "text": "Recipes from Wikibooks", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/360", "parent": {"$ref": "#/groups/41"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Taxa from Wikispecies", "text": "Taxa from Wikispecies", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/361", "parent": {"$ref": "#/groups/41"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Data from Wikidata", "text": "Data from Wikidata", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/362", "parent": {"$ref": "#/groups/42"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "list of books (useful looking abstracts)", "text": "list of books (useful looking abstracts)", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/363", "parent": {"$ref": "#/groups/42"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Ducks on postage stamps Archived 2013-05-13 at the Wayback Machine", "text": "Ducks on postage stamps Archived 2013-05-13 at the Wayback Machine", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/364", "parent": {"$ref": "#/groups/42"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Ducks at a Distance, by <PERSON> at Project Gutenberg - A modern illustrated guide to identification of US waterfowl", "text": "Ducks at a Distance, by <PERSON> at Project Gutenberg - A modern illustrated guide to identification of US waterfowl", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/365", "parent": {"$ref": "#/texts/355"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Retrieved from \"\"", "text": "Retrieved from \"\""}, {"self_ref": "#/texts/366", "parent": {"$ref": "#/texts/355"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": ":", "text": ":"}, {"self_ref": "#/texts/367", "parent": {"$ref": "#/groups/43"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Ducks", "text": "Ducks", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/368", "parent": {"$ref": "#/groups/43"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Game birds", "text": "Game birds", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/369", "parent": {"$ref": "#/groups/43"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Bird common names", "text": "Bird common names", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/370", "parent": {"$ref": "#/texts/355"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Hidden categories:", "text": "Hidden categories:"}, {"self_ref": "#/texts/371", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "All accuracy disputes", "text": "All accuracy disputes", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/372", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Accuracy disputes from February 2020", "text": "Accuracy disputes from February 2020", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/373", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "CS1 Finnish-language sources (fi)", "text": "CS1 Finnish-language sources (fi)", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/374", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "CS1 Latvian-language sources (lv)", "text": "CS1 Latvian-language sources (lv)", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/375", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "CS1 Swedish-language sources (sv)", "text": "CS1 Swedish-language sources (sv)", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/376", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles with short description", "text": "Articles with short description", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/377", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Short description is different from Wikidata", "text": "Short description is different from Wikidata", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/378", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Wikipedia indefinitely move-protected pages", "text": "Wikipedia indefinitely move-protected pages", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/379", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Wikipedia indefinitely semi-protected pages", "text": "Wikipedia indefinitely semi-protected pages", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/380", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles with 'species' microformats", "text": "Articles with 'species' microformats", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/381", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles containing Old English (ca. 450-1100)-language text", "text": "Articles containing Old English (ca. 450-1100)-language text", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/382", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles containing Dutch-language text", "text": "Articles containing Dutch-language text", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/383", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles containing German-language text", "text": "Articles containing German-language text", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/384", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles containing Norwegian-language text", "text": "Articles containing Norwegian-language text", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/385", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles containing Lithuanian-language text", "text": "Articles containing Lithuanian-language text", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/386", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles containing Ancient Greek (to 1453)-language text", "text": "Articles containing Ancient Greek (to 1453)-language text", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/387", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "All articles with self-published sources", "text": "All articles with self-published sources", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/388", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles with self-published sources from February 2020", "text": "Articles with self-published sources from February 2020", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/389", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "All articles with unsourced statements", "text": "All articles with unsourced statements", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/390", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles with unsourced statements from January 2022", "text": "Articles with unsourced statements from January 2022", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/391", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "CS1: long volume value", "text": "CS1: long volume value", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/392", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Pages using Sister project links with wikidata mismatch", "text": "Pages using Sister project links with wikidata mismatch", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/393", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Pages using Sister project links with hidden wikidata", "text": "Pages using Sister project links with hidden wikidata", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/394", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Webarchive template wayback links", "text": "Webarchive template wayback links", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/395", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles with Project Gutenberg links", "text": "Articles with Project Gutenberg links", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/396", "parent": {"$ref": "#/groups/44"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Articles containing video clips", "text": "Articles containing video clips", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/397", "parent": {"$ref": "#/groups/45"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "This page was last edited on 21 September 2024, at 12:11 (UTC).", "text": "This page was last edited on 21 September 2024, at 12:11 (UTC).", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/398", "parent": {"$ref": "#/groups/45"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Text is available under the Creative Commons Attribution-ShareAlike License 4.0;\nadditional terms may apply. By using this site, you agree to the Terms of Use and Privacy Policy. Wikipedia® is a registered trademark of the Wikimedia Foundation, Inc., a non-profit organization.", "text": "Text is available under the Creative Commons Attribution-ShareAlike License 4.0;\nadditional terms may apply. By using this site, you agree to the Terms of Use and Privacy Policy. Wikipedia® is a registered trademark of the Wikimedia Foundation, Inc., a non-profit organization.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/399", "parent": {"$ref": "#/groups/46"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Privacy policy", "text": "Privacy policy", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/400", "parent": {"$ref": "#/groups/46"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "About Wikipedia", "text": "About Wikipedia", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/401", "parent": {"$ref": "#/groups/46"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Disclaimers", "text": "Disclaimers", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/402", "parent": {"$ref": "#/groups/46"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Contact Wikipedia", "text": "Contact Wikipedia", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/403", "parent": {"$ref": "#/groups/46"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Code of Conduct", "text": "Code of Conduct", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/404", "parent": {"$ref": "#/groups/46"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Developers", "text": "Developers", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/405", "parent": {"$ref": "#/groups/46"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Statistics", "text": "Statistics", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/406", "parent": {"$ref": "#/groups/46"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Cookie statement", "text": "Cookie statement", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/407", "parent": {"$ref": "#/groups/46"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Mobile view", "text": "Mobile view", "enumerated": false, "marker": ""}], "pictures": [{"self_ref": "#/pictures/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "picture", "prov": [], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "picture", "prov": [], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "picture", "prov": [], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/3", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/4", "parent": {"$ref": "#/texts/214"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/216"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/5", "parent": {"$ref": "#/texts/214"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/220"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/6", "parent": {"$ref": "#/texts/214"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/221"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/7", "parent": {"$ref": "#/texts/222"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/224"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/8", "parent": {"$ref": "#/texts/227"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/228"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/9", "parent": {"$ref": "#/texts/231"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/232"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/10", "parent": {"$ref": "#/texts/231"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/234"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/11", "parent": {"$ref": "#/texts/237"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/238"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/12", "parent": {"$ref": "#/texts/237"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/239"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/13", "parent": {"$ref": "#/texts/246"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/247"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/14", "parent": {"$ref": "#/texts/252"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/253"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/15", "parent": {"$ref": "#/texts/260"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/261"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/16", "parent": {"$ref": "#/texts/263"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [{"$ref": "#/texts/264"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/17", "parent": {"$ref": "#/texts/355"}, "children": [], "content_layer": "body", "label": "picture", "prov": [], "captions": [], "references": [], "footnotes": [], "annotations": []}], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/texts/43"}, "children": [], "content_layer": "body", "label": "table", "prov": [], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "<PERSON>\n", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Bufflehead\n(Bucephala albeola)\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Scientific classification \n", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Domain:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Eukaryota\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Kingdom:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Animalia\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Phylum:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "<PERSON><PERSON><PERSON>\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Class:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Aves\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Order:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Anseriformes\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Superfamily:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Anatoidea\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Family:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Anatidae\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Subfamilies\n", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "\nSee text\n\n", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 13, "num_cols": 2, "grid": [[{"row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "<PERSON>\n", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "<PERSON>\n", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "\n", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Bufflehead\n(Bucephala albeola)\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Bufflehead\n(Bucephala albeola)\n", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 2, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Scientific classification \n", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Scientific classification \n", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Domain:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Eukaryota\n", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Kingdom:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Animalia\n", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Phylum:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "<PERSON><PERSON><PERSON>\n", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Class:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Aves\n", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Order:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Anseriformes\n", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Superfamily:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Anatoidea\n", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Family:\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Anatidae\n", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 2, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Subfamilies\n", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Subfamilies\n", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 2, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "\nSee text\n\n", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "\nSee text\n\n", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/texts/355"}, "children": [], "content_layer": "body", "label": "table", "prov": [], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Authority control databases ", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "National", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "United StatesFranceBnF dataJapanLatviaIsrael", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Other", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "IdRef", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 3, "num_cols": 2, "grid": [[{"row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Authority control databases ", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 2, "text": "Authority control databases ", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "National", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "United StatesFranceBnF dataJapanLatviaIsrael", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Other", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "IdRef", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {}}