#!/usr/bin/env python3
"""
Example showing how to use VLM (Vision Language Models) with <PERSON>ling to get text with bounding box coordinates.
This demonstrates two approaches:
1. Using DocTags format with SmolDocling for spatial information
2. Using force_backend_text to get precise coordinates from traditional OCR backend
"""

from pathlib import Path
from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import VlmPipelineOptions
from docling.datamodel.pipeline_options_vlm_model import (
    InlineVlmOptions, 
    InferenceFramework, 
    TransformersModelType,
    ResponseFormat
)
from docling.datamodel.accelerator_options import AcceleratorDevice
from docling.pipeline.vlm_pipeline import VlmPipeline
from docling.datamodel import vlm_model_specs


def create_smoldocling_with_coordinates():
    """
    Use SmolDocling model which outputs DocTags format with spatial information.
    DocTags format includes <loc_x><loc_y> tags that provide bounding box coordinates.
    """
    
    # SmolDocling is specifically trained to output DocTags with spatial information
    vlm_options = vlm_model_specs.SMOLDOCLING_MLX  # or SMOLDOCLING_TRANSFORMERS
    
    pipeline_options = VlmPipelineOptions(
        vlm_options=vlm_options,
        generate_page_images=True,
        force_backend_text=False,  # Use VLM-generated text with coordinates
    )
    
    return pipeline_options


def create_qwen_with_backend_coordinates():
    """
    Use Qwen 2.5 VL with DocTags format and force_backend_text=True.
    This combines VLM layout understanding with precise OCR coordinates.
    """
    
    # Custom Qwen configuration for DocTags output
    qwen_doctags_prompt = """
Convert this page to docling format using DocTags. 

DocTags format uses these tags with spatial coordinates:
- <text><loc_x1><loc_y1><loc_x2><loc_y2>text content</text>
- <section_header><loc_x1><loc_y1><loc_x2><loc_y2>header text</section_header>
- <table><loc_x1><loc_y1><loc_x2><loc_y2>table content</table>

The <loc_> tags specify bounding box coordinates (left, top, right, bottom).
Be precise with the spatial layout and include all text with accurate coordinates.
"""
    
    qwen_vlm_options = InlineVlmOptions(
        repo_id="Qwen/Qwen2.5-VL-3B-Instruct",
        prompt=qwen_doctags_prompt,
        response_format=ResponseFormat.DOCTAGS,  # Key: Use DOCTAGS format
        inference_framework=InferenceFramework.TRANSFORMERS,
        transformers_model_type=TransformersModelType.AUTOMODEL_VISION2SEQ,
        supported_devices=[
            AcceleratorDevice.CPU,
            AcceleratorDevice.CUDA,
            AcceleratorDevice.MPS,
        ],
        scale=2.0,
        temperature=0.0,
        max_new_tokens=4096,
        trust_remote_code=True,
        quantized=False,
        load_in_8bit=False,
    )
    
    pipeline_options = VlmPipelineOptions(
        vlm_options=qwen_vlm_options,
        generate_page_images=True,
        force_backend_text=True,  # Use backend OCR for precise coordinates
    )
    
    return pipeline_options


def extract_text_with_coordinates_vlm(image_path: str, method: str = "smoldocling"):
    """
    Extract text with coordinates using VLM approaches.
    
    Args:
        image_path: Path to the image file
        method: "smoldocling" or "qwen_backend"
    
    Returns:
        Extracted text and coordinate information
    """
    
    if method == "smoldocling":
        pipeline_options = create_smoldocling_with_coordinates()
        print("Using SmolDocling with native DocTags coordinates")
    elif method == "qwen_backend":
        pipeline_options = create_qwen_with_backend_coordinates()
        print("Using Qwen 2.5 VL with backend OCR coordinates")
    else:
        raise ValueError("Method must be 'smoldocling' or 'qwen_backend'")
    
    # Create document converter with VLM pipeline
    converter = DocumentConverter(
        format_options={
            InputFormat.IMAGE: PdfFormatOption(
                pipeline_cls=VlmPipeline,
                pipeline_options=pipeline_options,
            ),
            InputFormat.PDF: PdfFormatOption(
                pipeline_cls=VlmPipeline,
                pipeline_options=pipeline_options,
            ),
        }
    )
    
    print(f"Processing: {image_path}")
    print(f"Model: {pipeline_options.vlm_options.repo_id}")
    print(f"Response format: {pipeline_options.vlm_options.response_format}")
    print(f"Force backend text: {pipeline_options.force_backend_text}")
    
    # Convert the image
    result = converter.convert(image_path)
    
    # Extract text with coordinates
    text_with_coords = []
    
    print("\n=== Extracted Text with Coordinates ===")
    
    # Method 1: Access document items with provenance (bounding boxes)
    for item, level in result.document.iterate_items():
        if hasattr(item, 'text') and hasattr(item, 'prov') and item.prov:
            for prov in item.prov:
                if hasattr(prov, 'bbox') and prov.bbox:
                    text_info = {
                        'text': item.text,
                        'label': getattr(item, 'label', 'unknown'),
                        'bbox': {
                            'left': prov.bbox.l,
                            'top': prov.bbox.t,
                            'right': prov.bbox.r,
                            'bottom': prov.bbox.b,
                            'width': prov.bbox.width,
                            'height': prov.bbox.height
                        },
                        'page': getattr(prov, 'page_no', 1),
                        'confidence': 1.0,  # VLM doesn't provide confidence scores
                        'from_vlm': True
                    }
                    
                    text_with_coords.append(text_info)
                    
                    # Print in OCR-like format
                    print(f"Text: '{item.text}'")
                    print(f"  Label: {getattr(item, 'label', 'unknown')}")
                    print(f"  Bbox: ({prov.bbox.l:.1f}, {prov.bbox.t:.1f}, {prov.bbox.r:.1f}, {prov.bbox.b:.1f})")
                    print(f"  Page: {getattr(prov, 'page_no', 1)}")
                    print()
    
    # Method 2: If using DocTags format, also show raw DocTags output
    if pipeline_options.vlm_options.response_format == ResponseFormat.DOCTAGS:
        print("\n=== Raw DocTags Output (with spatial coordinates) ===")
        for page in result.pages:
            if page.predictions.vlm_response:
                doctags_text = page.predictions.vlm_response.text
                print(doctags_text[:1000] + "..." if len(doctags_text) > 1000 else doctags_text)
    
    # Export to different formats
    markdown_output = result.document.export_to_markdown()
    json_output = result.document.export_to_dict()
    
    return {
        'text_with_coords': text_with_coords,
        'markdown': markdown_output,
        'json': json_output,
        'document': result.document
    }


def compare_vlm_vs_traditional_ocr(image_path: str):
    """
    Compare VLM-based coordinate extraction with traditional OCR.
    """
    
    print("="*60)
    print("COMPARISON: VLM vs Traditional OCR")
    print("="*60)
    
    # Method 1: SmolDocling VLM with DocTags
    print("\n1. SmolDocling VLM (DocTags format)")
    print("-" * 40)
    try:
        vlm_result = extract_text_with_coordinates_vlm(image_path, method="smoldocling")
        print(f"VLM extracted {len(vlm_result['text_with_coords'])} text elements")
    except Exception as e:
        print(f"Error with SmolDocling VLM: {e}")
    
    # Method 2: Qwen VLM with backend coordinates
    print("\n2. Qwen 2.5 VL + Backend OCR")
    print("-" * 40)
    try:
        qwen_result = extract_text_with_coordinates_vlm(image_path, method="qwen_backend")
        print(f"Qwen+Backend extracted {len(qwen_result['text_with_coords'])} text elements")
    except Exception as e:
        print(f"Error with Qwen+Backend: {e}")
    
    # Method 3: Traditional OCR (for comparison)
    print("\n3. Traditional OCR (EasyOCR)")
    print("-" * 40)
    try:
        from docling_ocr_coordinates_example import extract_text_with_coordinates
        traditional_result = extract_text_with_coordinates(image_path)
        print(f"Traditional OCR extracted {len(traditional_result)} text elements")
    except Exception as e:
        print(f"Error with traditional OCR: {e}")


def main():
    """
    Example usage of VLM with coordinate extraction.
    """
    
    # Example image path - replace with your image
    image_path = "sample_multilingual_document.jpg"  # Change this to your image path
    
    if not Path(image_path).exists():
        print(f"Image not found: {image_path}")
        print("Please provide a valid image path.")
        print("\nSupported formats: JPG, JPEG, PNG, TIFF, BMP, WEBP, PDF")
        return
    
    print("VLM-based Text Extraction with Coordinates")
    print("=" * 50)
    
    # Example 1: SmolDocling with native DocTags coordinates
    print("\n=== Method 1: SmolDocling (Native Spatial Understanding) ===")
    try:
        result1 = extract_text_with_coordinates_vlm(image_path, method="smoldocling")
        print(f"✓ Extracted {len(result1['text_with_coords'])} text elements with coordinates")
        
        # Show sample of extracted text
        if result1['text_with_coords']:
            print("\nSample extracted text:")
            for i, item in enumerate(result1['text_with_coords'][:3]):
                print(f"{i+1}. '{item['text'][:50]}...' at {item['bbox']}")
        
    except Exception as e:
        print(f"✗ Error with SmolDocling: {e}")
    
    # Example 2: Qwen with backend OCR coordinates
    print("\n=== Method 2: Qwen 2.5 VL + Backend OCR ===")
    try:
        result2 = extract_text_with_coordinates_vlm(image_path, method="qwen_backend")
        print(f"✓ Extracted {len(result2['text_with_coords'])} text elements with coordinates")
        
        # Show sample of extracted text
        if result2['text_with_coords']:
            print("\nSample extracted text:")
            for i, item in enumerate(result2['text_with_coords'][:3]):
                print(f"{i+1}. '{item['text'][:50]}...' at {item['bbox']}")
        
    except Exception as e:
        print(f"✗ Error with Qwen+Backend: {e}")
    
    # Example 3: Full comparison
    print("\n=== Full Comparison ===")
    try:
        compare_vlm_vs_traditional_ocr(image_path)
    except Exception as e:
        print(f"Error in comparison: {e}")


if __name__ == "__main__":
    main()
