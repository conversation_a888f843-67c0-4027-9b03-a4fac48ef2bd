{"_name": "", "type": "pdf-document", "description": {"title": null, "abstract": null, "authors": null, "affiliations": null, "subjects": null, "keywords": null, "publication_date": null, "languages": null, "license": null, "publishers": null, "url_refs": null, "references": null, "publication": null, "reference_count": null, "citation_count": null, "citation_date": null, "advanced": null, "analytics": null, "logs": [], "collection": null, "acquisition": null}, "file-info": {"filename": "2206.01062.pdf", "filename-prov": null, "document-hash": "ea5bd3ba45359d9f21632f29ac48cd8d7931b4e3dce1595ac524a1e3e8f17c68", "#-pages": 9, "collection-name": null, "description": null, "page-hashes": [{"hash": "8953a93154d76e567fd12cbedc80fdd96acd7b95f8796fdd99e6323e9b5e62e5", "model": "default", "page": 1}, {"hash": "95fd7493687c826ad61870d95fe51c293e5ff2d0ced3852dccca2724152476ab", "model": "default", "page": 2}, {"hash": "eb5b7ec90656ea3cfa128b31b9432372311744f14c489749e696d6a2eab71cc2", "model": "default", "page": 3}, {"hash": "c21e9c23ddb16c953b61dc355143d0df64f633c9d3e9933811a01475c6361444", "model": "default", "page": 4}, {"hash": "8bdd7d75da6d0379991f2d1ec5d4593ecd41a6423d24b77d6d18f339b22c8fc2", "model": "default", "page": 5}, {"hash": "a32fa49cde50042ed0a0620f5015e210f5ef4c09508fb7a2d801ebeaa36418ba", "model": "default", "page": 6}, {"hash": "874e4b99a0c8e3ade493554d3d3dab9020e212a30b13906b54802e625fec32f8", "model": "default", "page": 7}, {"hash": "fc85d29ecb3220967463748596069586cfb6b5a9ee4196aa4a4a5c7da14cd9ca", "model": "default", "page": 8}, {"hash": "63f84ea4aeecf4daa62599747b3722a22426f99924ca5fef9424a1a7f9ba7be2", "model": "default", "page": 9}]}, "main-text": [{"prov": [{"bbox": [107.29999999999998, 672.4044199999998, 505.06195, 708.3052999999999], "page": 1, "span": [0, 71], "__ref_s3_data": null}], "text": "DocLayNet: A Large Human-Annotated Dataset for Document-Layout Analysis", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [90.96701, 611.7597, 193.73123, 658.32764], "page": 1, "span": [0, 73], "__ref_s3_data": null}], "text": "<PERSON><PERSON><PERSON>n IBM Research Rueschlikon, Switzerland <EMAIL>", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [255.11602999999997, 611.7597, 357.88025, 658.32764], "page": 1, "span": [0, 71], "__ref_s3_data": null}], "text": "<PERSON> IBM Research Rueschlikon, Switzerland <EMAIL>", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [419.26505, 611.7597, 522.0293, 658.32764], "page": 1, "span": [0, 70], "__ref_s3_data": null}], "text": "<PERSON> IBM Research Rueschlikon, Switzerland <EMAIL>", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [172.54303, 553.37469, 275.30725, 599.94263], "page": 1, "span": [0, 72], "__ref_s3_data": null}], "text": "<PERSON> IBM Research Rueschlikon, Switzerland <EMAIL>", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [336.69302, 553.37469, 439.45727999999997, 599.94263], "page": 1, "span": [0, 68], "__ref_s3_data": null}], "text": "<PERSON> IBM Research Rueschlikon, Switzerland <EMAIL>", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.798035, 533.98798, 111.94354, 544.29712], "page": 1, "span": [0, 8], "__ref_s3_data": null}], "text": "ABSTRACT", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [53.466999, 257.70682, 295.56018, 529.09546], "page": 1, "span": [0, 1595], "__ref_s3_data": null}], "text": "Accurate document layout analysis is a key requirement for highquality PDF document conversion. With the recent availability of public, large ground-truth datasets such as PubLayNet and DocBank, deep-learning models have proven to be very effective at layout detection and segmentation. While these datasets are of adequate size to train such models, they severely lack in layout variability since they are sourced from scientific article repositories such as PubMed and arXiv only. Consequently, the accuracy of the layout segmentation drops significantly when these models are applied on more challenging and diverse layouts. In this paper, we present DocLayNet , a new, publicly available, document-layout annotation dataset in COCO format. It contains 80863 manually annotated pages from diverse data sources to represent a wide variability in layouts. For each PDF page, the layout annotations provide labelled bounding-boxes with a choice of 11 distinct classes. DocLayNet also provides a subset of double- and triple-annotated pages to determine the inter-annotator agreement. In multiple experiments, we provide baseline accuracy scores (in mAP) for a set of popular object detection models. We also demonstrate that these models fall approximately 10% behind the inter-annotator agreement. Furthermore, we provide evidence that DocLayNet is of sufficient size. Lastly, we compare models trained on PubLayNet, DocBank and DocLayNet, showing that layout predictions of the DocLayNettrained models are more robust and thus the preferred choice for general-purpose document-layout analysis.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 230.69398, 134.81989, 241.**************], "page": 1, "span": [0, 12], "__ref_s3_data": null}], "text": "CCS CONCEPTS", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [53.797989, 195.**************, 297.85294, 225.91701], "page": 1, "span": [0, 170], "__ref_s3_data": null}], "text": "· Information systems → Document structure ; · Applied computing → Document analysis ; · Computing methodologies → Machine learning ; Computer vision ; Object detection ;", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 119.**************, 295.11798, 157.**************], "page": 1, "span": [0, 397], "__ref_s3_data": null}], "text": "Permission to make digital or hard copies of part or all of this work for personal or classroom use is granted without fee provided that copies are not made or distributed for profit or commercial advantage and that copies bear this notice and the full citation on the first page. Copyrights for third-party components of this work must be honored. For all other uses, contact the owner/author(s).", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 110.43413999999996, 197.86275, 116.91976999999997], "page": 1, "span": [0, 48], "__ref_s3_data": null}], "text": "KDD ’22, August 14-18, 2022, Washington, DC, USA", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.317001, 101.67411000000004, 186.74652, 108.18763999999999], "page": 1, "span": [0, 45], "__ref_s3_data": null}], "text": "© 2022 Copyright held by the owner/author(s).", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.554001, 93.70311000000004, 157.03125, 100.21663999999998], "page": 1, "span": [0, 33], "__ref_s3_data": null}], "text": "ACM ISBN 978-1-4503-9385-0/22/08.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 85.73310900000001, 166.94093, 92.24663499999997], "page": 1, "span": [0, 39], "__ref_s3_data": null}], "text": "https://doi.org/10.1145/3534678.3539043", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/0"}, {"prov": [{"bbox": [317.95499, 232.48476000000005, 559.80579, 251.91701], "page": 1, "span": [0, 84], "__ref_s3_data": null}], "text": "Figure 1: Four examples of complex page layouts across different document categories", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [317.95499, 189.22498999999993, 379.8205, 199.53409], "page": 1, "span": [0, 8], "__ref_s3_data": null}], "text": "KEYWORDS", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [317.95499, 164.99883, 559.18597, 184.33244000000002], "page": 1, "span": [0, 90], "__ref_s3_data": null}], "text": "PDF document conversion, layout segmentation, object-detection, data set, Machine Learning", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.65997, 144.41391, 404.65366, 151.94565999999998], "page": 1, "span": [0, 21], "__ref_s3_data": null}], "text": "ACM Reference Format:", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [317.95499, 84.622971, 559.5495, 141.88004], "page": 1, "span": [0, 374], "__ref_s3_data": null}], "text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. 2022. DocLayNet: A Large Human-Annotated Dataset for DocumentLayout Analysis. In Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD '22), August 14-18, 2022, Washington, DC, USA. ACM, New York, NY, USA, 9 pages. https://doi.org/10.1145/ 3534678.3539043", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 695.83099, 156.52899, 706.14014], "page": 2, "span": [0, 14], "__ref_s3_data": null}], "text": "1 INTRODUCTION", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [53.528999, 563.05286, 303.01697, 681.01648], "page": 2, "span": [0, 702], "__ref_s3_data": null}], "text": "Despite the substantial improvements achieved with machine-learning (ML) approaches and deep neural networks in recent years, document conversion remains a challenging problem, as demonstrated by the numerous public competitions held on this topic [1-4]. The challenge originates from the huge variability in PDF documents regarding layout, language and formats (scanned, programmatic or a combination of both). Engineering a single ML model that can be applied on all types of documents and provides high-quality layout segmentation remains to this day extremely challenging [5]. To highlight the variability in document layouts, we show a few example documents from the DocLayNet dataset in Figure 1.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.528999, 289.08084, 295.56412, 560.46844], "page": 2, "span": [0, 1580], "__ref_s3_data": null}], "text": "A key problem in the process of document conversion is to understand the structure of a single document page, i.e. which segments of text should be grouped together in a unit. To train models for this task, there are currently two large datasets available to the community, PubLayNet [6] and DocBank [7]. They were introduced in 2019 and 2020 respectively and significantly accelerated the implementation of layout detection and segmentation models due to their sizes of 300K and 500K ground-truth pages. These sizes were achieved by leveraging an automation approach. The benefit of automated ground-truth generation is obvious: one can generate large ground-truth datasets at virtually no cost. However, the automation introduces a constraint on the variability in the dataset, because corresponding structured source data must be available. PubLayNet and DocBank were both generated from scientific document repositories (PubMed and arXiv), which provide XML or L A T E X sources. Those scientific documents present a limited variability in their layouts, because they are typeset in uniform templates provided by the publishers. Obviously, documents such as technical manuals, annual company reports, legal text, government tenders, etc. have very different and partially unique layouts. As a consequence, the layout predictions obtained from models trained on PubLayNet or DocBank is very reasonable when applied on scientific documents. However, for more artistic or free-style layouts, we see sub-par prediction quality from these models, which we demonstrate in Section 5.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.591999, 212.**************, 295.56396, 286.49646], "page": 2, "span": [0, 462], "__ref_s3_data": null}], "text": "In this paper, we present the DocLayNet dataset. It provides pageby-page layout annotation ground-truth using bounding-boxes for 11 distinct class labels on 80863 unique document pages, of which a fraction carry double- or triple-annotations. DocLayNet is similar in spirit to PubLayNet and DocBank and will likewise be made available to the public 1 in order to stimulate the document-layout analysis community. It distinguishes itself in the following aspects:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [64.708, 177.**************, 295.56165, 207.**************], "page": 2, "span": [0, 145], "__ref_s3_data": null}], "text": "(1) Human Annotation : In contrast to PubLayNet and DocBank, we relied on human annotation instead of automation approaches to generate the data set.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [64.708, 155.**************, 294.26254, 174.**************], "page": 2, "span": [0, 105], "__ref_s3_data": null}], "text": "(2) Large Layout Variability : We include diverse and complex layouts from a large variety of public sources.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [64.708, 122.**************, 294.68381, 152.**************], "page": 2, "span": [0, 176], "__ref_s3_data": null}], "text": "(3) Detailed Label Set : We define 11 class labels to distinguish layout features in high detail. PubLayNet provides 5 labels; DocBank provides 13, although not a superset of ours.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [64.708, 100.**************, 295.56439, 119.**************], "page": 2, "span": [0, 111], "__ref_s3_data": null}], "text": "(4) Redundant Annotations : A fraction of the pages in the DocLayNet data set carry more than one human annotation.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [53.672001, 83.**************, 216.**************, 89.**************], "page": 2, "span": [0, 60], "__ref_s3_data": null}], "text": "$^{1}$https://developer.ibm.com/exchanges/data/all/doclaynet", "type": "footnote", "payload": null, "name": "Footnote", "font": null}, {"prov": [{"bbox": [342.095, 685.30286, 558.43201, 704.63647], "page": 2, "span": [0, 86], "__ref_s3_data": null}], "text": "This enables experimentation with annotation uncertainty and quality control analysis.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [328.86502, 630.50885, 559.72101, 682.71851], "page": 2, "span": [0, 276], "__ref_s3_data": null}], "text": "(5) Pre-defined Train-, Test- & Validation-set : Like DocBank, we provide fixed train-, test- & validation-sets to ensure proportional representation of the class-labels. Further, we prevent leakage of unique layouts across sets, which has a large effect on model accuracy scores.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [317.62299, 571.81384, 559.19031, 624.02448], "page": 2, "span": [0, 297], "__ref_s3_data": null}], "text": "All aspects outlined above are detailed in Section 3. In Section 4, we will elaborate on how we designed and executed this large-scale human annotation campaign. We will also share key insights and lessons learned that might prove helpful for other parties planning to set up annotation campaigns.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.73099, 484.14282, 559.58197, 569.22943], "page": 2, "span": [0, 506], "__ref_s3_data": null}], "text": "In Section 5, we will present baseline accuracy numbers for a variety of object detection methods (Faster R-CNN, Mask R-CNN and YOLOv5) trained on DocLayNet. We further show how the model performance is impacted by varying the DocLayNet dataset size, reducing the label set and modifying the train/test-split. Last but not least, we compare the performance of models trained on PubLayNet, DocBank and DocLayNet and demonstrate that a model trained on DocLayNet provides overall more robust layout recovery.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 460.48203, 421.74411, 470.79111], "page": 2, "span": [0, 14], "__ref_s3_data": null}], "text": "2 RELATED WORK", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [317.52499, 327.70383, 559.71613, 445.*************], "page": 2, "span": [0, 655], "__ref_s3_data": null}], "text": "While early approaches in document-layout analysis used rulebased algorithms and heuristics [8], the problem is lately addressed with deep learning methods. The most common approach is to leverage object detection models [9-15]. In the last decade, the accuracy and speed of these models has increased dramatically. Furthermore, most state-of-the-art object detection methods can be trained and applied with very little work, thanks to a standardisation effort of the ground-truth data format [16] and common deep-learning frameworks [17]. Reference data sets such as PubLayNet [6] and DocBank provide their data in the commonly accepted COCO format [16].", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 240.03183, 559.18646, 325.11948], "page": 2, "span": [0, 500], "__ref_s3_data": null}], "text": "Lately, new types of ML models for document-layout analysis have emerged in the community [18-21]. These models do not approach the problem of layout analysis purely based on an image representation of the page, as computer vision methods do. Instead, they combine the text tokens and image representation of a page in order to obtain a segmentation. While the reported accuracies appear to be promising, a broadly accepted data format which links geometric and textual features has yet to establish.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 216.**************, 477.45688, 226.**************], "page": 2, "span": [0, 23], "__ref_s3_data": null}], "text": "3 THE DOCLAYNET DATASET", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [317.95499, 116.46983, 559.7132, 201.55644000000007], "page": 2, "span": [0, 522], "__ref_s3_data": null}], "text": "DocLayNet contains 80863 PDF pages. Among these, 7059 carry two instances of human annotations, and 1591 carry three. This amounts to 91104 total annotation instances. The annotations provide layout information in the shape of labeled, rectangular boundingboxes. We define 11 distinct labels for layout features, namely Caption , Footnote , Formula , List-item , Page-footer , Page-header , Picture , Section-header , Table , Text , and Title . Our reasoning for picking this particular label set is detailed in Section 4.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 83.59282699999994, 558.2041, 113.88544000000002], "page": 2, "span": [0, 186], "__ref_s3_data": null}], "text": "In addition to open intellectual property constraints for the source documents, we required that the documents in DocLayNet adhere to a few conditions. Firstly, we kept scanned documents", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/1"}, {"prov": [{"bbox": [53.**************, 536.45276, 294.04373, 555.88501], "page": 3, "span": [0, 69], "__ref_s3_data": null}], "text": "Figure 2: Distribution of DocLayNet pages across document categories.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [53.**************, 425.10983, 294.27383, 510.1964699999999], "page": 3, "span": [0, 513], "__ref_s3_data": null}], "text": "to a minimum, since they introduce difficulties in annotation (see Section 4). As a second condition, we focussed on medium to large documents ( > 10 pages) with technical content, dense in complex tables, figures, plots and captions. Such documents carry a lot of information value, but are often hard to analyse with high accuracy due to their challenging layouts. Counterexamples of documents not included in the dataset are receipts, invoices, hand-written documents or photographs showing \"text in the wild\".", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.57400100000001, 282.64383, 295.56046, 422.52545], "page": 3, "span": [0, 810], "__ref_s3_data": null}], "text": "The pages in DocLayNet can be grouped into six distinct categories, namely Financial Reports , Manuals , Scientific Articles , Laws & Regulations , Patents and Government Tenders . Each document category was sourced from various repositories. For example, Financial Reports contain both free-style format annual reports 2 which expose company-specific, artistic layouts as well as the more formal SEC filings. The two largest categories ( Financial Reports and Manuals ) contain a large amount of free-style layouts in order to obtain maximum variability. In the other four categories, we boosted the variability by mixing documents from independent providers, such as different government websites or publishers. In Figure 2, we show the document categories contained in DocLayNet with their respective sizes.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.466999, 184.01382, 295.56155, 280.05945], "page": 3, "span": [0, 535], "__ref_s3_data": null}], "text": "We did not control the document selection with regard to language. The vast majority of documents contained in DocLayNet (close to 95%) are published in English language. However, DocLayNet also contains a number of documents in other languages such as German (2.5%), French (1.0%) and Japanese (1.0%). While the document language has negligible impact on the performance of computer vision methods such as object detection and segmentation models, it might prove challenging for layout analysis methods which exploit textual features.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 107.30183, 295.56396, 181.42944], "page": 3, "span": [0, 413], "__ref_s3_data": null}], "text": "To ensure that future benchmarks in the document-layout analysis community can be easily compared, we have split up DocLayNet into pre-defined train-, test- and validation-sets. In this way, we can avoid spurious variations in the evaluation scores due to random splitting in train-, test- and validation-sets. We also ensured that less frequent labels are represented in train and test sets in equal proportions.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 83.830109, 195.78998, 90.34363600000006], "page": 3, "span": [0, 51], "__ref_s3_data": null}], "text": "$^{2}$e.g. AAPL from https://www.annualreports.com/", "type": "footnote", "payload": null, "name": "Footnote", "font": null}, {"prov": [{"bbox": [317.62299, 630.50885, 559.19183, 704.63647], "page": 3, "span": [0, 435], "__ref_s3_data": null}], "text": "Table 1 shows the overall frequency and distribution of the labels among the different sets. Importantly, we ensure that subsets are only split on full-document boundaries. This avoids that pages of the same document are spread over train, test and validation set, which can give an undesired evaluation advantage to models and lead to overestimation of their prediction accuracy. We will show the impact of this decision in Section 5.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 520.9198, 558.43811, 627.92444], "page": 3, "span": [0, 645], "__ref_s3_data": null}], "text": "In order to accommodate the different types of models currently in use by the community, we provide DocLayNet in an augmented COCO format [16]. This entails the standard COCO ground-truth file (in JSON format) with the associated page images (in PNG format, 1025 × 1025 pixels). Furthermore, custom fields have been added to each COCO record to specify document category, original document filename and page number. In addition, we also provide the original PDF pages, as well as sidecar files containing parsed PDF text and text-cell coordinates (in JSON). All additional files are linked to the primary page images by their matching filenames.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [316.94199, 203.11082, 559.72156, 518.33545], "page": 3, "span": [0, 1854], "__ref_s3_data": null}], "text": "Despite being cost-intense and far less scalable than automation, human annotation has several benefits over automated groundtruth generation. The first and most obvious reason to leverage human annotations is the freedom to annotate any type of document without requiring a programmatic source. For most PDF documents, the original source document is not available. The latter is not a hard constraint with human annotation, but it is for automated methods. A second reason to use human annotations is that the latter usually provide a more natural interpretation of the page layout. The human-interpreted layout can significantly deviate from the programmatic layout used in typesetting. For example, \"invisible\" tables might be used solely for aligning text paragraphs on columns. Such typesetting tricks might be interpreted by automated methods incorrectly as an actual table, while the human annotation will interpret it correctly as Text or other styles. The same applies to multi-line text elements, when authors decided to space them as \"invisible\" list elements without bullet symbols. A third reason to gather ground-truth through human annotation is to estimate a \"natural\" upper bound on the segmentation accuracy. As we will show in Section 4, certain documents featuring complex layouts can have different but equally acceptable layout interpretations. This natural upper bound for segmentation accuracy can be found by annotating the same pages multiple times by different people and evaluating the inter-annotator agreement. Such a baseline consistency evaluation is very useful to define expectations for a good target accuracy in trained deep neural network models and avoid overfitting (see Table 1). On the flip side, achieving high annotation consistency proved to be a key challenge in human annotation, as we outline in Section 4.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 174.84099000000003, 470.21326, 185.15008999999998], "page": 3, "span": [0, 21], "__ref_s3_data": null}], "text": "4 ANNOTATION CAMPAIGN", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [317.686, 85.897827, 559.71381, 160.02643999999998], "page": 3, "span": [0, 457], "__ref_s3_data": null}], "text": "The annotation campaign was carried out in four phases. In phase one, we identified and prepared the data sources for annotation. In phase two, we determined the class labels and how annotations should be done on the documents in order to obtain maximum consistency. The latter was guided by a detailed requirement analysis and exhaustive experiments. In phase three, we trained the annotation staff and performed exams for quality assurance. In phase four,", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/0"}, {"prov": [{"bbox": [53.501999, 676.65381, 558.48969, 707.0450400000001], "page": 4, "span": [0, 348], "__ref_s3_data": null}], "text": "Table 1: DocLayNet dataset overview. Along with the frequency of each class label, we present the relative occurrence (as % of row \"Total\") in the train, test and validation sets. The inter-annotator agreement is computed as the mAP@0.5-0.95 metric between pairwise annotations from the triple-annotated pages, from which we obtain accuracy ranges.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/2"}, {"prov": [{"bbox": [53.**************, 185.68075999999996, 295.64874, 237.99000999999998], "page": 4, "span": [0, 281], "__ref_s3_data": null}], "text": "Figure 3: Corpus Conversion Service annotation user interface. The PDF page is shown in the background, with overlaid text-cells (in darker shades). The annotation boxes can be drawn by dragging a rectangle over each segment with the respective label from the palette on the right.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [53.466999, 116.45682999999997, 294.04745, 157.70844999999997], "page": 4, "span": [0, 231], "__ref_s3_data": null}], "text": "we distributed the annotation workload and performed continuous quality controls. Phase one and two required a small team of experts only. For phases three and four, a group of 40 dedicated annotators were assembled and supervised.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 83.57982600000003, 295.55844, 113.98901000000001], "page": 4, "span": [0, 570], "__ref_s3_data": null}, {"bbox": [317.95499, 416.75183, 559.18536, 479.92047], "page": 4, "span": [0, 570], "__ref_s3_data": null}], "text": "Phase 1: Data selection and preparation. Our inclusion criteria for documents were described in Section 3. A large effort went into ensuring that all documents are free to use. The data sources include publication repositories such as arXiv$^{3}$, government offices, company websites as well as data directory services for financial reports and patents. Scanned documents were excluded wherever possible because they can be rotated or skewed. This would not allow us to perform annotation with rectangular bounding-boxes and therefore complicate the annotation process.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 285.24484, 559.71307, 414.1674499999999], "page": 4, "span": [0, 746], "__ref_s3_data": null}], "text": "Preparation work included uploading and parsing the sourced PDF documents in the Corpus Conversion Service (CCS) [22], a cloud-native platform which provides a visual annotation interface and allows for dataset inspection and analysis. The annotation interface of CCS is shown in Figure 3. The desired balance of pages between the different document categories was achieved by selective subsampling of pages with certain desired properties. For example, we made sure to include the title page of each document and bias the remaining page selection to those with figures or tables. The latter was achieved by leveraging pre-trained object detection models from PubLayNet, which helped us estimate how many figures and tables a given page contains.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.62299, 98.94382500000006, 559.71765, 282.77701], "page": 4, "span": [0, 1159], "__ref_s3_data": null}], "text": "Phase 2: Label selection and guideline. We reviewed the collected documents and identified the most common structural features they exhibit. This was achieved by identifying recurrent layout elements and lead us to the definition of 11 distinct class labels. These 11 class labels are Caption , Footnote , Formula , List-item , Pagefooter , Page-header , Picture , Section-header , Table , Text , and Title . Critical factors that were considered for the choice of these class labels were (1) the overall occurrence of the label, (2) the specificity of the label, (3) recognisability on a single page (i.e. no need for context from previous or next page) and (4) overall coverage of the page. Specificity ensures that the choice of label is not ambiguous, while coverage ensures that all meaningful items on a page can be annotated. We refrained from class labels that are very specific to a document category, such as Abstract in the Scientific Articles category. We also avoided class labels that are tightly linked to the semantics of the text. Labels such as Author and Affiliation , as seen in DocBank, are often only distinguishable by discriminating on", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 83.**************, 369.2457, 89.**************], "page": 4, "span": [0, 24], "__ref_s3_data": null}], "text": "$^{3}$https://arxiv.org/", "type": "footnote", "payload": null, "name": "Footnote", "font": null}, {"prov": [{"bbox": [53.**************, 685.29388, 294.04541, 704.63647], "page": 5, "span": [0, 135], "__ref_s3_data": null}], "text": "the textual content of an element, which goes beyond visual layout recognition, in particular outside the Scientific Articles category.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 542.83783, 295.55923, 682.71844], "page": 5, "span": [0, 812], "__ref_s3_data": null}], "text": "At first sight, the task of visual document-layout interpretation appears intuitive enough to obtain plausible annotations in most cases. However, during early trial-runs in the core team, we observed many cases in which annotators use different annotation styles, especially for documents with challenging layouts. For example, if a figure is presented with subfigures, one annotator might draw a single figure bounding-box, while another might annotate each subfigure separately. The same applies for lists, where one might annotate all list items in one block or each list item separately. In essence, we observed that challenging layouts would be annotated in different but plausible ways. To illustrate this, we show in Figure 4 multiple examples of plausible but inconsistent annotations on the same pages.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 455.16583, 295.56006, 540.25348], "page": 5, "span": [0, 465], "__ref_s3_data": null}], "text": "Obviously, this inconsistency in annotations is not desirable for datasets which are intended to be used for model training. To minimise these inconsistencies, we created a detailed annotation guideline. While perfect consistency across 40 annotation staff members is clearly not possible to achieve, we saw a huge improvement in annotation consistency after the introduction of our annotation guideline. A few selected, non-trivial highlights of the guideline are:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [64.708, 402.22687, 294.0462, 443.48746], "page": 5, "span": [0, 198], "__ref_s3_data": null}], "text": "(1) Every list-item is an individual object instance with class label List-item . This definition is different from PubLayNet and DocBank, where all list-items are grouped together into one List object.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [64.707993, 358.39984, 295.56372, 399.65149], "page": 5, "span": [0, 204], "__ref_s3_data": null}], "text": "(2) A List-item is a paragraph with hanging indentation. Singleline elements can qualify as List-item if the neighbour elements expose hanging indentation. Bullet or enumeration symbols are not a requirement.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [64.708, 336.47287, 294.04724, 355.**************], "page": 5, "span": [0, 78], "__ref_s3_data": null}], "text": "(3) For every Caption , there must be exactly one corresponding Picture or Table .", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [64.708, 314.56482, 294.04599, 333.*************], "page": 5, "span": [0, 66], "__ref_s3_data": null}], "text": "(4) Connected sub-pictures are grouped together in one Picture object.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [64.708, 303.59686, 264.50571, 311.98047], "page": 5, "span": [0, 49], "__ref_s3_data": null}], "text": "(5) Formula numbers are included in a Formula object.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [64.708008, 270.72882000000004, 294.04617, 301.02148], "page": 5, "span": [0, 156], "__ref_s3_data": null}], "text": "(6) Emphasised text (e.g. in italic or bold) at the beginning of a paragraph is not considered a Section-header , unless it appears exclusively on its own line.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [53.528999, 217.79882999999995, 295.56253, 259.0494699999999], "page": 5, "span": [0, 221], "__ref_s3_data": null}], "text": "The complete annotation guideline is over 100 pages long and a detailed description is obviously out of scope for this paper. Nevertheless, it will be made publicly available alongside with DocLayNet for future reference.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 86.29182400000002, 295.56226, 215.33101], "page": 5, "span": [0, 792], "__ref_s3_data": null}], "text": "Phase 3: Training. After a first trial with a small group of people, we realised that providing the annotation guideline and a set of random practice pages did not yield the desired quality level for layout annotation. Therefore we prepared a subset of pages with two different complexity levels, each with a practice and an exam part. 974 pages were reference-annotated by one proficient core team member. Annotation staff were then given the task to annotate the same subsets (blinded from the reference). By comparing the annotations of each staff member with the reference annotations, we could quantify how closely their annotations matched the reference. Only after passing two exam levels with high annotation quality, staff were admitted into the production phase. Practice iterations", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/3"}, {"prov": [{"bbox": [400.12842, 331.4399399999999, 476.3317**********, 333.55672999999996], "page": 5, "span": [0, 64], "__ref_s3_data": null}], "text": "05237a14f2524e3f53c8454b074409d05078038a6a36b770fcc8ec7e540deae0", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 288.11481, 559.80579, 318.50601], "page": 5, "span": [0, 173], "__ref_s3_data": null}], "text": "Figure 4: Examples of plausible annotation alternatives for the same page. Criteria in our annotation guideline can resolve cases A to C, while the case D remains ambiguous.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [317.62299, 247.16881999999998, 558.20435, 266.50247], "page": 5, "span": [0, 123], "__ref_s3_data": null}], "text": "were carried out over a timeframe of 12 weeks, after which 8 of the 40 initially allocated annotators did not pass the bar.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.62299, 82.78482800000006, 559.7149, 244.70100000000002], "page": 5, "span": [0, 987], "__ref_s3_data": null}], "text": "Phase 4: Production annotation. The previously selected 80K pages were annotated with the defined 11 class labels by 32 annotators. This production phase took around three months to complete. All annotations were created online through CCS, which visualises the programmatic PDF text-cells as an overlay on the page. The page annotation are obtained by drawing rectangular bounding-boxes, as shown in Figure 3. With regard to the annotation practices, we implemented a few constraints and capabilities on the tooling level. First, we only allow non-overlapping, vertically oriented, rectangular boxes. For the large majority of documents, this constraint was sufficient and it speeds up the annotation considerably in comparison with arbitrary segmentation shapes. Second, annotator staff were not able to see each other's annotations. This was enforced by design to avoid any bias in the annotation, which could skew the numbers of the inter-annotator agreement (see Table 1). We wanted", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.501999, 608.98291, 295.64874, 705.12708], "page": 6, "span": [0, 489], "__ref_s3_data": null}], "text": "Table 2: Prediction performance (mAP@0.5-0.95) of object detection networks on DocLayNet test set. The MRCNN (Mask R-CNN) and FRCNN (Faster R-CNN) models with ResNet-50 or ResNet-101 backbone were trained based on the network architectures from the detectron2 model zoo (Mask R-CNN R50, R101-FPN 3x, Faster R-CNN R101-FPN 3x), with default configurations. The YOLO implementation utilized was YOLOv5x6 [13]. All models were initialised using pre-trained weights from the COCO 2017 dataset.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/1"}, {"prov": [{"bbox": [53.528999, 215.43683, 295.55612, 421.07245], "page": 6, "span": [0, 1252], "__ref_s3_data": null}], "text": "to avoid this at any cost in order to have clear, unbiased baseline numbers for human document-layout annotation. Third, we introduced the feature of snapping boxes around text segments to obtain a pixel-accurate annotation and again reduce time and effort. The CCS annotation tool automatically shrinks every user-drawn box to the minimum bounding-box around the enclosed text-cells for all purely text-based segments, which excludes only Table and Picture . For the latter, we instructed annotation staff to minimise inclusion of surrounding whitespace while including all graphical lines. A downside of snapping boxes to enclosed text cells is that some wrongly parsed PDF pages cannot be annotated correctly and need to be skipped. Fourth, we established a way to flag pages as rejected for cases where no valid annotation according to the label guidelines could be achieved. Example cases for this would be PDF pages that render incorrectly or contain layouts that are impossible to capture with non-overlapping rectangles. Such rejected pages are not contained in the final dataset. With all these measures in place, experienced annotation staff managed to annotate a single page in a typical timeframe of 20s to 60s, depending on its complexity.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 193.56098999999995, 147.48535, 203.87009], "page": 6, "span": [0, 13], "__ref_s3_data": null}], "text": "5 EXPERIMENTS", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [53.484001, 82.700829, 295.4281, 178.74644], "page": 6, "span": [0, 584], "__ref_s3_data": null}], "text": "The primary goal of DocLayNet is to obtain high-quality ML models capable of accurate document-layout analysis on a wide variety of challenging layouts. As discussed in Section 2, object detection models are currently the easiest to use, due to the standardisation of ground-truth data in COCO format [16] and the availability of general frameworks such as detectron2 [17]. Furthermore, baseline numbers in PubLayNet and DocBank were obtained using standard object detection models such as Mask R-CNN and Faster R-CNN. As such, we will relate to these object detection methods in this", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/4"}, {"prov": [{"bbox": [317.95499, 449.**************, 559.80579, 512.98401], "page": 6, "span": [0, 329], "__ref_s3_data": null}], "text": "Figure 5: Prediction performance (mAP@0.5-0.95) of a Mask R-CNN network with ResNet50 backbone trained on increasing fractions of the DocLayNet dataset. The learning curve flattens around the 80% mark, indicating that increasing the size of the DocLayNet dataset with similar data will not yield significantly better predictions.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [317.95499, 388.**************, 558.20416, 407.98846], "page": 6, "span": [0, 102], "__ref_s3_data": null}], "text": "paper and leave the detailed evaluation of more recent methods mentioned in Section 2 for future work.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.64099, 311.94284, 558.4364, 386.07047], "page": 6, "span": [0, 397], "__ref_s3_data": null}], "text": "In this section, we will present several aspects related to the performance of object detection models on DocLayNet. Similarly as in PubLayNet, we will evaluate the quality of their predictions using mean average precision (mAP) with 10 overlaps that range from 0.5 to 0.95 in steps of 0.05 (mAP@0.5-0.95). These scores are computed by leveraging the evaluation code provided by the COCO API [16].", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 284.86902, 466.8532400000001, 295.1781], "page": 6, "span": [0, 30], "__ref_s3_data": null}], "text": "Baselines for Object Detection", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [317.74899, 85.29982800000005, 558.43085, 279.97546], "page": 6, "span": [0, 1146], "__ref_s3_data": null}], "text": "In Table 2, we present baseline experiments (given in mAP) on Mask R-CNN [12], Faster R-CNN [11], and YOLOv5 [13]. Both training and evaluation were performed on RGB images with dimensions of 1025 × 1025 pixels. For training, we only used one annotation in case of redundantly annotated pages. As one can observe, the variation in mAP between the models is rather low, but overall between 6 and 10% lower than the mAP computed from the pairwise human annotations on triple-annotated pages. This gives a good indication that the DocLayNet dataset poses a worthwhile challenge for the research community to close the gap between human recognition and ML approaches. It is interesting to see that Mask R-CNN and Faster R-CNN produce very comparable mAP scores, indicating that pixel-based image segmentation derived from bounding-boxes does not help to obtain better predictions. On the other hand, the more recent Yolov5x model does very well and even out-performs humans on selected labels such as Text , Table and Picture . This is not entirely surprising, as Text , Table and Picture are abundant and the most visually distinctive in a document.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.501999, 663.77686, 295.64865, 705.12708], "page": 7, "span": [0, 205], "__ref_s3_data": null}], "text": "Table 3: Performance of a Mask R-CNN R50 network in mAP@0.5-0.95 scores trained on DocLayNet with different class label sets. The reduced label sets were obtained by either down-mapping or dropping labels.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/2"}, {"prov": [{"bbox": [53.**************, 462.121, 131.05624, 472.43008], "page": 7, "span": [0, 14], "__ref_s3_data": null}], "text": "Learning Curve", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [52.785, 262.55182, 295.55835, 457.22845], "page": 7, "span": [0, 1157], "__ref_s3_data": null}], "text": "One of the fundamental questions related to any dataset is if it is \"large enough\". To answer this question for DocLayNet, we performed a data ablation study in which we evaluated a Mask R-CNN model trained on increasing fractions of the DocLayNet dataset. As can be seen in Figure 5, the mAP score rises sharply in the beginning and eventually levels out. To estimate the error-bar on the metrics, we ran the training five times on the entire data-set. This resulted in a 1% error-bar, depicted by the shaded area in Figure 5. In the inset of Figure 5, we show the exact same data-points, but with a logarithmic scale on the x-axis. As is expected, the mAP score increases linearly as a function of the data-size in the inset. The curve ultimately flattens out between the 80% and 100% mark, with the 80% mark falling within the error-bars of the 100% mark. This provides a good indication that the model would not improve significantly by yet increasing the data size. Rather, it would probably benefit more from improved data consistency (as discussed in Section 3), data augmentation methods [23], or the addition of more document categories and styles.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 239.**************, 164.32898, 249.**************], "page": 7, "span": [0, 22], "__ref_s3_data": null}], "text": "Impact of Class Labels", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [53.466999, 83.**************, 295.55679, 234.28845], "page": 7, "span": [0, 910], "__ref_s3_data": null}], "text": "The choice and number of labels can have a significant effect on the overall model performance. Since PubLayNet, DocBank and DocLayNet all have different label sets, it is of particular interest to understand and quantify this influence of the label set on the model performance. We investigate this by either down-mapping labels into more common ones (e.g. Caption → Text ) or excluding them from the annotations entirely. Furthermore, it must be stressed that all mappings and exclusions were performed on the data before model training. In Table 3, we present the mAP scores for a Mask R-CNN R50 network on different label sets. Where a label is down-mapped, we show its corresponding label, otherwise it was excluded. We present three different label sets, with 6, 5 and 4 different labels respectively. The set of 5 labels contains the same labels as PubLayNet. However, due to the different definition of", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.659, 663.77679, 559.80682, 705.12701], "page": 7, "span": [0, 189], "__ref_s3_data": null}], "text": "Table 4: Performance of a Mask R-CNN R50 network with document-wise and page-wise split for different label sets. Naive page-wise split will result in GLYPH<tildelow> 10% point improvement.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/3"}, {"prov": [{"bbox": [317.686, 375.50983, 559.58496, 460.59647], "page": 7, "span": [0, 469], "__ref_s3_data": null}], "text": "lists in PubLayNet (grouped list-items) versus DocLayNet (separate list-items), the label set of size 4 is the closest to PubLayNet, in the assumption that the List is down-mapped to Text in PubLayNet. The results in Table 3 show that the prediction accuracy on the remaining class labels does not change significantly when other classes are merged into them. The overall macro-average improves by around 5%, in particular when Page-footer and Page-header are excluded.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95496, 352.29602, 549.8606, 362.6051], "page": 7, "span": [0, 46], "__ref_s3_data": null}], "text": "Impact of Document Split in Train and Test Set", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [317.62299, 196.56282, 559.71381, 347.40347], "page": 7, "span": [0, 852], "__ref_s3_data": null}], "text": "Many documents in DocLayNet have a unique styling. In order to avoid overfitting on a particular style, we have split the train-, test- and validation-sets of DocLayNet on document boundaries, i.e. every document contributes pages to only one set. To the best of our knowledge, this was not considered in PubLayNet or DocBank. To quantify how this affects model performance, we trained and evaluated a Mask R-CNN R50 model on a modified dataset version. Here, the train-, test- and validation-sets were obtained by a randomised draw over the individual pages. As can be seen in Table 4, the difference in model performance is surprisingly large: pagewise splitting gains ˜ 10% in mAP over the document-wise splitting. Thus, random page-wise splitting of DocLayNet can easily lead to accidental overestimation of model performance and should be avoided.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 173.**************, 418.54776, 183.**************], "page": 7, "span": [0, 18], "__ref_s3_data": null}], "text": "Dataset Comparison", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [317.686, 83.**************, 559.18817, 168.**************], "page": 7, "span": [0, 521], "__ref_s3_data": null}], "text": "Throughout this paper, we claim that DocLayNet's wider variety of document layouts leads to more robust layout detection models. In Table 5, we provide evidence for that. We trained models on each of the available datasets (PubLayNet, DocBank and DocLayNet) and evaluated them on the test sets of the other datasets. Due to the different label sets and annotation styles, a direct comparison is not possible. Hence, we focussed on the common labels among the datasets. Between PubLayNet and DocLayNet, these are Picture ,", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.501999, 641.85889, 295.64868, 705.12708], "page": 8, "span": [0, 298], "__ref_s3_data": null}], "text": "Table 5: Prediction Performance (mAP@0.5-0.95) of a Mask R-CNN R50 network across the PubLayNet, DocBank & DocLayNet data-sets. By evaluating on common label classes of each dataset, we observe that the DocLayNet-trained model has much less pronounced variations in performance across all datasets.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/4"}, {"prov": [{"bbox": [53.**************, 348.85986, 294.04712, 401.07947], "page": 8, "span": [0, 295], "__ref_s3_data": null}], "text": "Section-header , Table and Text . Before training, we either mapped or excluded DocLayNet's other labels as specified in table 3, and also PubLayNet's List to Text . Note that the different clustering of lists (by list-element vs. whole list objects) naturally decreases the mAP score for Text .", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.466999, 206.40382, 295.55908, 346.**************], "page": 8, "span": [0, 793], "__ref_s3_data": null}], "text": "For comparison of DocBank with DocLayNet, we trained only on Picture and Table clusters of each dataset. We had to exclude Text because successive paragraphs are often grouped together into a single object in DocBank. This paragraph grouping is incompatible with the individual paragraphs of DocLayNet. As can be seen in Table 5, DocLayNet trained models yield better performance compared to the previous datasets. It is noteworthy that the models trained on PubLayNet and DocBank perform very well on their own test set, but have a much lower performance on the foreign datasets. While this also applies to DocLayNet, the difference is far less pronounced. Thus we conclude that DocLayNet trained models are overall more robust and will produce better results for challenging, unseen layouts.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [53.**************, 176.**************, 156.00534, 186.9390**********], "page": 8, "span": [0, 19], "__ref_s3_data": null}], "text": "Example Predictions", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [53.528999, 86.**************, 295.55844, 171.73645], "page": 8, "span": [0, 481], "__ref_s3_data": null}], "text": "To conclude this section, we illustrate the quality of layout predictions one can expect from DocLayNet-trained models by providing a selection of examples without any further post-processing applied. Figure 6 shows selected layout predictions on pages from the test-set of DocLayNet. Results look decent in general across document categories, however one can also observe mistakes such as overlapping clusters of different classes, or entirely missing boxes due to low confidence.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95502, 695.83099, 405.72961, 706.14014], "page": 8, "span": [0, 12], "__ref_s3_data": null}], "text": "6 CONCLUSION", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [317.95499, 605.85083, 559.71375, 690.93848], "page": 8, "span": [0, 507], "__ref_s3_data": null}], "text": "In this paper, we presented the DocLayNet dataset. It provides the document conversion and layout analysis research community a new and challenging dataset to improve and fine-tune novel ML methods on. In contrast to many other datasets, DocLayNet was created by human annotation in order to obtain reliable layout ground-truth on a wide variety of publication- and typesettingstyles. Including a large proportion of documents outside the scientific publishing domain adds significant value in this respect.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.686, 507.22083, 559.71704, 603.26648], "page": 8, "span": [0, 573], "__ref_s3_data": null}], "text": "From the dataset, we have derived on the one hand reference metrics for human performance on document-layout annotation (through double and triple annotations) and on the other hand evaluated the baseline performance of commonly used object detection methods. We also illustrated the impact of various dataset-related aspects on model performance through data-ablation experiments, both from a size and class-label perspective. Last but not least, we compared the accuracy of models trained on other public datasets and showed that DocLayNet trained models are more robust.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.62299, 474.34383999999994, 558.43469, 504.63647], "page": 8, "span": [0, 188], "__ref_s3_data": null}], "text": "To date, there is still a significant gap between human and ML accuracy on the layout interpretation task, and we hope that this work will inspire the research community to close that gap.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 446.59903, 387.3696, 456.90811], "page": 8, "span": [0, 10], "__ref_s3_data": null}], "text": "REFERENCES", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [321.198, 420.83713000000006, 558.20099, 443.29767], "page": 8, "span": [0, 187], "__ref_s3_data": null}], "text": "[1] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Icdar 2013 table competition. In 2013 12th International Conference on Document Analysis and Recognition , pages 1449-1453, 2013.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [321.198, 388.95712000000003, 559.37982, 419.38763], "page": 8, "span": [0, 275], "__ref_s3_data": null}], "text": "[2] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Icdar2017 competition on recognition of documents with complex layouts rdcl2017. In 2017 14th IAPR International Conference on Document Analysis and Recognition (ICDAR) , volume 01, pages 1404-1410, 2017.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [321.198, 365.05310000000003, 558.20013, 387.50763], "page": 8, "span": [0, 209], "__ref_s3_data": null}], "text": "[3] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. ICDAR 2019 Competition on Table Detection and Recognition (cTDaR), April 2019. http://sac.founderit.com/.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [321.198, 333.1731, 559.37872, 363.59665], "page": 8, "span": [0, 247], "__ref_s3_data": null}], "text": "[4] <PERSON>, <PERSON>, and <PERSON>. Competition on scientific literature parsing. In Proceedings of the International Conference on Document Analysis and Recognition , ICDAR, pages 605-617. LNCS 12824, SpringerVerlag, sep 2021.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [321.198, 301.29208, 559.02625, 331.71664], "page": 8, "span": [0, 257], "__ref_s3_data": null}], "text": "[5] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Segmentation for document layout analysis: not dead yet. International Journal on Document Analysis and Recognition (IJDAR) , pages 1-11, 01 2022.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [321.198, 277.3751199999999, 558.20361, 299.83563], "page": 8, "span": [0, 231], "__ref_s3_data": null}], "text": "[6] <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Publaynet: Largest dataset ever for document layout analysis. In Proceedings of the International Conference on Document Analysis and Recognition , ICDAR, pages 1015-1022, sep 2019.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [321.19797, 237.53111, 558.9715, 275.**************], "page": 8, "span": [0, 312], "__ref_s3_data": null}], "text": "[7] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Docbank: A benchmark dataset for document layout analysis. In Proceedings of the 28th International Conference on Computational Linguistics , COLING, pages 949-960. International Committee on Computational Linguistics, dec 2020.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [321.198, 213.**************, 558.90222, 236.**************], "page": 8, "span": [0, 168], "__ref_s3_data": null}], "text": "[8] <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Information extraction from pdf sources based on rule-based system using integrated formats. In SemWebEval@ESWC , 2016.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [321.198, 181.**************, 559.27448, 212.**************], "page": 8, "span": [0, 267], "__ref_s3_data": null}], "text": "[9] <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. <PERSON> feature hierarchies for accurate object detection and semantic segmentation. In IEEE Conference on Computer Vision and Pattern Recognition , CVPR, pages 580-587. IEEE Computer Society, jun 2014.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [317.95499, 165.79314, 558.20203, 180.28463999999997], "page": 8, "span": [0, 144], "__ref_s3_data": null}], "text": "[10] <PERSON>. Fast R-CNN. In 2015 IEEE International Conference on Computer Vision , ICCV, pages 1440-1448. IEEE Computer Society, dec 2015.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [317.95499, 141.88312999999994, 558.20142, 164.34362999999996], "page": 8, "span": [0, 222], "__ref_s3_data": null}], "text": "[11] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Faster r-cnn: Towards real-time object detection with region proposal networks. IEEE Transactions on Pattern Analysis and Machine Intelligence , 39(6):1137-1149, 2017.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [317.95499, 117.98010999999997, 559.27808, 140.43362000000002], "page": 8, "span": [0, 187], "__ref_s3_data": null}], "text": "[12] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Mask R-CNN. In IEEE International Conference on Computer Vision , ICCV, pages 2980-2988. IEEE Computer Society, Oct 2017.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [317.95499, 86.099106, 558.97156, 116.52364], "page": 8, "span": [0, 300], "__ref_s3_data": null}], "text": "[13] <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/5"}, {"prov": [{"bbox": [62.323874999999994, 343.73517, 318.50473, 349.71457], "page": 9, "span": [0, 89], "__ref_s3_data": null}], "text": "Text Caption List-Item Formula Table Section-Header Picture Page-Header Page-Footer Title", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [53.**************, 286.16876, 559.80786, 327.51801], "page": 9, "span": [0, 386], "__ref_s3_data": null}], "text": "Figure 6: Example layout predictions on selected pages from the DocLayNet test-set. (A, D) exhibit favourable results on coloured backgrounds. (B, C) show accurate list-item and paragraph differentiation despite densely-spaced lines. (E) demonstrates good table and figure distinction. (F) shows predictions on a Chinese patent with multiple overlaps, label confusion and missing boxes.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [69.234001, 242.4801, 295.22406, 264.93364999999994], "page": 9, "span": [0, 195], "__ref_s3_data": null}], "text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, o<PERSON>, and wa<PERSON><PERSON> yang. ultralytics/yolov5: v6.0 - yolov5n nano models, roboflow integration, tensorflow export, opencv dnn support, October 2021.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [317.95499, 250.45010000000002, 559.02637, 264.93362], "page": 9, "span": [0, 148], "__ref_s3_data": null}], "text": "[20] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Vtlayout: Fusion of visual and text features for document layout analysis, 2021.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [53.**************, 218.56313999999998, 295.12177, 241.02362000000005], "page": 9, "span": [0, 185], "__ref_s3_data": null}], "text": "[14] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. End-to-end object detection with transformers. CoRR , abs/2005.12872, 2020.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [53.**************, 202.62212999999997, 294.04224, 217.11365], "page": 9, "span": [0, 127], "__ref_s3_data": null}], "text": "[15] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> <PERSON><PERSON> Le. Eff<PERSON>det: Scalable and efficient object detection. CoRR , abs/1911.09070, 2019.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [53.79800399999999, 178.71910000000003, 295.22263, 201.17264], "page": 9, "span": [0, 214], "__ref_s3_data": null}], "text": "[16] <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Microsoft COCO: common objects in context, 2014.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [53.**************, 162.77910999999995, 295.12009, 177.26263000000006], "page": 9, "span": [0, 95], "__ref_s3_data": null}], "text": "[17] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Detectron2, 2019.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [53.**************, 122.92811000000006, 294.8089, 161.32263], "page": 9, "span": [0, 334], "__ref_s3_data": null}], "text": "[18] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Robust pdf document conversion using recurrent neural networks. In Proceedings of the 35th Conference on Artificial Intelligence , AAAI, pages 1513715145, feb 2021.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [53.797997, 83.07811000000004, 295.22174, 121.47163], "page": 9, "span": [0, 331], "__ref_s3_data": null}], "text": "[19] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Layoutlm: Pre-training of text and layout for document image understanding. In Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery and Data Mining , KDD, pages 1192-1200, New York, USA, 2020. Association for Computing Machinery.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [317.95499, 226.54009999999994, 558.9715, 248.99361999999996], "page": 9, "span": [0, 183], "__ref_s3_data": null}], "text": "[21] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Vsr: A unified framework for document layout analysis combining vision, semantics and relations, 2021.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [317.95499, 194.65212999999994, 559.27539, 225.08365000000003], "page": 9, "span": [0, 285], "__ref_s3_data": null}], "text": "[22] <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Corpus conversion service: A machine learning platform to ingest documents at scale. In Proceedings of the 24th ACM SIGKDD International Conference on Knowledge Discovery and Data Mining , KDD, pages 774-782. ACM, 2018.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [317.95499, 178.71213, 559.3783, 193.20263999999997], "page": 9, "span": [0, 133], "__ref_s3_data": null}], "text": "[23] <PERSON> and <PERSON><PERSON>. A survey on image data augmentation for deep learning. Journal of Big Data , 6(1):60, 2019.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}], "figures": [{"prov": [{"bbox": [323.408203125, 266.1492919921875, 553.2952270507812, 541.6512603759766], "page": 1, "span": [0, 84], "__ref_s3_data": null}], "text": "Figure 1: Four examples of complex page layouts across different document categories", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [88.33030700683594, 571.4317321777344, 263.7049560546875, 699.1134796142578], "page": 3, "span": [0, 69], "__ref_s3_data": null}], "text": "Figure 2: Distribution of DocLayNet pages across document categories.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [53.05912780761719, 251.135986328125, 295.8506164550781, 481.2087097167969], "page": 4, "span": [0, 281], "__ref_s3_data": null}], "text": "Figure 3: Corpus Conversion Service annotation user interface. The PDF page is shown in the background, with overlaid text-cells (in darker shades). The annotation boxes can be drawn by dragging a rectangle over each segment with the respective label from the palette on the right.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [315.960205078125, 332.31915283203125, 559.396484375, 706.6611862182617], "page": 5, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [323.48431396484375, 531.9892272949219, 553.5411376953125, 702.1139678955078], "page": 6, "span": [0, 329], "__ref_s3_data": null}], "text": "Figure 5: Prediction performance (mAP@0.5-0.95) of a Mask R-CNN network with ResNet50 backbone trained on increasing fractions of the DocLayNet dataset. The learning curve flattens around the 80% mark, indicating that increasing the size of the DocLayNet dataset with similar data will not yield significantly better predictions.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [52.963985443115234, 349.8648681640625, 556.931640625, 707.2641143798828], "page": 9, "span": [0, 89], "__ref_s3_data": null}], "text": "Text Caption List-Item Formula Table Section-Header Picture Page-Header Page-Footer Title", "type": "figure", "payload": null, "bounding-box": null}], "tables": [{"prov": [{"bbox": [98.93103790283203, 497.91851806640625, 512.579833984375, 654.5245208740234], "page": 4, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 1: DocLayNet dataset overview. Along with the frequency of each class label, we present the relative occurrence (as % of row \"Total\") in the train, test and validation sets. The inter-annotator agreement is computed as the mAP@0.5-0.95 metric between pairwise annotations from the triple-annotated pages, from which we obtain accuracy ranges.", "type": "table", "payload": null, "#-cols": 12, "#-rows": 14, "data": [[{"bbox": null, "spans": [[0, 0]], "text": "", "type": "body"}, {"bbox": null, "spans": [[0, 1]], "text": "", "type": "body"}, {"bbox": [233.94400000000002, 140.22351000000003, 270.04272, 148.59813999999994], "spans": [[0, 2], [0, 3], [0, 4]], "text": "% of Total", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [233.94400000000002, 140.22351000000003, 270.04272, 148.59813999999994], "spans": [[0, 2], [0, 3], [0, 4]], "text": "% of Total", "type": "col_header", "col": 3, "col-header": true, "col-span": [2, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [233.94400000000002, 140.22351000000003, 270.04272, 148.59813999999994], "spans": [[0, 2], [0, 3], [0, 4]], "text": "% of Total", "type": "col_header", "col": 4, "col-header": true, "col-span": [2, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [329.04999, 140.22351000000003, 483.3976400000001, 148.59813999999994], "spans": [[0, 5], [0, 6], [0, 7], [0, 8], [0, 9], [0, 10], [0, 11]], "text": "triple inter-annotator mAP @ 0.5-0.95 (%)", "type": "col_header", "col": 5, "col-header": true, "col-span": [5, 12], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [329.04999, 140.22351000000003, 483.3976400000001, 148.59813999999994], "spans": [[0, 5], [0, 6], [0, 7], [0, 8], [0, 9], [0, 10], [0, 11]], "text": "triple inter-annotator mAP @ 0.5-0.95 (%)", "type": "col_header", "col": 6, "col-header": true, "col-span": [5, 12], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [329.04999, 140.22351000000003, 483.3976400000001, 148.59813999999994], "spans": [[0, 5], [0, 6], [0, 7], [0, 8], [0, 9], [0, 10], [0, 11]], "text": "triple inter-annotator mAP @ 0.5-0.95 (%)", "type": "col_header", "col": 7, "col-header": true, "col-span": [5, 12], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [329.04999, 140.22351000000003, 483.3976400000001, 148.59813999999994], "spans": [[0, 5], [0, 6], [0, 7], [0, 8], [0, 9], [0, 10], [0, 11]], "text": "triple inter-annotator mAP @ 0.5-0.95 (%)", "type": "col_header", "col": 8, "col-header": true, "col-span": [5, 12], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [329.04999, 140.22351000000003, 483.3976400000001, 148.59813999999994], "spans": [[0, 5], [0, 6], [0, 7], [0, 8], [0, 9], [0, 10], [0, 11]], "text": "triple inter-annotator mAP @ 0.5-0.95 (%)", "type": "col_header", "col": 9, "col-header": true, "col-span": [5, 12], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [329.04999, 140.22351000000003, 483.3976400000001, 148.59813999999994], "spans": [[0, 5], [0, 6], [0, 7], [0, 8], [0, 9], [0, 10], [0, 11]], "text": "triple inter-annotator mAP @ 0.5-0.95 (%)", "type": "col_header", "col": 10, "col-header": true, "col-span": [5, 12], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [329.04999, 140.22351000000003, 483.3976400000001, 148.59813999999994], "spans": [[0, 5], [0, 6], [0, 7], [0, 8], [0, 9], [0, 10], [0, 11]], "text": "triple inter-annotator mAP @ 0.5-0.95 (%)", "type": "col_header", "col": 11, "col-header": true, "col-span": [5, 12], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [104.825, 151.18255999999997, 141.71277, 159.55719], "spans": [[1, 0]], "text": "class label", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [175.94701, 151.18255999999997, 198.71269, 159.55719], "spans": [[1, 1]], "text": "Count", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [213.795, 151.18255999999997, 233.69144, 159.55719], "spans": [[1, 2]], "text": "Train", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [249.37367, 151.18255999999997, 264.5, 159.55719], "spans": [[1, 3]], "text": "Test", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [283.53568, 151.18255999999997, 295.30856, 159.55719], "spans": [[1, 4]], "text": "Val", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [314.01501, 151.18255999999997, 324.98093, 159.55719], "spans": [[1, 5]], "text": "All", "type": "col_header", "col": 5, "col-header": true, "col-span": [5, 6], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [343.01236, 151.18255999999997, 354.65076, 159.55719], "spans": [[1, 6]], "text": "Fin", "type": "col_header", "col": 6, "col-header": true, "col-span": [6, 7], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [367.84033, 151.18255999999997, 384.32059, 159.55719], "spans": [[1, 7]], "text": "Man", "type": "col_header", "col": 7, "col-header": true, "col-span": [7, 8], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [407.54358, 151.18255999999997, 418.15979, 159.55719], "spans": [[1, 8]], "text": "<PERSON>i", "type": "col_header", "col": 8, "col-header": true, "col-span": [8, 9], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [432.29979999999995, 151.18255999999997, 447.82962, 159.55719], "spans": [[1, 9]], "text": "Law", "type": "col_header", "col": 9, "col-header": true, "col-span": [9, 10], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [465.72656, 151.18255999999997, 477.50842, 159.55719], "spans": [[1, 10]], "text": "<PERSON>", "type": "col_header", "col": 10, "col-header": true, "col-span": [10, 11], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [493.52240000000006, 151.18255999999997, 507.17822, 159.55719], "spans": [[1, 11]], "text": "Ten", "type": "col_header", "col": 11, "col-header": true, "col-span": [11, 12], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [104.825, 162.53954999999996, 134.01064, 170.91418], "spans": [[2, 0]], "text": "Caption", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [177.866, 162.53954999999996, 198.71288, 170.91418], "spans": [[2, 1]], "text": "22524", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [219.211, 162.53954999999996, 233.69174000000004, 170.91418], "spans": [[2, 2]], "text": "2.04", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [250.01956, 162.53954999999996, 264.50031, 170.91418], "spans": [[2, 3]], "text": "1.77", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [280.82812, 162.53954999999996, 295.30887, 170.91418], "spans": [[2, 4]], "text": "2.32", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [305.27301, 162.53954999999996, 324.98117, 170.91418], "spans": [[2, 5]], "text": "84-89", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [334.94284, 162.53954999999996, 354.651, 170.91418], "spans": [[2, 6]], "text": "40-61", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [364.61267, 162.53954999999996, 384.32083, 170.91418], "spans": [[2, 7]], "text": "86-92", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [398.45187, 162.53954999999996, 418.16003, 170.91418], "spans": [[2, 8]], "text": "94-99", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [428.1217, 162.53954999999996, 447.82986, 170.91418], "spans": [[2, 9]], "text": "95-99", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [457.80051, 162.53954999999996, 477.50867, 170.91418], "spans": [[2, 10]], "text": "69-78", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [495.32489, 162.53954999999996, 507.17846999999995, 170.91418], "spans": [[2, 11]], "text": "n/a", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [104.825, 173.49854000000005, 137.3282, 181.87316999999996], "spans": [[3, 0]], "text": "Footnote", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [182.035, 173.49854000000005, 198.71251, 181.87316999999996], "spans": [[3, 1]], "text": "6318", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [219.211, 173.49854000000005, 233.69174000000004, 181.87316999999996], "spans": [[3, 2]], "text": "0.60", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [250.01956, 173.49854000000005, 264.50031, 181.87316999999996], "spans": [[3, 3]], "text": "0.31", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [280.82812, 173.49854000000005, 295.30887, 181.87316999999996], "spans": [[3, 4]], "text": "0.58", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [305.27301, 173.49854000000005, 324.98117, 181.87316999999996], "spans": [[3, 5]], "text": "83-91", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [342.79739, 173.49854000000005, 354.65097, 181.87316999999996], "spans": [[3, 6]], "text": "n/a", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [371.81265, 173.49854000000005, 384.32077, 181.87316999999996], "spans": [[3, 7]], "text": "100", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [398.45181, 173.49854000000005, 418.15997, 181.87316999999996], "spans": [[3, 8]], "text": "62-88", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [428.12164, 173.49854000000005, 447.8298, 181.87316999999996], "spans": [[3, 9]], "text": "85-94", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [465.655, 173.49854000000005, 477.50857999999994, 181.87316999999996], "spans": [[3, 10]], "text": "n/a", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [487.47025, 173.49854000000005, 507.17841, 181.87316999999996], "spans": [[3, 11]], "text": "82-97", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [104.825, 184.45752000000005, 135.33766, 192.83214999999996], "spans": [[4, 0]], "text": "Formula", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [177.866, 184.45752000000005, 198.71288, 192.83214999999996], "spans": [[4, 1]], "text": "25027", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [219.211, 184.45752000000005, 233.69174000000004, 192.83214999999996], "spans": [[4, 2]], "text": "2.25", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [250.01956, 184.45752000000005, 264.50031, 192.83214999999996], "spans": [[4, 3]], "text": "1.90", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [280.82812, 184.45752000000005, 295.30887, 192.83214999999996], "spans": [[4, 4]], "text": "2.96", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [305.27301, 184.45752000000005, 324.98117, 192.83214999999996], "spans": [[4, 5]], "text": "83-85", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [342.79739, 184.45752000000005, 354.65097, 192.83214999999996], "spans": [[4, 6]], "text": "n/a", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [372.46719, 184.45752000000005, 384.32077, 192.83214999999996], "spans": [[4, 7]], "text": "n/a", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [398.45181, 184.45752000000005, 418.15997, 192.83214999999996], "spans": [[4, 8]], "text": "84-87", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [428.12164, 184.45752000000005, 447.8298, 192.83214999999996], "spans": [[4, 9]], "text": "86-96", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [465.655, 184.45752000000005, 477.50857999999994, 192.83214999999996], "spans": [[4, 10]], "text": "n/a", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [495.3248, 184.45752000000005, 507.17838000000006, 192.83214999999996], "spans": [[4, 11]], "text": "n/a", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [104.825, 195.41656, 137.70479, 203.7912], "spans": [[5, 0]], "text": "List-item", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [173.69701, 195.41656, 198.71326, 203.7912], "spans": [[5, 1]], "text": "185660", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [215.04201, 195.41656, 233.69212, 203.7912], "spans": [[5, 2]], "text": "17.19", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [245.85056, 195.41656, 264.50067, 203.7912], "spans": [[5, 3]], "text": "13.34", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [276.65912, 195.41656, 295.30923, 203.7912], "spans": [[5, 4]], "text": "15.82", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [305.27301, 195.41656, 324.98117, 203.7912], "spans": [[5, 5]], "text": "87-88", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [334.94284, 195.41656, 354.651, 203.7912], "spans": [[5, 6]], "text": "74-83", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [364.61267, 195.41656, 384.32083, 203.7912], "spans": [[5, 7]], "text": "90-92", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [398.45187, 195.41656, 418.16003, 203.7912], "spans": [[5, 8]], "text": "97-97", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [428.1217, 195.41656, 447.82986, 203.7912], "spans": [[5, 9]], "text": "81-85", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [457.80051, 195.41656, 477.50867, 203.7912], "spans": [[5, 10]], "text": "75-88", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [487.47034, 195.41656, 507.17849999999993, 203.7912], "spans": [[5, 11]], "text": "93-95", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [104.825, 206.37554999999998, 147.35262, 214.75018], "spans": [[6, 0]], "text": "Page-footer", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [177.866, 206.37554999999998, 198.71288, 214.75018], "spans": [[6, 1]], "text": "70878", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [219.211, 206.37554999999998, 233.69174000000004, 214.75018], "spans": [[6, 2]], "text": "6.51", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [250.01956, 206.37554999999998, 264.50031, 214.75018], "spans": [[6, 3]], "text": "5.58", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [280.82812, 206.37554999999998, 295.30887, 214.75018], "spans": [[6, 4]], "text": "6.00", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [305.27301, 206.37554999999998, 324.98117, 214.75018], "spans": [[6, 5]], "text": "93-94", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [334.94284, 206.37554999999998, 354.651, 214.75018], "spans": [[6, 6]], "text": "88-90", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [364.61267, 206.37554999999998, 384.32083, 214.75018], "spans": [[6, 7]], "text": "95-96", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [405.65189, 206.37554999999998, 418.16, 214.75018], "spans": [[6, 8]], "text": "100", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [428.12167, 206.37554999999998, 447.82983, 214.75018], "spans": [[6, 9]], "text": "92-97", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [465.00049, 206.37554999999998, 477.5086099999999, 214.75018], "spans": [[6, 10]], "text": "100", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [487.47028, 206.37554999999998, 507.17843999999997, 214.75018], "spans": [[6, 11]], "text": "96-98", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 6, "row-header": false, "row-span": [6, 7]}], [{"bbox": [104.825, 217.33452999999997, 150.10532, 225.70916999999997], "spans": [[7, 0]], "text": "Page-header", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 7, "row-header": true, "row-span": [7, 8]}, {"bbox": [177.866, 217.33452999999997, 198.71288, 225.70916999999997], "spans": [[7, 1]], "text": "58022", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [219.211, 217.33452999999997, 233.69174000000004, 225.70916999999997], "spans": [[7, 2]], "text": "5.10", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [250.01956, 217.33452999999997, 264.50031, 225.70916999999997], "spans": [[7, 3]], "text": "6.70", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [280.82812, 217.33452999999997, 295.30887, 225.70916999999997], "spans": [[7, 4]], "text": "5.06", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [305.27301, 217.33452999999997, 324.98117, 225.70916999999997], "spans": [[7, 5]], "text": "85-89", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [334.94284, 217.33452999999997, 354.651, 225.70916999999997], "spans": [[7, 6]], "text": "66-76", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [364.61267, 217.33452999999997, 384.32083, 225.70916999999997], "spans": [[7, 7]], "text": "90-94", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [394.2825, 217.33452999999997, 418.16003, 225.70916999999997], "spans": [[7, 8]], "text": "98-100", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [428.1217, 217.33452999999997, 447.82986, 225.70916999999997], "spans": [[7, 9]], "text": "91-92", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [457.80051, 217.33452999999997, 477.50867, 225.70916999999997], "spans": [[7, 10]], "text": "97-99", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [487.47034, 217.33452999999997, 507.17849999999993, 225.70916999999997], "spans": [[7, 11]], "text": "81-86", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 7, "row-header": false, "row-span": [7, 8]}], [{"bbox": [104.825, 228.29351999999994, 130.80963, 236.66814999999997], "spans": [[8, 0]], "text": "Picture", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 8, "row-header": true, "row-span": [8, 9]}, {"bbox": [177.866, 228.29351999999994, 198.71288, 236.66814999999997], "spans": [[8, 1]], "text": "45976", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [219.211, 228.29351999999994, 233.69174000000004, 236.66814999999997], "spans": [[8, 2]], "text": "4.21", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [250.01956, 228.29351999999994, 264.50031, 236.66814999999997], "spans": [[8, 3]], "text": "2.78", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [280.82812, 228.29351999999994, 295.30887, 236.66814999999997], "spans": [[8, 4]], "text": "5.31", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [305.27301, 228.29351999999994, 324.98117, 236.66814999999997], "spans": [[8, 5]], "text": "69-71", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [334.94284, 228.29351999999994, 354.651, 236.66814999999997], "spans": [[8, 6]], "text": "56-59", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [364.61267, 228.29351999999994, 384.32083, 236.66814999999997], "spans": [[8, 7]], "text": "82-86", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [398.45187, 228.29351999999994, 418.16003, 236.66814999999997], "spans": [[8, 8]], "text": "69-82", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [428.1217, 228.29351999999994, 447.82986, 236.66814999999997], "spans": [[8, 9]], "text": "80-95", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [457.80051, 228.29351999999994, 477.50867, 236.66814999999997], "spans": [[8, 10]], "text": "66-71", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [487.47034, 228.29351999999994, 507.17849999999993, 236.66814999999997], "spans": [[8, 11]], "text": "59-76", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 8, "row-header": false, "row-span": [8, 9]}], [{"bbox": [104.825, 239.25256000000002, 159.56487, 247.62720000000002], "spans": [[9, 0]], "text": "Section-header", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 9, "row-header": true, "row-span": [9, 10]}, {"bbox": [173.69701, 239.25256000000002, 198.71326, 247.62720000000002], "spans": [[9, 1]], "text": "142884", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [215.04201, 239.25256000000002, 233.69212, 247.62720000000002], "spans": [[9, 2]], "text": "12.60", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [245.85056, 239.25256000000002, 264.50067, 247.62720000000002], "spans": [[9, 3]], "text": "15.77", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [276.65912, 239.25256000000002, 295.30923, 247.62720000000002], "spans": [[9, 4]], "text": "12.85", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [305.27301, 239.25256000000002, 324.98117, 247.62720000000002], "spans": [[9, 5]], "text": "83-84", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [334.94284, 239.25256000000002, 354.651, 247.62720000000002], "spans": [[9, 6]], "text": "76-81", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [364.61267, 239.25256000000002, 384.32083, 247.62720000000002], "spans": [[9, 7]], "text": "90-92", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [398.45187, 239.25256000000002, 418.16003, 247.62720000000002], "spans": [[9, 8]], "text": "94-95", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [428.1217, 239.25256000000002, 447.82986, 247.62720000000002], "spans": [[9, 9]], "text": "87-94", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [457.80051, 239.25256000000002, 477.50867, 247.62720000000002], "spans": [[9, 10]], "text": "69-73", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [487.47034, 239.25256000000002, 507.17849999999993, 247.62720000000002], "spans": [[9, 11]], "text": "78-86", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 9, "row-header": false, "row-span": [9, 10]}], [{"bbox": [104.825, 250.21155, 124.63177, 258.58618], "spans": [[10, 0]], "text": "Table", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 10, "row-header": true, "row-span": [10, 11]}, {"bbox": [177.866, 250.21155, 198.71288, 258.58618], "spans": [[10, 1]], "text": "34733", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [219.211, 250.21155, 233.69174000000004, 258.58618], "spans": [[10, 2]], "text": "3.20", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [250.01956, 250.21155, 264.50031, 258.58618], "spans": [[10, 3]], "text": "2.27", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [280.82812, 250.21155, 295.30887, 258.58618], "spans": [[10, 4]], "text": "3.60", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [305.27301, 250.21155, 324.98117, 258.58618], "spans": [[10, 5]], "text": "77-81", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [334.94284, 250.21155, 354.651, 258.58618], "spans": [[10, 6]], "text": "75-80", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [364.61267, 250.21155, 384.32083, 258.58618], "spans": [[10, 7]], "text": "83-86", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [398.45187, 250.21155, 418.16003, 258.58618], "spans": [[10, 8]], "text": "98-99", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [428.1217, 250.21155, 447.82986, 258.58618], "spans": [[10, 9]], "text": "58-80", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [457.80051, 250.21155, 477.50867, 258.58618], "spans": [[10, 10]], "text": "79-84", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [487.47034, 250.21155, 507.17849999999993, 258.58618], "spans": [[10, 11]], "text": "70-85", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 10, "row-header": false, "row-span": [10, 11]}], [{"bbox": [104.825, 261.**************, 120.78519, 269.5441**********], "spans": [[11, 0]], "text": "Text", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 11, "row-header": true, "row-span": [11, 12]}, {"bbox": [173.69701, 261.**************, 198.71326, 269.5441**********], "spans": [[11, 1]], "text": "510377", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [215.04201, 261.**************, 233.69212, 269.5441**********], "spans": [[11, 2]], "text": "45.82", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [245.85056, 261.**************, 264.50067, 269.5441**********], "spans": [[11, 3]], "text": "49.28", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [276.65912, 261.**************, 295.30923, 269.5441**********], "spans": [[11, 4]], "text": "45.00", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [305.27301, 261.**************, 324.98117, 269.5441**********], "spans": [[11, 5]], "text": "84-86", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [334.94284, 261.**************, 354.651, 269.5441**********], "spans": [[11, 6]], "text": "81-86", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [364.61267, 261.**************, 384.32083, 269.5441**********], "spans": [[11, 7]], "text": "88-93", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [398.45187, 261.**************, 418.16003, 269.5441**********], "spans": [[11, 8]], "text": "89-93", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [428.1217, 261.**************, 447.82986, 269.5441**********], "spans": [[11, 9]], "text": "87-92", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [457.80051, 261.**************, 477.50867, 269.5441**********], "spans": [[11, 10]], "text": "71-79", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [487.47034, 261.**************, 507.17849999999993, 269.5441**********], "spans": [[11, 11]], "text": "87-95", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 11, "row-header": false, "row-span": [11, 12]}], [{"bbox": [104.825, 272.12854000000004, 121.81633, 280.50317], "spans": [[12, 0]], "text": "Title", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 12, "row-header": true, "row-span": [12, 13]}, {"bbox": [182.035, 272.12854000000004, 198.71251, 280.50317], "spans": [[12, 1]], "text": "5071", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [219.211, 272.12854000000004, 233.69174000000004, 280.50317], "spans": [[12, 2]], "text": "0.47", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [250.01956, 272.12854000000004, 264.50031, 280.50317], "spans": [[12, 3]], "text": "0.30", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [280.82812, 272.12854000000004, 295.30887, 280.50317], "spans": [[12, 4]], "text": "0.50", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [305.27301, 272.12854000000004, 324.98117, 280.50317], "spans": [[12, 5]], "text": "60-72", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [334.94284, 272.12854000000004, 354.651, 280.50317], "spans": [[12, 6]], "text": "24-63", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [364.61267, 272.12854000000004, 384.32083, 280.50317], "spans": [[12, 7]], "text": "50-63", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [394.2825, 272.12854000000004, 418.16003, 280.50317], "spans": [[12, 8]], "text": "94-100", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [428.1217, 272.12854000000004, 447.82986, 280.50317], "spans": [[12, 9]], "text": "82-96", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [457.80051, 272.12854000000004, 477.50867, 280.50317], "spans": [[12, 10]], "text": "68-79", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [487.47034, 272.12854000000004, 507.17849999999993, 280.50317], "spans": [[12, 11]], "text": "24-56", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 12, "row-header": false, "row-span": [12, 13]}], [{"bbox": [104.825, 283.48654, 123.43028, 291.86118000000005], "spans": [[13, 0]], "text": "Total", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 13, "row-header": true, "row-span": [13, 14]}, {"bbox": [169.52699, 283.48654, 198.71263, 291.86118000000005], "spans": [[13, 1]], "text": "1107470", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [208.675, 283.48654, 233.69124999999997, 291.86118000000005], "spans": [[13, 2]], "text": "941123", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [243.65291999999997, 283.48654, 264.49982, 291.86118000000005], "spans": [[13, 3]], "text": "99816", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [274.46149, 283.48654, 295.30838, 291.86118000000005], "spans": [[13, 4]], "text": "66531", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [305.27301, 283.48654, 324.98117, 291.86118000000005], "spans": [[13, 5]], "text": "82-83", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [334.94284, 283.48654, 354.651, 291.86118000000005], "spans": [[13, 6]], "text": "71-74", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [364.61267, 283.48654, 384.32083, 291.86118000000005], "spans": [[13, 7]], "text": "79-81", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [398.45187, 283.48654, 418.16003, 291.86118000000005], "spans": [[13, 8]], "text": "89-94", "type": "body", "col": 8, "col-header": false, "col-span": [8, 9], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [428.1217, 283.48654, 447.82986, 291.86118000000005], "spans": [[13, 9]], "text": "86-91", "type": "body", "col": 9, "col-header": false, "col-span": [9, 10], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [457.80051, 283.48654, 477.50867, 291.86118000000005], "spans": [[13, 10]], "text": "71-76", "type": "body", "col": 10, "col-header": false, "col-span": [10, 11], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [487.47034, 283.48654, 507.17849999999993, 291.86118000000005], "spans": [[13, 11]], "text": "68-85", "type": "body", "col": 11, "col-header": false, "col-span": [11, 12], "row": 13, "row-header": false, "row-span": [13, 14]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [62.02753829956055, 440.3381042480469, 285.78955078125, 596.3199310302734], "page": 6, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 6, "#-rows": 14, "data": [[{"bbox": null, "spans": [[0, 0]], "text": "", "type": "body"}, {"bbox": [132.36501, 197.97351000000003, 157.99098, 206.34813999999994], "spans": [[0, 1]], "text": "human", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [173.505, 197.97351000000003, 204.61841, 206.34813999999994], "spans": [[0, 2], [0, 3]], "text": "MRCNN", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 4], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [173.505, 197.97351000000003, 204.61841, 206.34813999999994], "spans": [[0, 2], [0, 3]], "text": "MRCNN", "type": "col_header", "col": 3, "col-header": true, "col-span": [2, 4], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [220.13028, 197.97351000000003, 248.06958, 206.34813999999994], "spans": [[0, 4]], "text": "FRCNN", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [258.03125, 197.97351000000003, 280.17825, 206.34813999999994], "spans": [[0, 5]], "text": "YOLO", "type": "col_header", "col": 5, "col-header": true, "col-span": [5, 6], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": null, "spans": [[1, 0]], "text": "", "type": "body"}, {"bbox": null, "spans": [[1, 1]], "text": "", "type": "body"}, {"bbox": [168.39301, 208.93255999999997, 181.99504, 217.30719], "spans": [[1, 2]], "text": "R50", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [192.39606, 208.93255999999997, 210.16747, 217.30719], "spans": [[1, 3]], "text": "R101", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [225.21309, 208.93255999999997, 242.9845, 217.30719], "spans": [[1, 4]], "text": "R101", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [260.51379, 208.93255999999997, 277.70239, 217.30719], "spans": [[1, 5]], "text": "v5x6", "type": "col_header", "col": 5, "col-header": true, "col-span": [5, 6], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [67.663002, 220.28954999999996, 96.848633, 228.66418], "spans": [[2, 0]], "text": "Caption", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [135.32401, 220.28954999999996, 155.03215, 228.66418], "spans": [[2, 1]], "text": "84-89", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [167.95399, 220.28954999999996, 182.43472, 228.66418], "spans": [[2, 2]], "text": "68.4", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [194.0462, 220.28954999999996, 208.52695, 228.66418], "spans": [[2, 3]], "text": "71.5", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [226.86324000000002, 220.28954999999996, 241.34396, 228.66418], "spans": [[2, 4]], "text": "70.1", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [261.86804, 220.28954999999996, 276.34879, 228.66418], "spans": [[2, 5]], "text": "77.7", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [67.663002, 231.24854000000005, 100.1662, 239.62316999999996], "spans": [[3, 0]], "text": "Footnote", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [135.32401, 231.24854000000005, 155.03215, 239.62316999999996], "spans": [[3, 1]], "text": "83-91", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [167.95399, 231.24854000000005, 182.43472, 239.62316999999996], "spans": [[3, 2]], "text": "70.9", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [194.0462, 231.24854000000005, 208.52695, 239.62316999999996], "spans": [[3, 3]], "text": "71.8", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [226.86324000000002, 231.24854000000005, 241.34396, 239.62316999999996], "spans": [[3, 4]], "text": "73.7", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [261.86804, 231.24854000000005, 276.34879, 239.62316999999996], "spans": [[3, 5]], "text": "77.2", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [67.663002, 242.20752000000005, 98.175659, 250.58214999999996], "spans": [[4, 0]], "text": "Formula", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [135.32401, 242.20752000000005, 155.03215, 250.58214999999996], "spans": [[4, 1]], "text": "83-85", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [167.95399, 242.20752000000005, 182.43472, 250.58214999999996], "spans": [[4, 2]], "text": "60.1", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [194.0462, 242.20752000000005, 208.52695, 250.58214999999996], "spans": [[4, 3]], "text": "63.4", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [226.86324000000002, 242.20752000000005, 241.34396, 250.58214999999996], "spans": [[4, 4]], "text": "63.5", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [261.86804, 242.20752000000005, 276.34879, 250.58214999999996], "spans": [[4, 5]], "text": "66.2", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [67.663002, 253.16656, 100.54279, 261.5412], "spans": [[5, 0]], "text": "List-item", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [135.32401, 253.16656, 155.03215, 261.5412], "spans": [[5, 1]], "text": "87-88", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [167.95399, 253.16656, 182.43472, 261.5412], "spans": [[5, 2]], "text": "81.2", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [194.0462, 253.16656, 208.52695, 261.5412], "spans": [[5, 3]], "text": "80.8", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [226.86324000000002, 253.16656, 241.34396, 261.5412], "spans": [[5, 4]], "text": "81.0", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [261.86804, 253.16656, 276.34879, 261.5412], "spans": [[5, 5]], "text": "86.2", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [67.663002, 264.12555, 110.19064, 272.50018], "spans": [[6, 0]], "text": "Page-footer", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [135.32401, 264.12555, 155.03215, 272.50018], "spans": [[6, 1]], "text": "93-94", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [167.95399, 264.12555, 182.43472, 272.50018], "spans": [[6, 2]], "text": "61.6", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [194.0462, 264.12555, 208.52695, 272.50018], "spans": [[6, 3]], "text": "59.3", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [226.86324000000002, 264.12555, 241.34396, 272.50018], "spans": [[6, 4]], "text": "58.9", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [261.86804, 264.12555, 276.34879, 272.50018], "spans": [[6, 5]], "text": "61.1", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 6, "row-header": false, "row-span": [6, 7]}], [{"bbox": [67.663002, 275.08453, 112.94331999999999, 283.45917], "spans": [[7, 0]], "text": "Page-header", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 7, "row-header": true, "row-span": [7, 8]}, {"bbox": [135.32401, 275.08453, 155.03215, 283.45917], "spans": [[7, 1]], "text": "85-89", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [167.95399, 275.08453, 182.43472, 283.45917], "spans": [[7, 2]], "text": "71.9", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [194.0462, 275.08453, 208.52695, 283.45917], "spans": [[7, 3]], "text": "70.0", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [226.86324000000002, 275.08453, 241.34396, 283.45917], "spans": [[7, 4]], "text": "72.0", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [261.86804, 275.08453, 276.34879, 283.45917], "spans": [[7, 5]], "text": "67.9", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 7, "row-header": false, "row-span": [7, 8]}], [{"bbox": [67.663002, 286.04355000000004, 93.647629, 294.41818], "spans": [[8, 0]], "text": "Picture", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 8, "row-header": true, "row-span": [8, 9]}, {"bbox": [135.32401, 286.04355000000004, 155.03215, 294.41818], "spans": [[8, 1]], "text": "69-71", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [167.95399, 286.04355000000004, 182.43472, 294.41818], "spans": [[8, 2]], "text": "71.7", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [194.0462, 286.04355000000004, 208.52695, 294.41818], "spans": [[8, 3]], "text": "72.7", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [226.86324000000002, 286.04355000000004, 241.34396, 294.41818], "spans": [[8, 4]], "text": "72.0", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [261.86804, 286.04355000000004, 276.34879, 294.41818], "spans": [[8, 5]], "text": "77.1", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 8, "row-header": false, "row-span": [8, 9]}], [{"bbox": [67.663002, 297.00253, 122.40287999999998, 305.37717], "spans": [[9, 0]], "text": "Section-header", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 9, "row-header": true, "row-span": [9, 10]}, {"bbox": [135.32401, 297.00253, 155.03215, 305.37717], "spans": [[9, 1]], "text": "83-84", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [167.95399, 297.00253, 182.43472, 305.37717], "spans": [[9, 2]], "text": "67.6", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [194.0462, 297.00253, 208.52695, 305.37717], "spans": [[9, 3]], "text": "69.3", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [226.86324000000002, 297.00253, 241.34396, 305.37717], "spans": [[9, 4]], "text": "68.4", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [261.86804, 297.00253, 276.34879, 305.37717], "spans": [[9, 5]], "text": "74.6", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 9, "row-header": false, "row-span": [9, 10]}], [{"bbox": [67.663002, 307.96155, 87.46978, 316.33618], "spans": [[10, 0]], "text": "Table", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 10, "row-header": true, "row-span": [10, 11]}, {"bbox": [135.32401, 307.96155, 155.03215, 316.33618], "spans": [[10, 1]], "text": "77-81", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [167.95399, 307.96155, 182.43472, 316.33618], "spans": [[10, 2]], "text": "82.2", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [194.0462, 307.96155, 208.52695, 316.33618], "spans": [[10, 3]], "text": "82.9", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [226.86324000000002, 307.96155, 241.34396, 316.33618], "spans": [[10, 4]], "text": "82.2", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [261.86804, 307.96155, 276.34879, 316.33618], "spans": [[10, 5]], "text": "86.3", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 10, "row-header": false, "row-span": [10, 11]}], [{"bbox": [67.663002, 318.91953, 83.623199, 327.29416], "spans": [[11, 0]], "text": "Text", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 11, "row-header": true, "row-span": [11, 12]}, {"bbox": [135.32401, 318.91953, 155.03215, 327.29416], "spans": [[11, 1]], "text": "84-86", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [167.95399, 318.91953, 182.43472, 327.29416], "spans": [[11, 2]], "text": "84.6", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [194.0462, 318.91953, 208.52695, 327.29416], "spans": [[11, 3]], "text": "85.8", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [226.86324000000002, 318.91953, 241.34396, 327.29416], "spans": [[11, 4]], "text": "85.4", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [261.86804, 318.91953, 276.34879, 327.29416], "spans": [[11, 5]], "text": "88.1", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 11, "row-header": false, "row-span": [11, 12]}], [{"bbox": [67.663002, 329.87854, 84.654327, 338.25317], "spans": [[12, 0]], "text": "Title", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 12, "row-header": true, "row-span": [12, 13]}, {"bbox": [135.32401, 329.87854, 155.03215, 338.25317], "spans": [[12, 1]], "text": "60-72", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [167.95399, 329.87854, 182.43472, 338.25317], "spans": [[12, 2]], "text": "76.7", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [194.0462, 329.87854, 208.52695, 338.25317], "spans": [[12, 3]], "text": "80.4", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [226.86324000000002, 329.87854, 241.34396, 338.25317], "spans": [[12, 4]], "text": "79.9", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [261.86804, 329.87854, 276.34879, 338.25317], "spans": [[12, 5]], "text": "82.7", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 12, "row-header": false, "row-span": [12, 13]}], [{"bbox": [67.663002, 341.23654, 78.628906, 349.61118000000005], "spans": [[13, 0]], "text": "All", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 13, "row-header": true, "row-span": [13, 14]}, {"bbox": [135.32401, 341.23654, 155.03215, 349.61118000000005], "spans": [[13, 1]], "text": "82-83", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [167.95399, 341.23654, 182.43472, 349.61118000000005], "spans": [[13, 2]], "text": "72.4", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [194.0462, 341.23654, 208.52695, 349.61118000000005], "spans": [[13, 3]], "text": "73.5", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [226.86324000000002, 341.23654, 241.34396, 349.61118000000005], "spans": [[13, 4]], "text": "73.4", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [261.86804, 341.23654, 276.34879, 349.61118000000005], "spans": [[13, 5]], "text": "76.8", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 13, "row-header": false, "row-span": [13, 14]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [80.35525512695312, 496.5545349121094, 267.0082092285156, 641.0637054443359], "page": 7, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 5, "#-rows": 13, "data": [[{"bbox": [86.372002, 153.10051999999996, 129.46452, 161.47515999999996], "spans": [[0, 0]], "text": "Class-count", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [151.07401, 153.10051999999996, 159.41275, 161.47515999999996], "spans": [[0, 1]], "text": "11", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [179.31816, 153.10051999999996, 183.48753, 161.47515999999996], "spans": [[0, 2]], "text": "6", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [213.33669, 153.10051999999996, 217.50606, 161.47515999999996], "spans": [[0, 3]], "text": "5", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [247.35521, 153.10051999999996, 251.52458, 161.47515999999996], "spans": [[0, 4]], "text": "4", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [86.372002, 164.45752000000005, 115.55763, 172.83214999999996], "spans": [[1, 0]], "text": "Caption", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": true, "row-span": [1, 2]}, {"bbox": [151.07401, 164.45752000000005, 159.41275, 172.83214999999996], "spans": [[1, 1]], "text": "68", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [173.42723, 164.45752000000005, 189.38742, 172.83214999999996], "spans": [[1, 2]], "text": "Text", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [207.44576, 164.45752000000005, 223.40594000000002, 172.83214999999996], "spans": [[1, 3]], "text": "Text", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [241.46428, 164.45752000000005, 257.42447, 172.83214999999996], "spans": [[1, 4]], "text": "Text", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [86.372002, 175.41656, 118.8752, 183.7912], "spans": [[2, 0]], "text": "Footnote", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [151.07401, 175.41656, 159.41275, 183.7912], "spans": [[2, 1]], "text": "71", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [173.42723, 175.41656, 189.38742, 183.7912], "spans": [[2, 2]], "text": "Text", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [207.44576, 175.41656, 223.40594000000002, 183.7912], "spans": [[2, 3]], "text": "Text", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [241.46428, 175.41656, 257.42447, 183.7912], "spans": [[2, 4]], "text": "Text", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [86.372002, 186.37554999999998, 116.88466, 194.75018], "spans": [[3, 0]], "text": "Formula", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [151.07401, 186.37554999999998, 159.41275, 194.75018], "spans": [[3, 1]], "text": "60", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [173.42723, 186.37554999999998, 189.38742, 194.75018], "spans": [[3, 2]], "text": "Text", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [207.44576, 186.37554999999998, 223.40594000000002, 194.75018], "spans": [[3, 3]], "text": "Text", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [241.46428, 186.37554999999998, 257.42447, 194.75018], "spans": [[3, 4]], "text": "Text", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [86.372002, 197.33452999999997, 119.25179, 205.70916999999997], "spans": [[4, 0]], "text": "List-item", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [151.07401, 197.33452999999997, 159.41275, 205.70916999999997], "spans": [[4, 1]], "text": "81", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [173.42723, 197.33452999999997, 189.38742, 205.70916999999997], "spans": [[4, 2]], "text": "Text", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [211.25647, 197.33452999999997, 219.59521, 205.70916999999997], "spans": [[4, 3]], "text": "82", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [241.46426, 197.33452999999997, 257.42447, 205.70916999999997], "spans": [[4, 4]], "text": "Text", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [86.372002, 208.29351999999994, 128.89964, 216.66814999999997], "spans": [[5, 0]], "text": "Page-footer", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [151.07401, 208.29351999999994, 159.41275, 216.66814999999997], "spans": [[5, 1]], "text": "62", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [177.23795, 208.29351999999994, 185.57669, 216.66814999999997], "spans": [[5, 2]], "text": "62", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [213.91052, 208.29351999999994, 216.94116, 216.66814999999997], "spans": [[5, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [247.92905000000002, 208.29351999999994, 250.95969, 216.66814999999997], "spans": [[5, 4]], "text": "-", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [86.372002, 219.25256000000002, 131.65231, 227.62720000000002], "spans": [[6, 0]], "text": "Page-header", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [151.07401, 219.25256000000002, 159.41275, 227.62720000000002], "spans": [[6, 1]], "text": "72", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [177.23795, 219.25256000000002, 185.57669, 227.62720000000002], "spans": [[6, 2]], "text": "68", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [213.91052, 219.25256000000002, 216.94116, 227.62720000000002], "spans": [[6, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [247.92905000000002, 219.25256000000002, 250.95969, 227.62720000000002], "spans": [[6, 4]], "text": "-", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 6, "row-header": false, "row-span": [6, 7]}], [{"bbox": [86.372002, 230.21155, 112.35663, 238.58618], "spans": [[7, 0]], "text": "Picture", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 7, "row-header": true, "row-span": [7, 8]}, {"bbox": [151.07401, 230.21155, 159.41275, 238.58618], "spans": [[7, 1]], "text": "72", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [177.23795, 230.21155, 185.57669, 238.58618], "spans": [[7, 2]], "text": "72", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [211.25645, 230.21155, 219.5952, 238.58618], "spans": [[7, 3]], "text": "72", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [245.27496, 230.21155, 253.61371, 238.58618], "spans": [[7, 4]], "text": "72", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 7, "row-header": false, "row-span": [7, 8]}], [{"bbox": [86.372002, 241.**************, 141.11188, 249.5441**********], "spans": [[8, 0]], "text": "Section-header", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 8, "row-header": true, "row-span": [8, 9]}, {"bbox": [151.07401, 241.**************, 159.41275, 249.5441**********], "spans": [[8, 1]], "text": "68", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [177.23795, 241.**************, 185.57669, 249.5441**********], "spans": [[8, 2]], "text": "67", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [211.25645, 241.**************, 219.5952, 249.5441**********], "spans": [[8, 3]], "text": "69", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [245.27496, 241.**************, 253.61371, 249.5441**********], "spans": [[8, 4]], "text": "68", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 8, "row-header": false, "row-span": [8, 9]}], [{"bbox": [86.372002, 252.12854000000004, 106.17878, 260.50316999999995], "spans": [[9, 0]], "text": "Table", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 9, "row-header": true, "row-span": [9, 10]}, {"bbox": [151.07401, 252.12854000000004, 159.41275, 260.50316999999995], "spans": [[9, 1]], "text": "82", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [177.23795, 252.12854000000004, 185.57669, 260.50316999999995], "spans": [[9, 2]], "text": "83", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [211.25645, 252.12854000000004, 219.5952, 260.50316999999995], "spans": [[9, 3]], "text": "82", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [245.27496, 252.12854000000004, 253.61371, 260.50316999999995], "spans": [[9, 4]], "text": "82", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 9, "row-header": false, "row-span": [9, 10]}], [{"bbox": [86.372002, 263.08752000000004, 102.3322, 271.46216000000004], "spans": [[10, 0]], "text": "Text", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 10, "row-header": true, "row-span": [10, 11]}, {"bbox": [151.07401, 263.08752000000004, 159.41275, 271.46216000000004], "spans": [[10, 1]], "text": "85", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [177.23795, 263.08752000000004, 185.57669, 271.46216000000004], "spans": [[10, 2]], "text": "84", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [211.25645, 263.08752000000004, 219.5952, 271.46216000000004], "spans": [[10, 3]], "text": "84", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [245.27496, 263.08752000000004, 253.61371, 271.46216000000004], "spans": [[10, 4]], "text": "84", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 10, "row-header": false, "row-span": [10, 11]}], [{"bbox": [86.372002, 274.04657, 103.36333, 282.42117], "spans": [[11, 0]], "text": "Title", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 11, "row-header": true, "row-span": [11, 12]}, {"bbox": [151.07401, 274.04657, 159.41275, 282.42117], "spans": [[11, 1]], "text": "77", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [169.37442, 274.04657, 193.43127, 282.42117], "spans": [[11, 2]], "text": "Sec.-h.", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [203.39294, 274.04657, 227.4498, 282.42117], "spans": [[11, 3]], "text": "Sec.-h.", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [237.41147, 274.04657, 261.46832, 282.42117], "spans": [[11, 4]], "text": "Sec.-h.", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 11, "row-header": false, "row-span": [11, 12]}], [{"bbox": [86.372002, 285.40454, 113.31602000000001, 293.77917], "spans": [[12, 0]], "text": "Overall", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 12, "row-header": true, "row-span": [12, 13]}, {"bbox": [151.07401, 285.40454, 159.41275, 293.77917], "spans": [[12, 1]], "text": "72", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [177.23795, 285.40454, 185.57669, 293.77917], "spans": [[12, 2]], "text": "73", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [211.25645, 285.40454, 219.5952, 293.77917], "spans": [[12, 3]], "text": "78", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [245.27496, 285.40454, 253.61371, 293.77917], "spans": [[12, 4]], "text": "77", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 12, "row-header": false, "row-span": [12, 13]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [352.97747802734375, 485.7341613769531, 522.9158935546875, 641.208740234375], "page": 7, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 5, "#-rows": 14, "data": [[{"bbox": [358.63901, 153.10051999999996, 401.73154, 161.47515999999996], "spans": [[0, 0]], "text": "Class-count", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [440.22501, 153.10051999999996, 448.56375, 161.47515999999996], "spans": [[0, 1], [0, 2]], "text": "11", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 3], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [440.22501, 153.10051999999996, 448.56375, 161.47515999999996], "spans": [[0, 1], [0, 2]], "text": "11", "type": "col_header", "col": 2, "col-header": true, "col-span": [1, 3], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [494.38, 153.10051999999996, 498.54938, 161.47515999999996], "spans": [[0, 3], [0, 4]], "text": "5", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [494.38, 153.10051999999996, 498.54938, 161.47515999999996], "spans": [[0, 3], [0, 4]], "text": "5", "type": "col_header", "col": 4, "col-header": true, "col-span": [3, 5], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [358.63901, 164.05951000000005, 375.27167, 172.43413999999996], "spans": [[1, 0]], "text": "Split", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [423.341, 164.05951000000005, 438.0459, 172.43413999999996], "spans": [[1, 1]], "text": "Doc", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [448.00757, 164.05951000000005, 465.4472, 172.43413999999996], "spans": [[1, 2]], "text": "Page", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [475.41101, 164.05951000000005, 490.11591, 172.43413999999996], "spans": [[1, 3]], "text": "Doc", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [500.07757999999995, 164.05951000000005, 517.51721, 172.43413999999996], "spans": [[1, 4]], "text": "Page", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [358.63901, 175.41656, 387.82465, 183.7912], "spans": [[2, 0]], "text": "Caption", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [426.52399, 175.41656, 434.86273, 183.7912], "spans": [[2, 1]], "text": "68", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [452.56240999999994, 175.41656, 460.90115000000003, 183.7912], "spans": [[2, 2]], "text": "83", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": null, "spans": [[2, 3]], "text": "", "type": "body"}, {"bbox": null, "spans": [[2, 4]], "text": "", "type": "body"}], [{"bbox": [358.63901, 186.37554999999998, 391.14221, 194.75018], "spans": [[3, 0]], "text": "Footnote", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [426.52399, 186.37554999999998, 434.86273, 194.75018], "spans": [[3, 1]], "text": "71", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [452.56240999999994, 186.37554999999998, 460.90115000000003, 194.75018], "spans": [[3, 2]], "text": "84", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": null, "spans": [[3, 3]], "text": "", "type": "body"}, {"bbox": null, "spans": [[3, 4]], "text": "", "type": "body"}], [{"bbox": [358.63901, 197.33452999999997, 389.15167, 205.70916999999997], "spans": [[4, 0]], "text": "Formula", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [426.52399, 197.33452999999997, 434.86273, 205.70916999999997], "spans": [[4, 1]], "text": "60", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [452.56240999999994, 197.33452999999997, 460.90115000000003, 205.70916999999997], "spans": [[4, 2]], "text": "66", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": null, "spans": [[4, 3]], "text": "", "type": "body"}, {"bbox": null, "spans": [[4, 4]], "text": "", "type": "body"}], [{"bbox": [358.63901, 208.29351999999994, 391.5188, 216.66814999999997], "spans": [[5, 0]], "text": "List-item", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [426.52399, 208.29351999999994, 434.86273, 216.66814999999997], "spans": [[5, 1]], "text": "81", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [452.56240999999994, 208.29351999999994, 460.90115000000003, 216.66814999999997], "spans": [[5, 2]], "text": "88", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [478.59399, 208.29351999999994, 486.93274, 216.66814999999997], "spans": [[5, 3]], "text": "82", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [504.6324200000001, 208.29351999999994, 512.97119, 216.66814999999997], "spans": [[5, 4]], "text": "88", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [358.63901, 219.25256000000002, 401.16666, 227.62720000000002], "spans": [[6, 0]], "text": "Page-footer", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [426.52399, 219.25256000000002, 434.86273, 227.62720000000002], "spans": [[6, 1]], "text": "62", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [452.56240999999994, 219.25256000000002, 460.90115000000003, 227.62720000000002], "spans": [[6, 2]], "text": "89", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": null, "spans": [[6, 3]], "text": "", "type": "body"}, {"bbox": null, "spans": [[6, 4]], "text": "", "type": "body"}], [{"bbox": [358.63901, 230.21155, 403.91931, 238.58618], "spans": [[7, 0]], "text": "Page-header", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 7, "row-header": true, "row-span": [7, 8]}, {"bbox": [426.52399, 230.21155, 434.86273, 238.58618], "spans": [[7, 1]], "text": "72", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [452.56240999999994, 230.21155, 460.90115000000003, 238.58618], "spans": [[7, 2]], "text": "90", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": null, "spans": [[7, 3]], "text": "", "type": "body"}, {"bbox": null, "spans": [[7, 4]], "text": "", "type": "body"}], [{"bbox": [358.63901, 241.**************, 384.62366, 249.5441**********], "spans": [[8, 0]], "text": "Picture", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 8, "row-header": true, "row-span": [8, 9]}, {"bbox": [426.52399, 241.**************, 434.86273, 249.5441**********], "spans": [[8, 1]], "text": "72", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [452.56240999999994, 241.**************, 460.90115000000003, 249.5441**********], "spans": [[8, 2]], "text": "82", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [478.59399, 241.**************, 486.93274, 249.5441**********], "spans": [[8, 3]], "text": "72", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [504.6324200000001, 241.**************, 512.97119, 249.5441**********], "spans": [[8, 4]], "text": "82", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 8, "row-header": false, "row-span": [8, 9]}], [{"bbox": [358.63901, 252.12854000000004, 413.37891, 260.50316999999995], "spans": [[9, 0]], "text": "Section-header", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 9, "row-header": true, "row-span": [9, 10]}, {"bbox": [426.52399, 252.12854000000004, 434.86273, 260.50316999999995], "spans": [[9, 1]], "text": "68", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [452.56240999999994, 252.12854000000004, 460.90115000000003, 260.50316999999995], "spans": [[9, 2]], "text": "83", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [478.59399, 252.12854000000004, 486.93274, 260.50316999999995], "spans": [[9, 3]], "text": "69", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [504.6324200000001, 252.12854000000004, 512.97119, 260.50316999999995], "spans": [[9, 4]], "text": "83", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 9, "row-header": false, "row-span": [9, 10]}], [{"bbox": [358.63901, 263.08752000000004, 378.44577, 271.46216000000004], "spans": [[10, 0]], "text": "Table", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 10, "row-header": true, "row-span": [10, 11]}, {"bbox": [426.52399, 263.08752000000004, 434.86273, 271.46216000000004], "spans": [[10, 1]], "text": "82", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [452.56240999999994, 263.08752000000004, 460.90115000000003, 271.46216000000004], "spans": [[10, 2]], "text": "89", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [478.59399, 263.08752000000004, 486.93274, 271.46216000000004], "spans": [[10, 3]], "text": "82", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [504.6324200000001, 263.08752000000004, 512.97119, 271.46216000000004], "spans": [[10, 4]], "text": "90", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 10, "row-header": false, "row-span": [10, 11]}], [{"bbox": [358.63901, 274.04657, 374.59921, 282.42117], "spans": [[11, 0]], "text": "Text", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 11, "row-header": true, "row-span": [11, 12]}, {"bbox": [426.52399, 274.04657, 434.86273, 282.42117], "spans": [[11, 1]], "text": "85", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [452.56240999999994, 274.04657, 460.90115000000003, 282.42117], "spans": [[11, 2]], "text": "91", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [478.59399, 274.04657, 486.93274, 282.42117], "spans": [[11, 3]], "text": "84", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [504.6324200000001, 274.04657, 512.97119, 282.42117], "spans": [[11, 4]], "text": "90", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 11, "row-header": false, "row-span": [11, 12]}], [{"bbox": [358.63901, 285.00552, 375.63034, 293.38015999999993], "spans": [[12, 0]], "text": "Title", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 12, "row-header": true, "row-span": [12, 13]}, {"bbox": [426.52399, 285.00552, 434.86273, 293.38015999999993], "spans": [[12, 1]], "text": "77", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [452.56240999999994, 285.00552, 460.90115000000003, 293.38015999999993], "spans": [[12, 2]], "text": "81", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": null, "spans": [[12, 3]], "text": "", "type": "body"}, {"bbox": null, "spans": [[12, 4]], "text": "", "type": "body"}], [{"bbox": [358.63901, 296.36255, 369.60492, 304.73718], "spans": [[13, 0]], "text": "All", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 13, "row-header": true, "row-span": [13, 14]}, {"bbox": [426.52399, 296.36255, 434.86273, 304.73718], "spans": [[13, 1]], "text": "72", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [452.56240999999994, 296.36255, 460.90115000000003, 304.73718], "spans": [[13, 2]], "text": "84", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [478.59399, 296.36255, 486.93274, 304.73718], "spans": [[13, 3]], "text": "78", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [504.6324200000001, 296.36255, 512.97119, 304.73718], "spans": [[13, 4]], "text": "87", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 13, "row-header": false, "row-span": [13, 14]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [72.6590347290039, 452.1459655761719, 274.83465576171875, 619.5191955566406], "page": 8, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 5, "#-rows": 15, "data": [[{"bbox": null, "spans": [[0, 0]], "text": "", "type": "body"}, {"bbox": null, "spans": [[0, 1]], "text": "", "type": "body"}, {"bbox": [217.74099999999999, 175.01855, 256.26065, 183.39319], "spans": [[0, 2], [0, 3], [0, 4]], "text": "Testing on", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [217.74099999999999, 175.01855, 256.26065, 183.39319], "spans": [[0, 2], [0, 3], [0, 4]], "text": "Testing on", "type": "col_header", "col": 3, "col-header": true, "col-span": [2, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [217.74099999999999, 175.01855, 256.26065, 183.39319], "spans": [[0, 2], [0, 3], [0, 4]], "text": "Testing on", "type": "col_header", "col": 4, "col-header": true, "col-span": [2, 5], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [89.954002, 185.97655999999995, 133.24379, 194.35119999999995], "spans": [[1, 0]], "text": "Training on", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [154.629, 185.97655999999995, 175.47588, 194.35119999999995], "spans": [[1, 1]], "text": "labels", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [204.69, 185.97655999999995, 220.54260000000002, 194.35119999999995], "spans": [[1, 2]], "text": "PLN", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [230.50427000000002, 185.97655999999995, 242.06197, 194.35119999999995], "spans": [[1, 3]], "text": "DB", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [252.02364, 185.97655999999995, 269.31085, 194.35119999999995], "spans": [[1, 4]], "text": "DLN", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [78.530998, 219.25256000000002, 142.56006, 227.62720000000002], "spans": [[2, 0], [3, 0]], "text": "PubLayNet (PLN)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 4]}, {"bbox": [154.629, 197.33452999999997, 177.92371, 205.70916999999997], "spans": [[2, 1]], "text": "Figure", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [208.44701, 197.33452999999997, 216.**************, 205.70916999999997], "spans": [[2, 2]], "text": "96", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [232.1183, 197.33452999999997, 240.45705, 205.70916999999997], "spans": [[2, 3]], "text": "43", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [256.49792, 197.33452999999997, 264.83667, 205.70916999999997], "spans": [[2, 4]], "text": "23", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [78.530998, 219.25256000000002, 142.56006, 227.62720000000002], "spans": [[2, 0], [3, 0]], "text": "PubLayNet (PLN)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [2, 4]}, {"bbox": [154.629, 208.29351999999994, 194.72675, 216.66814999999997], "spans": [[3, 1]], "text": "Sec-header", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [208.44701, 208.29351999999994, 216.**************, 216.66814999999997], "spans": [[3, 2]], "text": "87", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [234.77235, 208.29351999999994, 237.80299000000002, 216.66814999999997], "spans": [[3, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [256.49792, 208.29351999999994, 264.83667, 216.66814999999997], "spans": [[3, 4]], "text": "32", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": null, "spans": [[4, 0]], "text": "", "type": "body"}, {"bbox": [154.629, 219.25256000000002, 174.43578, 227.62720000000002], "spans": [[4, 1]], "text": "Table", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [208.44701, 219.25256000000002, 216.**************, 227.62720000000002], "spans": [[4, 2]], "text": "95", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [232.1183, 219.25256000000002, 240.45705, 227.62720000000002], "spans": [[4, 3]], "text": "24", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [256.49792, 219.25256000000002, 264.83667, 227.62720000000002], "spans": [[4, 4]], "text": "49", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": null, "spans": [[5, 0]], "text": "", "type": "body"}, {"bbox": [154.629, 230.21155, 170.58919, 238.58618], "spans": [[5, 1]], "text": "Text", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [208.44701, 230.21155, 216.**************, 238.58618], "spans": [[5, 2]], "text": "96", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [234.77235, 230.21155, 237.80299000000002, 238.58618], "spans": [[5, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [256.49792, 230.21155, 264.83667, 238.58618], "spans": [[5, 4]], "text": "42", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": null, "spans": [[6, 0]], "text": "", "type": "body"}, {"bbox": [154.629, 241.**************, 171.2796, 249.5441**********], "spans": [[6, 1]], "text": "total", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [208.44701, 241.**************, 216.**************, 249.5441**********], "spans": [[6, 2]], "text": "93", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [232.1183, 241.**************, 240.45705, 249.5441**********], "spans": [[6, 3]], "text": "34", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [256.49792, 241.**************, 264.83667, 249.5441**********], "spans": [[6, 4]], "text": "30", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 6, "row-header": false, "row-span": [6, 7]}], [{"bbox": [78.530998, 263.**************, 131.19963, 271.**************], "spans": [[7, 0], [8, 0], [9, 0]], "text": "DocBank (DB)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 7, "row-header": true, "row-span": [7, 10]}, {"bbox": [154.629, 252.**************, 177.92371, 260.90216], "spans": [[7, 1]], "text": "Figure", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 7, "row-header": true, "row-span": [7, 8]}, {"bbox": [208.44701, 252.**************, 216.**************, 260.90216], "spans": [[7, 2]], "text": "77", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [232.1183, 252.**************, 240.45705, 260.90216], "spans": [[7, 3]], "text": "71", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [256.49792, 252.**************, 264.83667, 260.90216], "spans": [[7, 4]], "text": "31", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 7, "row-header": false, "row-span": [7, 8]}], [{"bbox": [78.530998, 263.**************, 131.19963, 271.**************], "spans": [[7, 0], [8, 0], [9, 0]], "text": "DocBank (DB)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 8, "row-header": true, "row-span": [7, 10]}, {"bbox": [154.629, 263.**************, 174.43578, 271.**************], "spans": [[8, 1]], "text": "Table", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 8, "row-header": true, "row-span": [8, 9]}, {"bbox": [208.44701, 263.**************, 216.**************, 271.**************], "spans": [[8, 2]], "text": "19", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [232.1183, 263.**************, 240.45705, 271.**************], "spans": [[8, 3]], "text": "65", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [256.49792, 263.**************, 264.83667, 271.**************], "spans": [[8, 4]], "text": "22", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 8, "row-header": false, "row-span": [8, 9]}], [{"bbox": [78.530998, 263.**************, 131.19963, 271.**************], "spans": [[7, 0], [8, 0], [9, 0]], "text": "DocBank (DB)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 9, "row-header": true, "row-span": [7, 10]}, {"bbox": [154.629, 274.44556, 171.2796, 282.82016], "spans": [[9, 1]], "text": "total", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 9, "row-header": true, "row-span": [9, 10]}, {"bbox": [208.44701, 274.44556, 216.**************, 282.82016], "spans": [[9, 2]], "text": "48", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [232.1183, 274.44556, 240.45705, 282.82016], "spans": [[9, 3]], "text": "68", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [256.49792, 274.44556, 264.83667, 282.82016], "spans": [[9, 4]], "text": "27", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 9, "row-header": false, "row-span": [9, 10]}], [{"bbox": [78.530998, 307.72055, 144.66716, 316.09517999999997], "spans": [[10, 0], [11, 0]], "text": "DocLayNet (DLN)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 10, "row-header": true, "row-span": [10, 12]}, {"bbox": [154.629, 285.80255, 177.92371, 294.17719000000005], "spans": [[10, 1]], "text": "Figure", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 10, "row-header": true, "row-span": [10, 11]}, {"bbox": [208.44701, 285.80255, 216.**************, 294.17719000000005], "spans": [[10, 2]], "text": "67", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [232.1183, 285.80255, 240.45705, 294.17719000000005], "spans": [[10, 3]], "text": "51", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [256.49792, 285.80255, 264.83667, 294.17719000000005], "spans": [[10, 4]], "text": "72", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 10, "row-header": false, "row-span": [10, 11]}], [{"bbox": [78.530998, 307.72055, 144.66716, 316.09517999999997], "spans": [[10, 0], [11, 0]], "text": "DocLayNet (DLN)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 11, "row-header": true, "row-span": [10, 12]}, {"bbox": [154.629, 296.76154, 194.72675, 305.13617], "spans": [[11, 1]], "text": "Sec-header", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 11, "row-header": true, "row-span": [11, 12]}, {"bbox": [208.44701, 296.76154, 216.**************, 305.13617], "spans": [[11, 2]], "text": "53", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [234.77235, 296.76154, 237.80299000000002, 305.13617], "spans": [[11, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": [256.49792, 296.76154, 264.83667, 305.13617], "spans": [[11, 4]], "text": "68", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 11, "row-header": false, "row-span": [11, 12]}], [{"bbox": null, "spans": [[12, 0]], "text": "", "type": "body"}, {"bbox": [154.629, 307.72055, 174.43578, 316.09517999999997], "spans": [[12, 1]], "text": "Table", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 12, "row-header": true, "row-span": [12, 13]}, {"bbox": [208.44701, 307.72055, 216.**************, 316.09517999999997], "spans": [[12, 2]], "text": "87", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [232.1183, 307.72055, 240.45705, 316.09517999999997], "spans": [[12, 3]], "text": "43", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": [256.49792, 307.72055, 264.83667, 316.09517999999997], "spans": [[12, 4]], "text": "82", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 12, "row-header": false, "row-span": [12, 13]}], [{"bbox": null, "spans": [[13, 0]], "text": "", "type": "body"}, {"bbox": [154.629, 318.67953, 170.58919, 327.05417], "spans": [[13, 1]], "text": "Text", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 13, "row-header": true, "row-span": [13, 14]}, {"bbox": [208.44701, 318.67953, 216.**************, 327.05417], "spans": [[13, 2]], "text": "77", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [234.77235, 318.67953, 237.80299000000002, 327.05417], "spans": [[13, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 13, "row-header": false, "row-span": [13, 14]}, {"bbox": [256.49792, 318.67953, 264.83667, 327.05417], "spans": [[13, 4]], "text": "84", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 13, "row-header": false, "row-span": [13, 14]}], [{"bbox": null, "spans": [[14, 0]], "text": "", "type": "body"}, {"bbox": [154.629, 329.63855, 171.2796, 338.01318], "spans": [[14, 1]], "text": "total", "type": "row_header", "col": 1, "col-header": false, "col-span": [1, 2], "row": 14, "row-header": true, "row-span": [14, 15]}, {"bbox": [208.44701, 329.63855, 216.**************, 338.01318], "spans": [[14, 2]], "text": "59", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 14, "row-header": false, "row-span": [14, 15]}, {"bbox": [232.1183, 329.63855, 240.45705, 338.01318], "spans": [[14, 3]], "text": "47", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 14, "row-header": false, "row-span": [14, 15]}, {"bbox": [256.49792, 329.63855, 264.83667, 338.01318], "spans": [[14, 4]], "text": "78", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 14, "row-header": false, "row-span": [14, 15]}]], "model": null, "bounding-box": null}], "bitmaps": null, "equations": [], "footnotes": [], "page-dimensions": [{"height": 792.0, "page": 1, "width": 612.0}, {"height": 792.0, "page": 2, "width": 612.0}, {"height": 792.0, "page": 3, "width": 612.0}, {"height": 792.0, "page": 4, "width": 612.0}, {"height": 792.0, "page": 5, "width": 612.0}, {"height": 792.0, "page": 6, "width": 612.0}, {"height": 792.0, "page": 7, "width": 612.0}, {"height": 792.0, "page": 8, "width": 612.0}, {"height": 792.0, "page": 9, "width": 612.0}], "page-footers": [], "page-headers": [], "_s3_data": null, "identifiers": null}