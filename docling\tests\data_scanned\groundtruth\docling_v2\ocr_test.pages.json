[{"page_no": 0, "size": {"width": 595.201171875, "height": 841.9216918945312}, "parsed_page": {"dimension": {"angle": 0.0, "rect": {"r_x0": 0.0, "r_y0": 0.0, "r_x1": 595.201171875, "r_y1": 0.0, "r_x2": 595.201171875, "r_y2": 841.9216918945312, "r_x3": 0.0, "r_y3": 841.9216918945312, "coord_origin": "BOTTOMLEFT"}, "boundary_type": "crop_box", "art_bbox": {"l": 0.0, "t": 841.9216918945312, "r": 595.201171875, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "bleed_bbox": {"l": 0.0, "t": 841.9216918945312, "r": 595.201171875, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "crop_bbox": {"l": 0.0, "t": 841.9216918945312, "r": 595.201171875, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "media_bbox": {"l": 0.0, "t": 841.9216918945312, "r": 595.201171875, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "trim_bbox": {"l": 0.0, "t": 841.9216918945312, "r": 595.201171875, "b": 0.0, "coord_origin": "BOTTOMLEFT"}}, "bitmap_resources": [], "char_cells": [], "word_cells": [], "textline_cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 73.34702132031646, "r_y0": 97.99999977896755, "r_x1": 503.64955224479564, "r_y1": 97.99999977896755, "r_x2": 503.64955224479564, "r_y2": 76.99999977896756, "r_x3": 73.34702132031646, "r_y3": 76.99999977896756, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 69.6796630536824, "r_y0": 124.83139494707741, "r_x1": 504.8720051760782, "r_y1": 124.83139494707741, "r_x2": 504.8720051760782, "r_y2": 104.00000011573796, "r_x3": 69.6796630536824, "r_y3": 104.00000011573796, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 71.84193505100733, "r_y0": 152.90926970226084, "r_x1": 153.088934155825, "r_y1": 152.90926970226084, "r_x2": 153.088934155825, "r_y2": 129.797125232046, "r_x3": 71.84193505100733, "r_y3": 129.797125232046, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "has_chars": false, "has_words": false, "has_lines": true, "image": null, "lines": []}, "predictions": {"layout": {"clusters": [{"id": 0, "label": "text", "bbox": {"l": 69.6796630536824, "t": 76.99999977896756, "r": 504.8720051760782, "b": 152.90926970226084, "coord_origin": "TOPLEFT"}, "confidence": 0.9715733528137207, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 73.34702132031646, "r_y0": 97.99999977896755, "r_x1": 503.64955224479564, "r_y1": 97.99999977896755, "r_x2": 503.64955224479564, "r_y2": 76.99999977896756, "r_x3": 73.34702132031646, "r_y3": 76.99999977896756, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 69.6796630536824, "r_y0": 124.83139494707741, "r_x1": 504.8720051760782, "r_y1": 124.83139494707741, "r_x2": 504.8720051760782, "r_y2": 104.00000011573796, "r_x3": 69.6796630536824, "r_y3": 104.00000011573796, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 71.84193505100733, "r_y0": 152.90926970226084, "r_x1": 153.088934155825, "r_y1": 152.90926970226084, "r_x2": 153.088934155825, "r_y2": 129.797125232046, "r_x3": 71.84193505100733, "r_y3": 129.797125232046, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}]}, "tablestructure": {"table_map": {}}, "figures_classification": null, "equations_prediction": null, "vlm_response": null}, "assembled": {"elements": [{"label": "text", "id": 0, "page_no": 0, "cluster": {"id": 0, "label": "text", "bbox": {"l": 69.6796630536824, "t": 76.99999977896756, "r": 504.8720051760782, "b": 152.90926970226084, "coord_origin": "TOPLEFT"}, "confidence": 0.9715733528137207, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 73.34702132031646, "r_y0": 97.99999977896755, "r_x1": 503.64955224479564, "r_y1": 97.99999977896755, "r_x2": 503.64955224479564, "r_y2": 76.99999977896756, "r_x3": 73.34702132031646, "r_y3": 76.99999977896756, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 69.6796630536824, "r_y0": 124.83139494707741, "r_x1": 504.8720051760782, "r_y1": 124.83139494707741, "r_x2": 504.8720051760782, "r_y2": 104.00000011573796, "r_x3": 69.6796630536824, "r_y3": 104.00000011573796, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 71.84193505100733, "r_y0": 152.90926970226084, "r_x1": 153.088934155825, "r_y1": 152.90926970226084, "r_x2": 153.088934155825, "r_y2": 129.797125232046, "r_x3": 71.84193505100733, "r_y3": 129.797125232046, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "Docling bundles PDF document conversion to JSON and Markdown in an easy self contained package"}], "body": [{"label": "text", "id": 0, "page_no": 0, "cluster": {"id": 0, "label": "text", "bbox": {"l": 69.6796630536824, "t": 76.99999977896756, "r": 504.8720051760782, "b": 152.90926970226084, "coord_origin": "TOPLEFT"}, "confidence": 0.9715733528137207, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 73.34702132031646, "r_y0": 97.99999977896755, "r_x1": 503.64955224479564, "r_y1": 97.99999977896755, "r_x2": 503.64955224479564, "r_y2": 76.99999977896756, "r_x3": 73.34702132031646, "r_y3": 76.99999977896756, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 69.6796630536824, "r_y0": 124.83139494707741, "r_x1": 504.8720051760782, "r_y1": 124.83139494707741, "r_x2": 504.8720051760782, "r_y2": 104.00000011573796, "r_x3": 69.6796630536824, "r_y3": 104.00000011573796, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 71.84193505100733, "r_y0": 152.90926970226084, "r_x1": 153.088934155825, "r_y1": 152.90926970226084, "r_x2": 153.088934155825, "r_y2": 129.797125232046, "r_x3": 71.84193505100733, "r_y3": 129.797125232046, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "Docling bundles PDF document conversion to JSON and Markdown in an easy self contained package"}], "headers": []}}]