# FastAPI OCR API Requirements
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
pydantic>=2.0.0

# Docling and VLM dependencies
docling>=2.0.0
torch>=2.0.0
transformers>=4.40.0
accelerate>=0.20.0
Pillow>=10.0.0

# Optional: MLX for Apple Silicon (uncomment if using Apple Silicon)
# mlx>=0.15.0
# mlx-lm>=0.15.0

# Optional: CUDA support (uncomment if using NVIDIA GPU)
# torch-audio  # For CUDA support

# Development dependencies (optional)
# pytest>=7.0.0
# httpx>=0.25.0  # For testing API endpoints
