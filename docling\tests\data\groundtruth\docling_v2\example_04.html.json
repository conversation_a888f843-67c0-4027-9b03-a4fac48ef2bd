{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "example_04", "origin": {"mimetype": "text/html", "binary_hash": 2846345769602286603, "filename": "example_04.html"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/tables/0"}], "content_layer": "body", "label": "title", "prov": [], "orig": "Data Table with Rowspan and Colspan", "text": "Data Table with Rowspan and Colspan"}], "pictures": [], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "table", "prov": [], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Header 2 & 3 (co<PERSON><PERSON>)", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Row 1 & 2, Col 1 (rowspan)", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Row 1, <PERSON> 2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Row 1, Col 3", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Row 2, Col 2 & 3 (co<PERSON><PERSON>)", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON> 3, <PERSON> 1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "<PERSON> 3, <PERSON> 2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "<PERSON> 3, <PERSON> 3", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 4, "num_cols": 3, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Header 2 & 3 (co<PERSON><PERSON>)", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Header 2 & 3 (co<PERSON><PERSON>)", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 2, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Row 1 & 2, Col 1 (rowspan)", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Row 1, <PERSON> 2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Row 1, Col 3", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 2, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Row 1 & 2, Col 1 (rowspan)", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Row 2, Col 2 & 3 (co<PERSON><PERSON>)", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Row 2, Col 2 & 3 (co<PERSON><PERSON>)", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON> 3, <PERSON> 1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "<PERSON> 3, <PERSON> 2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "<PERSON> 3, <PERSON> 3", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {}}