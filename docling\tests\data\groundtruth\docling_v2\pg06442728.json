{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "pg06442728.xml", "origin": {"mimetype": "application/xml", "binary_hash": 17070828553291841850, "filename": "pg06442728.xml"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/1"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/5"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/18"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/72"}], "content_layer": "body", "label": "title", "prov": [], "orig": "Methods and apparatus for turbo code", "text": "Methods and apparatus for turbo code"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/2"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "ABSTRACT", "text": "ABSTRACT", "level": 2}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/texts/1"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "An interleaver receives incoming data frames of size N. The interleaver indexes the elements of the frame with an N₁×N₂ index array. The interleaver then effectively rearranges (permutes) the data by permuting the rows of the index array. The interleaver employs the equation I(j,k)=I(j,αjk+βj)modP) to permute the columns (indexed by k) of each row (indexed by j). P is at least equal to N₂, βj is a constant which may be different for each row, and each αj is a relative prime number relative to P. After permuting, the interleaver outputs the data in a different order than received (e.g., receives sequentially row by row, outputs sequentially each column by column).", "text": "An interleaver receives incoming data frames of size N. The interleaver indexes the elements of the frame with an N₁×N₂ index array. The interleaver then effectively rearranges (permutes) the data by permuting the rows of the index array. The interleaver employs the equation I(j,k)=I(j,αjk+βj)modP) to permute the columns (indexed by k) of each row (indexed by j). P is at least equal to N₂, βj is a constant which may be different for each row, and each αj is a relative prime number relative to P. After permuting, the interleaver outputs the data in a different order than received (e.g., receives sequentially row by row, outputs sequentially each column by column)."}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/4"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "CROSS-REFERENCE TO RELATED APPLICATIONS", "text": "CROSS-REFERENCE TO RELATED APPLICATIONS", "level": 2}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/texts/3"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "This application claims the benefit of U.S. Provisional Application No. 60/115,394 filed Jan. 11, 1999.", "text": "This application claims the benefit of U.S. Provisional Application No. 60/115,394 filed Jan. 11, 1999."}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/6"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "FIELD OF THE INVENTION", "text": "FIELD OF THE INVENTION", "level": 2}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/texts/5"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "This invention relates generally to communication systems and, more particularly, to interleavers for performing code modulation.", "text": "This invention relates generally to communication systems and, more particularly, to interleavers for performing code modulation."}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}, {"$ref": "#/texts/17"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "BACKGROUND OF THE INVENTION", "text": "BACKGROUND OF THE INVENTION", "level": 2}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Techniques for encoding communication channels, known as coded modulation, have been found to improve the bit error rate (BER) of electronic communication systems such as modem and wireless communication systems. Turbo coded modulation has proven to be a practical, power-efficient, and bandwidth-efficient modulation method for “random-error” channels characterized by additive white Gaussian noise (AWGN) or fading. These random-error channels can be found, for example, in the code division multiple access (CDMA) environment. Since the capacity of a CDMA environment is dependent upon the operating signal to noise ratio, improved performance translates into higher capacity.", "text": "Techniques for encoding communication channels, known as coded modulation, have been found to improve the bit error rate (BER) of electronic communication systems such as modem and wireless communication systems. Turbo coded modulation has proven to be a practical, power-efficient, and bandwidth-efficient modulation method for “random-error” channels characterized by additive white Gaussian noise (AWGN) or fading. These random-error channels can be found, for example, in the code division multiple access (CDMA) environment. Since the capacity of a CDMA environment is dependent upon the operating signal to noise ratio, improved performance translates into higher capacity."}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "An aspect of turbo coders which makes them so effective is an interleaver which permutes the original received or transmitted data frame before it is input to a second encoder. The permuting is accomplished by randomizing portions of the signal based upon one or more randomizing algorithms. Combining the permuted data frames with the original data frames has been shown to achieve low BERs in AWGN and fading channels. The interleaving process increases the diversity in the data such that if the modulated symbol is distorted in transmission the error may be recoverable with the use of error correcting algorithms in the decoder.", "text": "An aspect of turbo coders which makes them so effective is an interleaver which permutes the original received or transmitted data frame before it is input to a second encoder. The permuting is accomplished by randomizing portions of the signal based upon one or more randomizing algorithms. Combining the permuted data frames with the original data frames has been shown to achieve low BERs in AWGN and fading channels. The interleaving process increases the diversity in the data such that if the modulated symbol is distorted in transmission the error may be recoverable with the use of error correcting algorithms in the decoder."}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "A conventional interleaver collects, or frames, the signal points to be transmitted into an array, where the array is sequentially filled up row by row. After a predefined number of signal points have been framed, the interleaver is emptied by sequentially reading out the columns of the array for transmission. As a result, signal points in the same row of the array that were near each other in the original signal point flow are separated by a number of signal points equal to the number of rows in the array. Ideally, the number of columns and rows would be picked such that interdependent signal points, after transmission, would be separated by more than the expected length of an error burst for the channel.", "text": "A conventional interleaver collects, or frames, the signal points to be transmitted into an array, where the array is sequentially filled up row by row. After a predefined number of signal points have been framed, the interleaver is emptied by sequentially reading out the columns of the array for transmission. As a result, signal points in the same row of the array that were near each other in the original signal point flow are separated by a number of signal points equal to the number of rows in the array. Ideally, the number of columns and rows would be picked such that interdependent signal points, after transmission, would be separated by more than the expected length of an error burst for the channel."}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Non-uniform interleaving achieves “maximum scattering” of data and “maximum disorder” of the output sequence. Thus the redundancy introduced by the two convolutional encoders is more equally spread in the output sequence of the turbo encoder. The minimum distance is increased to much higher values than for uniform interleaving. A persistent problem for non-uniform interleaving is how to practically implement the interleaving while achieving sufficient “non-uniformity,” and minimizing delay compensations which limit the use for applications with real-time requirements.", "text": "Non-uniform interleaving achieves “maximum scattering” of data and “maximum disorder” of the output sequence. Thus the redundancy introduced by the two convolutional encoders is more equally spread in the output sequence of the turbo encoder. The minimum distance is increased to much higher values than for uniform interleaving. A persistent problem for non-uniform interleaving is how to practically implement the interleaving while achieving sufficient “non-uniformity,” and minimizing delay compensations which limit the use for applications with real-time requirements."}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Finding an effective interleaver is a current topic in the third generation CDMA standard activities. It has been determined and generally agreed that, as the frame size approaches infinity, the most effective interleaver is the random interleaver. However, for finite frame sizes, the decision as to the most effective interleaver is still open for discussion.", "text": "Finding an effective interleaver is a current topic in the third generation CDMA standard activities. It has been determined and generally agreed that, as the frame size approaches infinity, the most effective interleaver is the random interleaver. However, for finite frame sizes, the decision as to the most effective interleaver is still open for discussion."}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Accordingly there exists a need for systems and methods of interleaving codes that improve non-uniformity for finite frame sizes.", "text": "Accordingly there exists a need for systems and methods of interleaving codes that improve non-uniformity for finite frame sizes."}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "There also exists a need for such systems and methods of interleaving codes which are relatively simple to implement.", "text": "There also exists a need for such systems and methods of interleaving codes which are relatively simple to implement."}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "It is thus an object of the present invention to provide systems and methods of interleaving codes that improve non-uniformity for finite frame sizes.", "text": "It is thus an object of the present invention to provide systems and methods of interleaving codes that improve non-uniformity for finite frame sizes."}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "It is also an object of the present invention to provide systems and methods of interleaving codes which are relatively simple to implement.", "text": "It is also an object of the present invention to provide systems and methods of interleaving codes which are relatively simple to implement."}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "These and other objects of the invention will become apparent to those skilled in the art from the following description thereof.", "text": "These and other objects of the invention will become apparent to those skilled in the art from the following description thereof."}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}, {"$ref": "#/texts/22"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "SUMMARY OF THE INVENTION", "text": "SUMMARY OF THE INVENTION", "level": 2}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/texts/18"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "The foregoing objects, and others, may be accomplished by the present invention, which interleaves a data frame, where the data frame has a predetermined size and is made up of portions. An embodiment of the invention includes an interleaver for interleaving these data frames. The interleaver includes an input memory configured to store a received data frame as an array organized into rows and columns, a processor connected to the input memory and configured to permute the received data frame in accordance with the equation D(j,k)=D (j, (αjk+βj)modP), and a working memory in electrical communication with the processor and configured to store a permuted version of the data frame. The elements of the equation are as follows: D is the data frame, j and k are indexes to the rows and columns, respectively, in the data frame, α and β are sets of constants selected according to the current row, and P and each αj are relative prime numbers. (“Relative prime numbers” connotes a set of numbers that have no common divisor other than 1. Members of a set of relative prime numbers, considered by themselves, need not be prime numbers.)", "text": "The foregoing objects, and others, may be accomplished by the present invention, which interleaves a data frame, where the data frame has a predetermined size and is made up of portions. An embodiment of the invention includes an interleaver for interleaving these data frames. The interleaver includes an input memory configured to store a received data frame as an array organized into rows and columns, a processor connected to the input memory and configured to permute the received data frame in accordance with the equation D(j,k)=D (j, (αjk+βj)modP), and a working memory in electrical communication with the processor and configured to store a permuted version of the data frame. The elements of the equation are as follows: D is the data frame, j and k are indexes to the rows and columns, respectively, in the data frame, α and β are sets of constants selected according to the current row, and P and each αj are relative prime numbers. (“Relative prime numbers” connotes a set of numbers that have no common divisor other than 1. Members of a set of relative prime numbers, considered by themselves, need not be prime numbers.)"}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/texts/18"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Another embodiment of the invention includes a method of storing a data frame and indexing it by an N₁×N₂ index array I, where the product of N₁ and N₂ is at least equal to N. The elements of the index array indicate positions of the elements of the data frame. The data frame elements may be stored in any convenient manner and need not be organized as an array. The method further includes permuting the index array according to I(j,k)=I(j,(αjk+βj)modP), wherein I is the index array, and as above j and k are indexes to the rows and columns, respectively, in the index array, α and β are sets of constants selected according to the current row, and P and each αj are relative prime numbers. The data frame, as indexed by the permuted index array I, is effectively permuted.", "text": "Another embodiment of the invention includes a method of storing a data frame and indexing it by an N₁×N₂ index array I, where the product of N₁ and N₂ is at least equal to N. The elements of the index array indicate positions of the elements of the data frame. The data frame elements may be stored in any convenient manner and need not be organized as an array. The method further includes permuting the index array according to I(j,k)=I(j,(αjk+βj)modP), wherein I is the index array, and as above j and k are indexes to the rows and columns, respectively, in the index array, α and β are sets of constants selected according to the current row, and P and each αj are relative prime numbers. The data frame, as indexed by the permuted index array I, is effectively permuted."}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/texts/18"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Still another embodiment of the invention includes an interleaver which includes a storage device for storing a data frame and for storing an N₁×N₂ index array I, where the product of N₁ and N₂ is at least equal to N. The elements of the index array indicate positions of the elements of the data frame. The data frame elements may be stored in any convenient manner and need not be organized as an array. The interleaver further includes a permuting device for permuting the index array according to I(j,k)=I(j,(αjk+βj)modP), wherein I is the index array, and as above j and k are indexes to the rows and columns, respectively, in the index array, α and β are sets of constants selected according to the current row, and P and each αj are relative prime numbers. The data frame, as indexed by the permuted index array I, is effectively permuted.", "text": "Still another embodiment of the invention includes an interleaver which includes a storage device for storing a data frame and for storing an N₁×N₂ index array I, where the product of N₁ and N₂ is at least equal to N. The elements of the index array indicate positions of the elements of the data frame. The data frame elements may be stored in any convenient manner and need not be organized as an array. The interleaver further includes a permuting device for permuting the index array according to I(j,k)=I(j,(αjk+βj)modP), wherein I is the index array, and as above j and k are indexes to the rows and columns, respectively, in the index array, α and β are sets of constants selected according to the current row, and P and each αj are relative prime numbers. The data frame, as indexed by the permuted index array I, is effectively permuted."}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/texts/18"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "The invention will next be described in connection with certain illustrated embodiments and practices. However, it will be clear to those skilled in the art that various modifications, additions and subtractions can be made without departing from the spirit or scope of the claims.", "text": "The invention will next be described in connection with certain illustrated embodiments and practices. However, it will be clear to those skilled in the art that various modifications, additions and subtractions can be made without departing from the spirit or scope of the claims."}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/texts/27"}, {"$ref": "#/texts/28"}, {"$ref": "#/texts/29"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "BRIEF DESCRIPTION OF THE DRAWINGS", "text": "BRIEF DESCRIPTION OF THE DRAWINGS", "level": 2}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/texts/23"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "The invention will be more clearly understood by reference to the following detailed description of an exemplary embodiment in conjunction with the accompanying drawings, in which:", "text": "The invention will be more clearly understood by reference to the following detailed description of an exemplary embodiment in conjunction with the accompanying drawings, in which:"}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/texts/23"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "FIG. 1 depicts a diagram of a conventional turbo encoder.", "text": "FIG. 1 depicts a diagram of a conventional turbo encoder."}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/texts/23"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "FIG. 2 depicts a block diagram of the interleaver illustrated in FIG. 1;", "text": "FIG. 2 depicts a block diagram of the interleaver illustrated in FIG. 1;"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/texts/23"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "FIG. 3 depicts an array containing a data frame, and permutation of that array;", "text": "FIG. 3 depicts an array containing a data frame, and permutation of that array;"}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/texts/23"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "FIG. 4 depicts a data frame stored in consecutive storage locations;", "text": "FIG. 4 depicts a data frame stored in consecutive storage locations;"}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/texts/23"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "FIG. 5 depicts an index array for indexing the data frame shown in FIG. 4, and permutation of the index array.", "text": "FIG. 5 depicts an index array for indexing the data frame shown in FIG. 4, and permutation of the index array."}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}, {"$ref": "#/texts/33"}, {"$ref": "#/texts/34"}, {"$ref": "#/texts/35"}, {"$ref": "#/texts/36"}, {"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}, {"$ref": "#/texts/42"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/44"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}, {"$ref": "#/texts/47"}, {"$ref": "#/texts/48"}, {"$ref": "#/texts/49"}, {"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}, {"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}, {"$ref": "#/texts/54"}, {"$ref": "#/texts/55"}, {"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/texts/60"}, {"$ref": "#/texts/61"}, {"$ref": "#/texts/62"}, {"$ref": "#/texts/63"}, {"$ref": "#/texts/64"}, {"$ref": "#/texts/65"}, {"$ref": "#/texts/66"}, {"$ref": "#/texts/67"}, {"$ref": "#/texts/68"}, {"$ref": "#/texts/69"}, {"$ref": "#/texts/70"}, {"$ref": "#/texts/71"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "DETAILED DESCRIPTION OF THE INVENTION", "text": "DETAILED DESCRIPTION OF THE INVENTION", "level": 2}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "FIG. 1 illustrates a conventional turbo encoder. As illustrated, conventional turbo encoders include two encoders 20 and an interleaver 100. An interleaver 100 in accordance with the present invention receives incoming data frames 110 of size N, where N is the number of bits, number of bytes, or the number of some other portion the frame may be separated into, which are regarded as frame elements. The interleaver 100 separates the N frame elements into sets of data, such as rows. The interleaver then rearranges (permutes) the data in each set (row) in a pseudo-random fashion. The interleaver 100 may employ different methods for rearranging the data of the different sets. However, those skilled in the art will recognize that one or more of the methods could be reused on one or more of the sets without departing from the scope of the invention. After permuting the data in each of the sets, the interleaver outputs the data in a different order than received.", "text": "FIG. 1 illustrates a conventional turbo encoder. As illustrated, conventional turbo encoders include two encoders 20 and an interleaver 100. An interleaver 100 in accordance with the present invention receives incoming data frames 110 of size N, where N is the number of bits, number of bytes, or the number of some other portion the frame may be separated into, which are regarded as frame elements. The interleaver 100 separates the N frame elements into sets of data, such as rows. The interleaver then rearranges (permutes) the data in each set (row) in a pseudo-random fashion. The interleaver 100 may employ different methods for rearranging the data of the different sets. However, those skilled in the art will recognize that one or more of the methods could be reused on one or more of the sets without departing from the scope of the invention. After permuting the data in each of the sets, the interleaver outputs the data in a different order than received."}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "The interleaver 100 may store the data frame 110 in an array of size N₁×N₂ such that N₁*N₂=N. An example depicted in FIG. 3 shows an array 350 having 3 rows (N₁=3) of 6 columns (N₂=6)for storing a data frame 110 having 18 elements, denoted Frame Element 00 (FE00) through FE17 (N=18). While this is the preferred method, the array may also be designed such that N₁*N₂ is a fraction of N such that one or more of the smaller arrays is/are operated on in accordance with the present invention and the results from each of the smaller arrays are later combined.", "text": "The interleaver 100 may store the data frame 110 in an array of size N₁×N₂ such that N₁*N₂=N. An example depicted in FIG. 3 shows an array 350 having 3 rows (N₁=3) of 6 columns (N₂=6)for storing a data frame 110 having 18 elements, denoted Frame Element 00 (FE00) through FE17 (N=18). While this is the preferred method, the array may also be designed such that N₁*N₂ is a fraction of N such that one or more of the smaller arrays is/are operated on in accordance with the present invention and the results from each of the smaller arrays are later combined."}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "To permute array 350 according to the present invention, each row j of array 350 is individually operated on, to permute the columns k of each row according to the equation:", "text": "To permute array 350 according to the present invention, each row j of array 350 is individually operated on, to permute the columns k of each row according to the equation:"}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "D₁(j,k)=D(j,(αk+β)modP)", "text": "D₁(j,k)=D(j,(αk+β)modP)"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "where:", "text": "where:"}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "j and k are row and column indices, respectively, in array 350;", "text": "j and k are row and column indices, respectively, in array 350;"}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "P is a number greater than or equal to N₂;", "text": "P is a number greater than or equal to N₂;"}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "αj and P arc relative prime numbers (one or both can be non-prime numbers, but the only divisor that they have in common is 1);", "text": "αj and P arc relative prime numbers (one or both can be non-prime numbers, but the only divisor that they have in common is 1);"}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "βj is a constant, one value associated with each row.", "text": "βj is a constant, one value associated with each row."}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Once the data for all of the rows are permuted, the new array is read out column by column. Also, once the rows have been permuted, it is possible (but not required) to permute the data grouped by column before outputting the data. In the event that both the rows and columns are permuted, the rows, the columns or both may be permuted in accordance with the present invention. It is also possible to transpose rows of array, for example by transposing bits in the binary representation of the row index j. (In a four-row array, for example, the second and third rows would be transposed under this scheme.) It is also possible that either the rows or the columns, but not both may be permuted in accordance with a different method of permuting. Those skilled in the art will recognize that the system could be rearranged to store the data column by column, permute each set of data in a column and read out the results row by row without departing from the scope of the invention.", "text": "Once the data for all of the rows are permuted, the new array is read out column by column. Also, once the rows have been permuted, it is possible (but not required) to permute the data grouped by column before outputting the data. In the event that both the rows and columns are permuted, the rows, the columns or both may be permuted in accordance with the present invention. It is also possible to transpose rows of array, for example by transposing bits in the binary representation of the row index j. (In a four-row array, for example, the second and third rows would be transposed under this scheme.) It is also possible that either the rows or the columns, but not both may be permuted in accordance with a different method of permuting. Those skilled in the art will recognize that the system could be rearranged to store the data column by column, permute each set of data in a column and read out the results row by row without departing from the scope of the invention."}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "These methods of interleaving are based on number theory and may be implemented in software and/or hardware (i.e. application specific integrated circuits (ASIC), programmable logic arrays (PLA), or any other suitable logic devices). Further, a single pseudo random sequence generator (i.e. m-sequence, M-sequence, Gold sequence, Kasami sequence . . . ) can be employed as the interleaver.", "text": "These methods of interleaving are based on number theory and may be implemented in software and/or hardware (i.e. application specific integrated circuits (ASIC), programmable logic arrays (PLA), or any other suitable logic devices). Further, a single pseudo random sequence generator (i.e. m-sequence, M-sequence, Gold sequence, Kasami sequence . . . ) can be employed as the interleaver."}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "In the example depicted in FIG. 3, the value selected for P is 6, the values of α are 5 for all three rows, and the values of β are 1, 2, and 3 respectively for the three rows. (These are merely exemplary. Other numbers may be chosen to achieve different permutation results.) The values of α (5) are each relative prime numbers relative to the value of P (6), as stipulated above.", "text": "In the example depicted in FIG. 3, the value selected for P is 6, the values of α are 5 for all three rows, and the values of β are 1, 2, and 3 respectively for the three rows. (These are merely exemplary. Other numbers may be chosen to achieve different permutation results.) The values of α (5) are each relative prime numbers relative to the value of P (6), as stipulated above."}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Calculating the specified equation with the specified values for permuting row 0 of array D 350 into row 0 of array D₁ 360 proceeds as:", "text": "Calculating the specified equation with the specified values for permuting row 0 of array D 350 into row 0 of array D₁ 360 proceeds as:"}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "and the permuted data frame is contained in array D₁ 360 shown in FIG. 3. Outputting the array column by column outputs the frame elements in the order:", "text": "and the permuted data frame is contained in array D₁ 360 shown in FIG. 3. Outputting the array column by column outputs the frame elements in the order:"}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "1,8,15,0,7,14,5,6,13,4,11,12,3,10,17,2,9,16.", "text": "1,8,15,0,7,14,5,6,13,4,11,12,3,10,17,2,9,16."}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "In an alternative practice of the invention, data frame 110 is stored in consecutive storage locations, not as an array or matrix, and a separate index array is stored to index the elements of the data frame, the index array is permuted according to the equations of the present invention, and the data frame is output as indexed by the permuted index array.", "text": "In an alternative practice of the invention, data frame 110 is stored in consecutive storage locations, not as an array or matrix, and a separate index array is stored to index the elements of the data frame, the index array is permuted according to the equations of the present invention, and the data frame is output as indexed by the permuted index array."}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "FIG. 4 depicts a block 400 of storage 32 elements in length (thus having offsets of 0 through 31 from a starting storage location). A data frame 110, taken in this example to be 22 elements long and thus to consist of elements FE00 through FE21, occupies offset locations 00 through 21 within block 400. Offset locations 22 through 31 of block 400 contain unknown contents. A frame length of 22 elements is merely exemplary, and other lengths could be chosen. Also, storage of the frame elements in consecutive locations is exemplary, and non-consecutive locations could be employed.", "text": "FIG. 4 depicts a block 400 of storage 32 elements in length (thus having offsets of 0 through 31 from a starting storage location). A data frame 110, taken in this example to be 22 elements long and thus to consist of elements FE00 through FE21, occupies offset locations 00 through 21 within block 400. Offset locations 22 through 31 of block 400 contain unknown contents. A frame length of 22 elements is merely exemplary, and other lengths could be chosen. Also, storage of the frame elements in consecutive locations is exemplary, and non-consecutive locations could be employed."}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "FIG. 5 depicts index array I 550 for indexing storage block 400. It is organized as 4 rows of 8 columns each (N₁=4, N₂=8, N=N₁*N₂=32). Initial contents are filled in to array I 550 as shown in FIG. 5 sequentially. This sequential initialization yields the same effect as a row-by-row read-in of data frame 110.", "text": "FIG. 5 depicts index array I 550 for indexing storage block 400. It is organized as 4 rows of 8 columns each (N₁=4, N₂=8, N=N₁*N₂=32). Initial contents are filled in to array I 550 as shown in FIG. 5 sequentially. This sequential initialization yields the same effect as a row-by-row read-in of data frame 110."}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "The index array is permuted according to", "text": "The index array is permuted according to"}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "I₁(j,k)=I(j,(αj*k+βj)modP)", "text": "I₁(j,k)=I(j,(αj*k+βj)modP)"}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "where", "text": "where"}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "α=1, 3, 5, 7", "text": "α=1, 3, 5, 7"}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "β=0, 0, 0, 0", "text": "β=0, 0, 0, 0"}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "P=8", "text": "P=8"}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "These numbers are exemplary and other numbers could be chosen, as long as the stipulations are observed that P is at least equal to N₂ and that each value of α is a relative prime number relative to the chosen value of P.", "text": "These numbers are exemplary and other numbers could be chosen, as long as the stipulations are observed that P is at least equal to N₂ and that each value of α is a relative prime number relative to the chosen value of P."}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "If the equation is applied to the columns of row 2, for example, it yields:", "text": "If the equation is applied to the columns of row 2, for example, it yields:"}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Applying the equation comparably to rows 0, 1, and 3 produces the permuted index array I₁ 560 shown in FIG. 5.", "text": "Applying the equation comparably to rows 0, 1, and 3 produces the permuted index array I₁ 560 shown in FIG. 5."}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "The data frame 110 is read out of storage block 400 and output in the order specified in the permuted index array I₁ 560 taken column by column. This would output storage locations in offset order:", "text": "The data frame 110 is read out of storage block 400 and output in the order specified in the permuted index array I₁ 560 taken column by column. This would output storage locations in offset order:"}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "0,8,16,24,1,11,21,31,2,14,18,30,3,9,23,29,4,12,20,28,5,15,17,27,6,10,22,26,7,13,19,25.", "text": "0,8,16,24,1,11,21,31,2,14,18,30,3,9,23,29,4,12,20,28,5,15,17,27,6,10,22,26,7,13,19,25."}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "However, the example assumed a frame length of 22 elements, with offset locations 22-31 in block 400 not being part of the data frame. Accordingly, when outputting the data frame it would be punctured or pruned to a length of 22; i.e., offset locations greater than 21 are ignored. The data frame is thus output with an element order of 0,8,16,1,11,21,2,14,18,3,9,4,12,20,5,15,17,6,10,7,13,19.", "text": "However, the example assumed a frame length of 22 elements, with offset locations 22-31 in block 400 not being part of the data frame. Accordingly, when outputting the data frame it would be punctured or pruned to a length of 22; i.e., offset locations greater than 21 are ignored. The data frame is thus output with an element order of 0,8,16,1,11,21,2,14,18,3,9,4,12,20,5,15,17,6,10,7,13,19."}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "In one aspect of the invention, rows of the array may be transposed prior to outputting, for example by reversing the bits in the binary representations of row index j.", "text": "In one aspect of the invention, rows of the array may be transposed prior to outputting, for example by reversing the bits in the binary representations of row index j."}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "There are a number of different ways to implement the interleavers 100 of the present invention. FIG. 2 illustrates an embodiment of the invention wherein the interleaver 100 includes an input memory 300 for receiving and storing the data frame 110. This memory 300 may include shift registers, RAM or the like. The interleaver 100 may also include a working memory 310 which may also include RAM, shift registers or the like. The interleaver includes a processor 320 (e.g., a microprocessor, ASIC, etc.) which may be configured to process I(j,k) in real time according to the above-identified equation or to access a table which includes the results of I(j,k) already stored therein. Those skilled in the art will recognize that memory 300 and memory 310 may be the same memory or they may be separate memories.", "text": "There are a number of different ways to implement the interleavers 100 of the present invention. FIG. 2 illustrates an embodiment of the invention wherein the interleaver 100 includes an input memory 300 for receiving and storing the data frame 110. This memory 300 may include shift registers, RAM or the like. The interleaver 100 may also include a working memory 310 which may also include RAM, shift registers or the like. The interleaver includes a processor 320 (e.g., a microprocessor, ASIC, etc.) which may be configured to process I(j,k) in real time according to the above-identified equation or to access a table which includes the results of I(j,k) already stored therein. Those skilled in the art will recognize that memory 300 and memory 310 may be the same memory or they may be separate memories."}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "For real-time determinations of I(j,k), the first row of the index array is permuted and the bytes corresponding to the permuted index are stored in the working memory. Then the next row is permuted and stored, etc. until all rows have been permuted and stored. The permutation of rows may be done sequentially or in parallel.", "text": "For real-time determinations of I(j,k), the first row of the index array is permuted and the bytes corresponding to the permuted index are stored in the working memory. Then the next row is permuted and stored, etc. until all rows have been permuted and stored. The permutation of rows may be done sequentially or in parallel."}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Whether the permuted I(j,k) is determined in real time or by lookup, the data may be stored in the working memory in a number of different ways. It can be stored by selecting the data from the input memory in the same order as the I(j,k)s in the permuted index array (i.e., indexing the input memory with the permuting function) and placing them in the working memory in sequential available memory locations. It may also be stored by selecting the bytes in the sequence they were stored in the input memory (i.e., FIFO) and storing them in the working memory directly into the location determined by the permuted I(j,k)s (i.e., indexing the working memory with the permuting function). Once this is done, the data may be read out of the working memory column by column based upon the permuted index array. As stated above, the data could be subjected to another round of permuting after it is stored in the working memory based upon columns rather than on rows to achieve different results.", "text": "Whether the permuted I(j,k) is determined in real time or by lookup, the data may be stored in the working memory in a number of different ways. It can be stored by selecting the data from the input memory in the same order as the I(j,k)s in the permuted index array (i.e., indexing the input memory with the permuting function) and placing them in the working memory in sequential available memory locations. It may also be stored by selecting the bytes in the sequence they were stored in the input memory (i.e., FIFO) and storing them in the working memory directly into the location determined by the permuted I(j,k)s (i.e., indexing the working memory with the permuting function). Once this is done, the data may be read out of the working memory column by column based upon the permuted index array. As stated above, the data could be subjected to another round of permuting after it is stored in the working memory based upon columns rather than on rows to achieve different results."}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "If the system is sufficiently fast, one of the memories could be eliminated and as a data element is received it could be placed into the working memory, in real time or by table lookup, in the order corresponding to the permuted index array.", "text": "If the system is sufficiently fast, one of the memories could be eliminated and as a data element is received it could be placed into the working memory, in real time or by table lookup, in the order corresponding to the permuted index array."}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "The disclosed interleavers are compatible with existing turbo code structures. These interleavers offer superior performance without increasing system complexity.", "text": "The disclosed interleavers are compatible with existing turbo code structures. These interleavers offer superior performance without increasing system complexity."}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "In addition, those skilled in the art will realize that de-interleavers can be used to decode the interleaved data frames. The construction of de-interleavers used in decoding turbo codes is well known in the art. As such they are not further discussed herein. However, a de-interleaver corresponding to the embodiments can be constructed using the permuted sequences discussed above.", "text": "In addition, those skilled in the art will realize that de-interleavers can be used to decode the interleaved data frames. The construction of de-interleavers used in decoding turbo codes is well known in the art. As such they are not further discussed herein. However, a de-interleaver corresponding to the embodiments can be constructed using the permuted sequences discussed above."}, {"self_ref": "#/texts/68", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Although the embodiment described above is a turbo encoder such as is found in a CDMA system, those skilled in the art realize that the practice of the invention is not limited thereto and that the invention may be practiced for any type of interleaving and de-interleaving in any communication system.", "text": "Although the embodiment described above is a turbo encoder such as is found in a CDMA system, those skilled in the art realize that the practice of the invention is not limited thereto and that the invention may be practiced for any type of interleaving and de-interleaving in any communication system."}, {"self_ref": "#/texts/69", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "It will thus be seen that the invention efficiently attains the objects set forth above, among those made apparent from the preceding description. In particular, the invention provides improved apparatus and methods of interleaving codes of finite length while minimizing the complexity of the implementation.", "text": "It will thus be seen that the invention efficiently attains the objects set forth above, among those made apparent from the preceding description. In particular, the invention provides improved apparatus and methods of interleaving codes of finite length while minimizing the complexity of the implementation."}, {"self_ref": "#/texts/70", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "It will be understood that changes may be made in the above construction and in the foregoing sequences of operation without departing from the scope of the invention. It is accordingly intended that all matter contained in the above description or shown in the accompanying drawings be interpreted as illustrative rather than in a limiting sense.", "text": "It will be understood that changes may be made in the above construction and in the foregoing sequences of operation without departing from the scope of the invention. It is accordingly intended that all matter contained in the above description or shown in the accompanying drawings be interpreted as illustrative rather than in a limiting sense."}, {"self_ref": "#/texts/71", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "It is also to be understood that the following claims are intended to cover all of the generic and specific features of the invention as described herein, and all statements of the scope of the invention which, as a matter of language, might be said to fall therebetween.", "text": "It is also to be understood that the following claims are intended to cover all of the generic and specific features of the invention as described herein, and all statements of the scope of the invention which, as a matter of language, might be said to fall therebetween."}, {"self_ref": "#/texts/72", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/73"}, {"$ref": "#/texts/74"}, {"$ref": "#/texts/75"}, {"$ref": "#/texts/76"}, {"$ref": "#/texts/77"}, {"$ref": "#/texts/78"}, {"$ref": "#/texts/79"}, {"$ref": "#/texts/80"}, {"$ref": "#/texts/81"}, {"$ref": "#/texts/82"}, {"$ref": "#/texts/83"}, {"$ref": "#/texts/84"}, {"$ref": "#/texts/85"}, {"$ref": "#/texts/86"}, {"$ref": "#/texts/87"}, {"$ref": "#/texts/88"}, {"$ref": "#/texts/89"}, {"$ref": "#/texts/90"}, {"$ref": "#/texts/91"}, {"$ref": "#/texts/92"}, {"$ref": "#/texts/93"}, {"$ref": "#/texts/94"}, {"$ref": "#/texts/95"}, {"$ref": "#/texts/96"}, {"$ref": "#/texts/97"}, {"$ref": "#/texts/98"}, {"$ref": "#/texts/99"}, {"$ref": "#/texts/100"}, {"$ref": "#/texts/101"}, {"$ref": "#/texts/102"}, {"$ref": "#/texts/103"}, {"$ref": "#/texts/104"}, {"$ref": "#/texts/105"}, {"$ref": "#/texts/106"}, {"$ref": "#/texts/107"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "CLAIMS", "text": "CLAIMS", "level": 2}, {"self_ref": "#/texts/73", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "1. A method of interleaving elements of frames of signal data communication channel, the method comprising; storing a frame of signal data comprising a plurality of elements as an array D having N₁ rows enumerated as 0, 1, . . . N₁−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1; and permuting array D into array D₁ according to D₁(𝑗,𝑘)=D(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays D and D₁; k is an index through the columns of arrays D and D₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P.", "text": "1. A method of interleaving elements of frames of signal data communication channel, the method comprising; storing a frame of signal data comprising a plurality of elements as an array D having N₁ rows enumerated as 0, 1, . . . N₁−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1; and permuting array D into array D₁ according to D₁(𝑗,𝑘)=D(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays D and D₁; k is an index through the columns of arrays D and D₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P."}, {"self_ref": "#/texts/74", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "2. The method according to claim 1 wherein said elements of array D are stored in accordance with a first order and wherein said elements of array D₁ are output in accordance with a second order.", "text": "2. The method according to claim 1 wherein said elements of array D are stored in accordance with a first order and wherein said elements of array D₁ are output in accordance with a second order."}, {"self_ref": "#/texts/75", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "3. The method according to claim 2 wherein elements of array D are stored row by row and elements of array D₁ are output column by column.", "text": "3. The method according to claim 2 wherein elements of array D are stored row by row and elements of array D₁ are output column by column."}, {"self_ref": "#/texts/76", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "4. The method according to claim 1 further including outputting of array D₁ and wherein the product of N₁ and N₂ is greater than the number of elements in the frame and the frame is punctured during outputting to the number of elements in the frame.", "text": "4. The method according to claim 1 further including outputting of array D₁ and wherein the product of N₁ and N₂ is greater than the number of elements in the frame and the frame is punctured during outputting to the number of elements in the frame."}, {"self_ref": "#/texts/77", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "5. A method of interleaving elements of frames of signal data communication channel, the method comprising; creating and storing an index array I having N₁ rows enumerated as 0, 1, . . . N₁−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1, storing elements of a frame of signal data in each of a plurality of storage locations; storing in row-by-row sequential positions in array I values indicative of corresponding locations of frame elements; and permuting array I into array I₁ according to I₁(𝑗,𝑘)=I(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays I and I₁; k is an index through the columns of arrays I and I₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P, whereby the frame of signal data as indexed by array I₁ is effectively permuted.", "text": "5. A method of interleaving elements of frames of signal data communication channel, the method comprising; creating and storing an index array I having N₁ rows enumerated as 0, 1, . . . N₁−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1, storing elements of a frame of signal data in each of a plurality of storage locations; storing in row-by-row sequential positions in array I values indicative of corresponding locations of frame elements; and permuting array I into array I₁ according to I₁(𝑗,𝑘)=I(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays I and I₁; k is an index through the columns of arrays I and I₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P, whereby the frame of signal data as indexed by array I₁ is effectively permuted."}, {"self_ref": "#/texts/78", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "6. The method according to claim 5 further including permuting said stored elements according to said permuted index array I₁.", "text": "6. The method according to claim 5 further including permuting said stored elements according to said permuted index array I₁."}, {"self_ref": "#/texts/79", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "7. The method according to claim 5 wherein said elements of the frame of data are output as indexed by entries of array I₁ taken other than row by row.", "text": "7. The method according to claim 5 wherein said elements of the frame of data are output as indexed by entries of array I₁ taken other than row by row."}, {"self_ref": "#/texts/80", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "8. The method according to claim 7 wherein elements of the frame of data are output as indexed by entries of array I₁ taken column by column.", "text": "8. The method according to claim 7 wherein elements of the frame of data are output as indexed by entries of array I₁ taken column by column."}, {"self_ref": "#/texts/81", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "9. The method according to claim 5 including the step of transposing rows of array I prior to the step of permuting array I.", "text": "9. The method according to claim 5 including the step of transposing rows of array I prior to the step of permuting array I."}, {"self_ref": "#/texts/82", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "10. The method according to claim 5 wherein N₁ is equal to 4, N₂ is equal to 8, P is equal to 8, and the values of αj are different for each row and are chosen from a group consisting of 1, 3, 5, and 7.", "text": "10. The method according to claim 5 wherein N₁ is equal to 4, N₂ is equal to 8, P is equal to 8, and the values of αj are different for each row and are chosen from a group consisting of 1, 3, 5, and 7."}, {"self_ref": "#/texts/83", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "11. The method according to claim 10 wherein the values of αj are 1, 3, 5, and 7 for j=0, 1, 2, and 3 respectively.", "text": "11. The method according to claim 10 wherein the values of αj are 1, 3, 5, and 7 for j=0, 1, 2, and 3 respectively."}, {"self_ref": "#/texts/84", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "12. The method according to claim 11 wherein all values of β are zero.", "text": "12. The method according to claim 11 wherein all values of β are zero."}, {"self_ref": "#/texts/85", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "13. The method according to claim 10 wherein the values of αj are 1, 5, 3, and 7 for j=0, 1, 2, and 3 respectively.", "text": "13. The method according to claim 10 wherein the values of αj are 1, 5, 3, and 7 for j=0, 1, 2, and 3 respectively."}, {"self_ref": "#/texts/86", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "14. The method according to claim 13 wherein all values of β are zero.", "text": "14. The method according to claim 13 wherein all values of β are zero."}, {"self_ref": "#/texts/87", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "15. The method according to claim 5 wherein all values of β are zero.", "text": "15. The method according to claim 5 wherein all values of β are zero."}, {"self_ref": "#/texts/88", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "16. The method according to claim 5 wherein at least two values of β are the same.", "text": "16. The method according to claim 5 wherein at least two values of β are the same."}, {"self_ref": "#/texts/89", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "17. The method according to claim 5 further including outputting of the frame of data and wherein the product of N₁ and N₂ is greater than the number of elements in the frame of data and the frame of data is punctured during outputting to the number of elements in the frame of data.", "text": "17. The method according to claim 5 further including outputting of the frame of data and wherein the product of N₁ and N₂ is greater than the number of elements in the frame of data and the frame of data is punctured during outputting to the number of elements in the frame of data."}, {"self_ref": "#/texts/90", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "18. An interleaver for interleaving elements of frames of data, the interleaver comprising; storage means for storing a frame of data comprising a plurality of elements as an array D having N₁ rows enumerated as 0, 1, . . . N₂−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1, and permuting means for permuting array D into array D₁ according to D₁(𝑗,𝑘)=D(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays D and D₁; k is an index through the columns of arrays D and D₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P.", "text": "18. An interleaver for interleaving elements of frames of data, the interleaver comprising; storage means for storing a frame of data comprising a plurality of elements as an array D having N₁ rows enumerated as 0, 1, . . . N₂−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1, and permuting means for permuting array D into array D₁ according to D₁(𝑗,𝑘)=D(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays D and D₁; k is an index through the columns of arrays D and D₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P."}, {"self_ref": "#/texts/91", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "19. The interleaver according to claim 18 including means for storing said elements of array D in accordance with a first order and means for outputting said elements of array D₁ in accordance with a second order.", "text": "19. The interleaver according to claim 18 including means for storing said elements of array D in accordance with a first order and means for outputting said elements of array D₁ in accordance with a second order."}, {"self_ref": "#/texts/92", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "20. The interleaver according to claim 19 wherein said means for storing said elements of array D stores row by row and said means for outputting elements of array D₁ outputs column by column.", "text": "20. The interleaver according to claim 19 wherein said means for storing said elements of array D stores row by row and said means for outputting elements of array D₁ outputs column by column."}, {"self_ref": "#/texts/93", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "21. The interleaver according to claim 18 including means for outputting said array D₁ and for puncturing said array D₁ to the number of elements in the frame when the product of N₁ and N₂ is greater than the number of elements in the frame.", "text": "21. The interleaver according to claim 18 including means for outputting said array D₁ and for puncturing said array D₁ to the number of elements in the frame when the product of N₁ and N₂ is greater than the number of elements in the frame."}, {"self_ref": "#/texts/94", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "22. An interleaver for interleaving elements of frames of data, the interleaver comprising; means for storing an index array I having N₁ rows enumerated as 0, 1, . . . N₁−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1, and means for receiving a frame of data and storing elements of the frame of data in each of a plurality of storage locations; means for storing in row-by-row sequential positions in array I values indicative of corresponding locations of frame elements; and means for permuting array I into array I₁ according to: I₁(𝑗,𝑘)=I(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays I and I₁; k is an index through the columns of arrays I and I₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P, whereby the frame of data as indexed by array I₁ is effectively permuted.", "text": "22. An interleaver for interleaving elements of frames of data, the interleaver comprising; means for storing an index array I having N₁ rows enumerated as 0, 1, . . . N₁−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1, and means for receiving a frame of data and storing elements of the frame of data in each of a plurality of storage locations; means for storing in row-by-row sequential positions in array I values indicative of corresponding locations of frame elements; and means for permuting array I into array I₁ according to: I₁(𝑗,𝑘)=I(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays I and I₁; k is an index through the columns of arrays I and I₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P, whereby the frame of data as indexed by array I₁ is effectively permuted."}, {"self_ref": "#/texts/95", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "23. The interleaver according to claim 22 further including means for permuting said stored elements according to said permuted index array I₁.", "text": "23. The interleaver according to claim 22 further including means for permuting said stored elements according to said permuted index array I₁."}, {"self_ref": "#/texts/96", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "24. The interleaver according to claim 22 including means for outputting frame elements as indexed by entries of array I₁ taken other than row by row.", "text": "24. The interleaver according to claim 22 including means for outputting frame elements as indexed by entries of array I₁ taken other than row by row."}, {"self_ref": "#/texts/97", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "25. The interleaver according to claim 24 including means for outputting frame elements as indexed by entries of array I₁ taken column by column.", "text": "25. The interleaver according to claim 24 including means for outputting frame elements as indexed by entries of array I₁ taken column by column."}, {"self_ref": "#/texts/98", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "26. The interleaver according to claim 22 wherein the product of N₁ and N₂ is greater than the number of elements in the frame and the frame is punctured by the means for outputting to the number of elements in the frame.", "text": "26. The interleaver according to claim 22 wherein the product of N₁ and N₂ is greater than the number of elements in the frame and the frame is punctured by the means for outputting to the number of elements in the frame."}, {"self_ref": "#/texts/99", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "27. An interleaver for interleaving elements of frames of data, the interleaver comprising; an input memory for storing a received frame of data comprising a plurality of elements as an array D having N₁ rows enumerated as 0, 1, . . . N₁−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1; a processor coupled to said input memory for permuting array D into array D₁ according to D₁(𝑗,𝑘)=D(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays D and D₁; k is an index through the columns of arrays D and D₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P, and a working memory coupled to said processor and configured to store the permuted array D₁.", "text": "27. An interleaver for interleaving elements of frames of data, the interleaver comprising; an input memory for storing a received frame of data comprising a plurality of elements as an array D having N₁ rows enumerated as 0, 1, . . . N₁−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1; a processor coupled to said input memory for permuting array D into array D₁ according to D₁(𝑗,𝑘)=D(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays D and D₁; k is an index through the columns of arrays D and D₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P, and a working memory coupled to said processor and configured to store the permuted array D₁."}, {"self_ref": "#/texts/100", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "28. The interlcavcr according to claim 27 wherein said input memory stores said elements of array D in accordance with a first order and said working memory outputs said elements of array D₁ in accordance with a second order.", "text": "28. The interlcavcr according to claim 27 wherein said input memory stores said elements of array D in accordance with a first order and said working memory outputs said elements of array D₁ in accordance with a second order."}, {"self_ref": "#/texts/101", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "29. The interleaver according to claim 28 wherein said input memory stores elements of array D row by row and said working memory outputs elements of array D₁ column by column.", "text": "29. The interleaver according to claim 28 wherein said input memory stores elements of array D row by row and said working memory outputs elements of array D₁ column by column."}, {"self_ref": "#/texts/102", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "30. The interleaver according to claim 27 said working memory punctures said array D₁ to the number of elements in the frame when the product of N₁ and N₂ is greater than the number of elements in the frame.", "text": "30. The interleaver according to claim 27 said working memory punctures said array D₁ to the number of elements in the frame when the product of N₁ and N₂ is greater than the number of elements in the frame."}, {"self_ref": "#/texts/103", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "31. An interleaver for interleaving elements of frames of data, the interleaver comprising; a memory for storing an index array I having N₁ rows enumerated as 0, 1, . . . N₁−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1, and said memory also for storing elements of a received frame of data in each of a plurality of storage locations; a processor coupled to said memory for storing in row-by-row sequential positions in array I values indicative of corresponding locations of frame elements; and said processor also for permuting array I into array I₁ stored in said memory according to: I₁(𝑗,𝑘)=I(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays I and I₁; k is an index through the columns of arrays I and I₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P, and whereby the frame of data as indexed by array I₁ is effectively permuted.", "text": "31. An interleaver for interleaving elements of frames of data, the interleaver comprising; a memory for storing an index array I having N₁ rows enumerated as 0, 1, . . . N₁−1; and N₂ columns enumerated as 0, 1, . . . N₂−1, wherein N₁ and N₂ are positive integers greater than 1, and said memory also for storing elements of a received frame of data in each of a plurality of storage locations; a processor coupled to said memory for storing in row-by-row sequential positions in array I values indicative of corresponding locations of frame elements; and said processor also for permuting array I into array I₁ stored in said memory according to: I₁(𝑗,𝑘)=I(𝑗,(αj𝑘+βj)𝑚𝑜𝑑𝑃)  wherein j is an index through the rows of arrays I and I₁; k is an index through the columns of arrays I and I₁; αj and βj are integers predetermined for each row j; P is an integer at least equal to N₂; and each αj is a relative prime number relative to P, and whereby the frame of data as indexed by array I₁ is effectively permuted."}, {"self_ref": "#/texts/104", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "32. The interleaver according to claim 31 wherein said processor permutes said stored elements according to said permuted index array I₁.", "text": "32. The interleaver according to claim 31 wherein said processor permutes said stored elements according to said permuted index array I₁."}, {"self_ref": "#/texts/105", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "33. The interleaver according to claim 31 wherein said memory outputs frame elements as indexed by entries of array I₁ taken other than row by row.", "text": "33. The interleaver according to claim 31 wherein said memory outputs frame elements as indexed by entries of array I₁ taken other than row by row."}, {"self_ref": "#/texts/106", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "34. The interleaver according to claim 33 wherein said memory outputs frame elements as indexed by entries of array I₁ taken column by column.", "text": "34. The interleaver according to claim 33 wherein said memory outputs frame elements as indexed by entries of array I₁ taken column by column."}, {"self_ref": "#/texts/107", "parent": {"$ref": "#/texts/72"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "35. The interleaver according to claim 31 wherein said memory punctures the frame of data to the number of elements in the frame of data when the product of N₁ and N₂ is greater than the number of elements in the frame of data.", "text": "35. The interleaver according to claim 31 wherein said memory punctures the frame of data to the number of elements in the frame of data when the product of N₁ and N₂ is greater than the number of elements in the frame of data."}], "pictures": [], "tables": [], "key_value_items": [], "form_items": [], "pages": {}}