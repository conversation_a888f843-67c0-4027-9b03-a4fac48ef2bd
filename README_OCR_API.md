# Docling VLM OCR API

High-accuracy OCR API using Docling with Vision Language Models (VLMs). Optimized for multilingual text recognition including Traditional Chinese, Simplified Chinese, English, and handwriting.

## Features

- **Two VLM Models for Comparison**:
  - **Qwen 2.5 VL**: High accuracy for multilingual text and handwriting
  - **SmolDocling**: Fast inference with native spatial understanding

- **PaddleOCR-like Output**: Returns text with bounding box coordinates
- **Multilingual Support**: Optimized for Chinese (Traditional/Simplified) and English
- **Handwriting Recognition**: Superior to traditional OCR engines
- **FastAPI Interface**: RESTful API with automatic documentation

## Quick Start

### 1. Installation

```bash
# Clone or download the files
# Install dependencies
pip install -r requirements_ocr_api.txt
```

### 2. Start the API Server

```bash
# Option 1: Use the startup script (recommended)
python start_ocr_api.py

# Option 2: Direct startup
python fastapi_docling_ocr.py
```

### 3. Test the API

```bash
# Test with the provided client
python test_ocr_api.py

# Or use curl
curl -X POST "http://localhost:8000/ocr/qwen" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_image.jpg"
```

## API Endpoints

### POST `/ocr/qwen`
OCR using Qwen 2.5 VL model (high accuracy, multilingual)

**Parameters:**
- `file`: Image file (JPG, PNG, TIFF, etc.)
- `storage_path`: Optional path to save results

**Response:**
```json
{
  "success": true,
  "message": "OCR completed successfully",
  "results": [
    {
      "text": "extracted text",
      "confidence": 1.0,
      "bbox": {
        "left": 100.0,
        "top": 50.0,
        "right": 200.0,
        "bottom": 80.0,
        "width": 100.0,
        "height": 30.0
      },
      "page": 1,
      "label": "text",
      "from_vlm": true
    }
  ],
  "total_text_elements": 10,
  "processing_time": 15.2,
  "model_used": "Qwen 2.5 VL 3B"
}
```

### POST `/ocr/smoldocling`
OCR using SmolDocling model (fast, good accuracy)

Same parameters and response format as Qwen endpoint.

### GET `/health`
Health check and model availability

### GET `/docs`
Interactive API documentation (Swagger UI)

## Configuration

### Environment Variables

```bash
# Qwen Model Configuration
export QWEN_MODEL_ID="Qwen/Qwen2.5-VL-3B-Instruct"  # or 7B, 14B variants
export QWEN_QUANTIZED="false"  # Set to "true" for CPU optimization
export QWEN_8BIT="false"       # Set to "true" to reduce memory usage
export QWEN_SCALE="2.0"        # Image resolution scaling

# SmolDocling Configuration
export SMOLDOCLING_MLX="false"        # Set to "true" for Apple Silicon
export SMOLDOCLING_BACKEND_TEXT="false"  # Use hybrid VLM+OCR approach

# API Configuration
export API_HOST="0.0.0.0"
export API_PORT="8000"
export API_WORKERS="1"
export STORAGE_PATH="./ocr_results"
```

### Hardware Optimization

**NVIDIA GPU (CUDA):**
```bash
# Use full precision for best accuracy
export QWEN_QUANTIZED="false"
export QWEN_8BIT="false"
```

**Apple Silicon (M1/M2/M3):**
```bash
# Use MLX for better performance
export SMOLDOCLING_MLX="true"
export QWEN_MODEL_ID="mlx-community/Qwen2.5-VL-3B-Instruct-bf16"
```

**CPU Only:**
```bash
# Enable optimizations for CPU
export QWEN_QUANTIZED="true"
export QWEN_8BIT="true"
export QWEN_SCALE="1.5"
```

## Model Comparison

| Model | Accuracy | Speed | Memory | Best For |
|-------|----------|-------|---------|----------|
| **Qwen 2.5 VL 3B** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | Multilingual, handwriting |
| **Qwen 2.5 VL 7B** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐ | Maximum accuracy |
| **SmolDocling** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Fast processing |

## Usage Examples

### Python Client

```python
from test_ocr_api import OCRAPIClient

client = OCRAPIClient("http://localhost:8000")

# Check API health
health = client.health_check()
print(f"Qwen available: {health['qwen_available']}")

# OCR with Qwen 2.5 VL
result = client.ocr_with_qwen("document.jpg")
for item in result['results']:
    print(f"Text: {item['text']}")
    print(f"Bbox: {item['bbox']}")

# Compare both models
comparison = client.compare_models("document.jpg")
```

### cURL Examples

```bash
# OCR with Qwen 2.5 VL
curl -X POST "http://localhost:8000/ocr/qwen" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@document.jpg" \
     -F "storage_path=./results"

# OCR with SmolDocling
curl -X POST "http://localhost:8000/ocr/smoldocling" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@document.jpg"

# Health check
curl "http://localhost:8000/health"
```

## Supported Formats

- **Images**: JPG, JPEG, PNG, TIFF, BMP, WEBP
- **Documents**: PDF (single page recommended)

## Performance Tips

1. **For Maximum Accuracy**: Use Qwen 2.5 VL 7B with full precision
2. **For Speed**: Use SmolDocling with MLX (Apple Silicon) or quantization (CPU)
3. **For Memory Efficiency**: Enable 8-bit loading and quantization
4. **For Batch Processing**: Process images sequentially (batch mode not implemented)

## Troubleshooting

### Common Issues

**Model Loading Errors:**
```bash
# Clear model cache
rm -rf ./models_cache ./huggingface_cache
# Restart API
python start_ocr_api.py
```

**Memory Issues:**
```bash
# Enable memory optimizations
export QWEN_QUANTIZED="true"
export QWEN_8BIT="true"
```

**Slow Performance:**
```bash
# For Apple Silicon
export SMOLDOCLING_MLX="true"

# For CPU
export QWEN_SCALE="1.5"  # Reduce image resolution
```

### Logs and Debugging

- API logs: Check console output
- Model cache: `./models_cache/`
- Results: `./ocr_results/`
- Health check: `http://localhost:8000/health`

## API Documentation

Once the server is running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Requirements

- Python 3.8+
- 8GB+ RAM (16GB+ recommended)
- GPU with 8GB+ VRAM (optional, but recommended)
- Internet connection (for model downloads)

## License

This project uses Docling and various open-source models. Please check individual model licenses for commercial use.
