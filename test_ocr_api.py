#!/usr/bin/env python3
"""
Test client for the Docling VLM OCR API.
Demonstrates how to use both Qwen 2.5 VL and SmolDocling endpoints.
"""

import requests
import json
import time
from pathlib import Path
from typing import Dict, Any


class OCRAPIClient:
    """Client for interacting with the Docling VLM OCR API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
    
    def health_check(self) -> Dict[str, Any]:
        """Check API health and model availability."""
        response = requests.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    def ocr_with_qwen(self, image_path: str, storage_path: str = None) -> Dict[str, Any]:
        """
        Perform OCR using Qwen 2.5 VL model.
        
        Args:
            image_path: Path to the image file
            storage_path: Optional path to save results
        
        Returns:
            OCR results in PaddleOCR-like format
        """
        
        if not Path(image_path).exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        with open(image_path, 'rb') as f:
            files = {'file': (Path(image_path).name, f, 'image/jpeg')}
            data = {}
            if storage_path:
                data['storage_path'] = storage_path
            
            response = requests.post(
                f"{self.base_url}/ocr/qwen",
                files=files,
                data=data,
                timeout=300  # 5 minutes timeout for VLM processing
            )
        
        response.raise_for_status()
        return response.json()
    
    def ocr_with_smoldocling(self, image_path: str, storage_path: str = None) -> Dict[str, Any]:
        """
        Perform OCR using SmolDocling model.
        
        Args:
            image_path: Path to the image file
            storage_path: Optional path to save results
        
        Returns:
            OCR results in PaddleOCR-like format
        """
        
        if not Path(image_path).exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        with open(image_path, 'rb') as f:
            files = {'file': (Path(image_path).name, f, 'image/jpeg')}
            data = {}
            if storage_path:
                data['storage_path'] = storage_path
            
            response = requests.post(
                f"{self.base_url}/ocr/smoldocling",
                files=files,
                data=data,
                timeout=300  # 5 minutes timeout for VLM processing
            )
        
        response.raise_for_status()
        return response.json()
    
    def compare_models(self, image_path: str, storage_path: str = None) -> Dict[str, Any]:
        """
        Compare OCR results from both models.
        
        Args:
            image_path: Path to the image file
            storage_path: Optional path to save results
        
        Returns:
            Comparison results
        """
        
        print(f"Comparing OCR models on: {image_path}")
        print("=" * 60)
        
        results = {}
        
        # Test Qwen 2.5 VL
        print("\n1. Testing Qwen 2.5 VL...")
        try:
            start_time = time.time()
            qwen_result = self.ocr_with_qwen(image_path, storage_path)
            qwen_time = time.time() - start_time
            
            results['qwen'] = qwen_result
            results['qwen']['client_processing_time'] = qwen_time
            
            print(f"✓ Qwen 2.5 VL completed in {qwen_time:.2f}s")
            print(f"  - Server processing time: {qwen_result['processing_time']:.2f}s")
            print(f"  - Text elements found: {qwen_result['total_text_elements']}")
            print(f"  - Success: {qwen_result['success']}")
            
        except Exception as e:
            print(f"✗ Qwen 2.5 VL failed: {e}")
            results['qwen'] = {'error': str(e)}
        
        # Test SmolDocling
        print("\n2. Testing SmolDocling...")
        try:
            start_time = time.time()
            smol_result = self.ocr_with_smoldocling(image_path, storage_path)
            smol_time = time.time() - start_time
            
            results['smoldocling'] = smol_result
            results['smoldocling']['client_processing_time'] = smol_time
            
            print(f"✓ SmolDocling completed in {smol_time:.2f}s")
            print(f"  - Server processing time: {smol_result['processing_time']:.2f}s")
            print(f"  - Text elements found: {smol_result['total_text_elements']}")
            print(f"  - Success: {smol_result['success']}")
            
        except Exception as e:
            print(f"✗ SmolDocling failed: {e}")
            results['smoldocling'] = {'error': str(e)}
        
        # Print comparison summary
        print("\n" + "=" * 60)
        print("COMPARISON SUMMARY")
        print("=" * 60)
        
        if 'qwen' in results and 'smoldocling' in results:
            if 'error' not in results['qwen'] and 'error' not in results['smoldocling']:
                print(f"Qwen 2.5 VL:   {results['qwen']['total_text_elements']} elements in {results['qwen']['processing_time']:.2f}s")
                print(f"SmolDocling:   {results['smoldocling']['total_text_elements']} elements in {results['smoldocling']['processing_time']:.2f}s")
                
                # Speed comparison
                qwen_speed = results['qwen']['processing_time']
                smol_speed = results['smoldocling']['processing_time']
                if qwen_speed > 0 and smol_speed > 0:
                    speed_ratio = qwen_speed / smol_speed
                    if speed_ratio > 1:
                        print(f"SmolDocling is {speed_ratio:.1f}x faster than Qwen 2.5 VL")
                    else:
                        print(f"Qwen 2.5 VL is {1/speed_ratio:.1f}x faster than SmolDocling")
        
        return results


def print_ocr_results(results: Dict[str, Any], max_results: int = 5):
    """Print OCR results in a readable format."""
    
    if not results['success']:
        print(f"❌ OCR failed: {results['message']}")
        return
    
    print(f"✅ OCR successful with {results['model_used']}")
    print(f"📊 Found {results['total_text_elements']} text elements")
    print(f"⏱️  Processing time: {results['processing_time']:.2f}s")
    print()
    
    # Show sample results
    print(f"📝 Sample results (showing first {max_results}):")
    print("-" * 50)
    
    for i, result in enumerate(results['results'][:max_results]):
        bbox = result['bbox']
        print(f"{i+1}. Text: '{result['text'][:50]}{'...' if len(result['text']) > 50 else ''}'")
        print(f"   Label: {result['label']}")
        print(f"   Bbox: ({bbox['left']:.1f}, {bbox['top']:.1f}, {bbox['right']:.1f}, {bbox['bottom']:.1f})")
        print(f"   Page: {result['page']}, Confidence: {result['confidence']:.3f}")
        print()


def main():
    """Example usage of the OCR API client."""
    
    # Initialize client
    client = OCRAPIClient("http://localhost:8000")
    
    # Check API health
    print("Checking API health...")
    try:
        health = client.health_check()
        print(f"✅ API is healthy")
        print(f"   Qwen 2.5 VL available: {health['qwen_available']}")
        print(f"   SmolDocling available: {health['smoldocling_available']}")
    except Exception as e:
        print(f"❌ API health check failed: {e}")
        print("Make sure the API server is running: python fastapi_docling_ocr.py")
        return
    
    # Example image path - replace with your image
    image_path = "sample_document.jpg"  # Change this to your image path
    
    if not Path(image_path).exists():
        print(f"\n❌ Image file not found: {image_path}")
        print("Please provide a valid image file path.")
        print("Supported formats: JPG, JPEG, PNG, TIFF, BMP, WEBP")
        return
    
    # Test individual models
    print(f"\n🖼️  Testing OCR on: {image_path}")
    
    # Test Qwen 2.5 VL
    print("\n" + "="*60)
    print("TESTING QWEN 2.5 VL")
    print("="*60)
    try:
        qwen_results = client.ocr_with_qwen(image_path, storage_path="./ocr_results")
        print_ocr_results(qwen_results)
    except Exception as e:
        print(f"❌ Qwen 2.5 VL test failed: {e}")
    
    # Test SmolDocling
    print("\n" + "="*60)
    print("TESTING SMOLDOCLING")
    print("="*60)
    try:
        smol_results = client.ocr_with_smoldocling(image_path, storage_path="./ocr_results")
        print_ocr_results(smol_results)
    except Exception as e:
        print(f"❌ SmolDocling test failed: {e}")
    
    # Compare both models
    print("\n" + "="*60)
    print("MODEL COMPARISON")
    print("="*60)
    try:
        comparison = client.compare_models(image_path, storage_path="./ocr_results")
        
        # Save comparison results
        with open("ocr_comparison.json", 'w', encoding='utf-8') as f:
            json.dump(comparison, f, ensure_ascii=False, indent=2)
        print(f"\n💾 Comparison results saved to: ocr_comparison.json")
        
    except Exception as e:
        print(f"❌ Model comparison failed: {e}")


if __name__ == "__main__":
    main()
