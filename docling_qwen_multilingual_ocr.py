#!/usr/bin/env python3
"""
Example showing how to use Qwen 2.5 VL with Docling for high-accuracy multilingual OCR.
Supports Traditional Chinese, Simplified Chinese, English, and handwriting recognition.
Optimized for single page image processing.
"""

from pathlib import Path
from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import VlmPipelineOptions
from docling.datamodel.pipeline_options_vlm_model import (
    InlineVlmOptions, 
    InferenceFramework, 
    TransformersModelType,
    ResponseFormat
)
from docling.datamodel.accelerator_options import AcceleratorDevice
from docling.pipeline.vlm_pipeline import VlmP<PERSON>eline


def create_qwen_multilingual_ocr_options():
    """
    Create VLM options for Qwen 2.5 VL optimized for multilingual OCR.
    Supports Traditional Chinese, Simplified Chinese, English, and handwriting.
    """
    
    # Custom prompt optimized for multilingual OCR with high accuracy
    multilingual_ocr_prompt = """
Please perform OCR (Optical Character Recognition) on this image with the highest accuracy possible.

Requirements:
1. Extract ALL text from the image, including:
   - Traditional Chinese (繁體中文)
   - Simplified Chinese (简体中文) 
   - English text
   - Numbers and symbols
   - Handwritten text (if any)

2. Preserve the original layout and structure as much as possible
3. Output the text in markdown format
4. If there are tables, format them as markdown tables
5. If there are multiple columns, maintain the column structure
6. For handwritten text, do your best to recognize it accurately
7. Do not miss any text, even if it's small or unclear
8. Maintain the reading order (top to bottom, left to right for English, appropriate order for Chinese)

Please be extremely careful and accurate with character recognition, especially for Chinese characters.
"""

    # Configure Qwen 2.5 VL for optimal multilingual OCR
    qwen_vlm_options = InlineVlmOptions(
        repo_id="Qwen/Qwen2.5-VL-3B-Instruct",  # Use the full 3B model for better accuracy
        prompt=multilingual_ocr_prompt,
        response_format=ResponseFormat.MARKDOWN,
        inference_framework=InferenceFramework.TRANSFORMERS,
        transformers_model_type=TransformersModelType.AUTOMODEL_VISION2SEQ,
        supported_devices=[
            AcceleratorDevice.CPU,
            AcceleratorDevice.CUDA,
            AcceleratorDevice.MPS,
        ],
        scale=2.0,  # Higher resolution for better OCR accuracy
        temperature=0.0,  # Deterministic output for OCR
        max_new_tokens=4096,  # Allow longer output for complex documents
        trust_remote_code=True,  # Required for Qwen models
        quantized=False,  # Disable quantization for better accuracy
        load_in_8bit=False,  # Use full precision for better OCR
    )
    
    return qwen_vlm_options


def create_qwen_mlx_options():
    """
    Alternative: Use MLX version of Qwen 2.5 VL for Apple Silicon (faster inference).
    """
    
    multilingual_ocr_prompt = """
Perform high-accuracy OCR on this image. Extract all text including Traditional Chinese (繁體中文), Simplified Chinese (简体中文), English, and handwritten text. Output in markdown format preserving the original layout. Be extremely accurate with Chinese character recognition.
"""
    
    qwen_mlx_options = InlineVlmOptions(
        repo_id="mlx-community/Qwen2.5-VL-3B-Instruct-bf16",
        prompt=multilingual_ocr_prompt,
        response_format=ResponseFormat.MARKDOWN,
        inference_framework=InferenceFramework.MLX,
        supported_devices=[AcceleratorDevice.MPS],
        scale=2.0,
        temperature=0.0,
        max_new_tokens=4096,
    )
    
    return qwen_mlx_options


def create_custom_qwen_options(repo_id: str, use_mlx: bool = False):
    """
    Create custom Qwen VLM options with specific model and configuration.
    
    Args:
        repo_id: Hugging Face model repository ID
        use_mlx: Whether to use MLX framework (for Apple Silicon)
    """
    
    # Enhanced prompt for maximum OCR accuracy
    enhanced_prompt = """
You are an expert OCR system. Analyze this image and extract ALL text with maximum accuracy.

Text types to recognize:
- Traditional Chinese characters (繁體中文)
- Simplified Chinese characters (简体中文)
- English text (uppercase and lowercase)
- Numbers and mathematical symbols
- Punctuation marks
- Handwritten text (print and cursive)

Output requirements:
- Use markdown format
- Preserve original layout and structure
- Maintain proper spacing and line breaks
- For tables: use markdown table format
- For lists: use markdown list format
- For headings: use appropriate markdown heading levels

Quality requirements:
- Be extremely accurate with character recognition
- Do not hallucinate or add text that isn't there
- If text is unclear, make your best effort but don't guess
- Pay special attention to similar-looking characters
- Maintain the correct reading order

Begin OCR extraction:
"""
    
    if use_mlx:
        options = InlineVlmOptions(
            repo_id=repo_id,
            prompt=enhanced_prompt,
            response_format=ResponseFormat.MARKDOWN,
            inference_framework=InferenceFramework.MLX,
            supported_devices=[AcceleratorDevice.MPS],
            scale=2.0,
            temperature=0.0,
            max_new_tokens=8192,
        )
    else:
        options = InlineVlmOptions(
            repo_id=repo_id,
            prompt=enhanced_prompt,
            response_format=ResponseFormat.MARKDOWN,
            inference_framework=InferenceFramework.TRANSFORMERS,
            transformers_model_type=TransformersModelType.AUTOMODEL_VISION2SEQ,
            supported_devices=[
                AcceleratorDevice.CPU,
                AcceleratorDevice.CUDA,
                AcceleratorDevice.MPS,
            ],
            scale=2.0,
            temperature=0.0,
            max_new_tokens=8192,
            trust_remote_code=True,
            quantized=False,
            load_in_8bit=False,
        )
    
    return options


def process_image_with_qwen_ocr(image_path: str, use_mlx: bool = False, custom_repo_id: str = None):
    """
    Process a single image file using Qwen 2.5 VL for high-accuracy multilingual OCR.
    
    Args:
        image_path: Path to the image file
        use_mlx: Whether to use MLX framework (faster on Apple Silicon)
        custom_repo_id: Custom Qwen model repository ID
    
    Returns:
        Extracted text and document object
    """
    
    # Choose VLM options based on parameters
    if custom_repo_id:
        vlm_options = create_custom_qwen_options(custom_repo_id, use_mlx)
    elif use_mlx:
        vlm_options = create_qwen_mlx_options()
    else:
        vlm_options = create_qwen_multilingual_ocr_options()
    
    # Configure VLM pipeline
    pipeline_options = VlmPipelineOptions(
        vlm_options=vlm_options,
        generate_page_images=True,  # Keep images for reference
    )
    
    # Create document converter with VLM pipeline
    converter = DocumentConverter(
        format_options={
            InputFormat.IMAGE: PdfFormatOption(  # Works for images too
                pipeline_cls=VlmPipeline,
                pipeline_options=pipeline_options,
            ),
            InputFormat.PDF: PdfFormatOption(  # Also support PDF input
                pipeline_cls=VlmPipeline,
                pipeline_options=pipeline_options,
            ),
        }
    )
    
    print(f"Processing image: {image_path}")
    print(f"Using model: {vlm_options.repo_id}")
    print(f"Framework: {vlm_options.inference_framework}")
    print("Starting OCR extraction...")
    
    # Convert the image
    result = converter.convert(image_path)
    
    # Extract the text
    extracted_text = result.document.export_to_markdown()
    
    print("OCR extraction completed!")
    print(f"Extracted text length: {len(extracted_text)} characters")
    
    return extracted_text, result.document


def main():
    """
    Example usage of Qwen 2.5 VL for multilingual OCR.
    """
    
    # Example image path - replace with your image
    image_path = "sample_multilingual_document.jpg"  # Change this to your image path
    
    if not Path(image_path).exists():
        print(f"Image not found: {image_path}")
        print("Please provide a valid image path.")
        print("\nSupported formats: JPG, JPEG, PNG, TIFF, BMP, WEBP")
        return
    
    # Example 1: Use standard Qwen 2.5 VL 3B model
    print("=== Method 1: Standard Qwen 2.5 VL (Transformers) ===")
    try:
        extracted_text, doc = process_image_with_qwen_ocr(image_path, use_mlx=False)
        print("\nExtracted Text:")
        print("-" * 50)
        print(extracted_text)
        print("-" * 50)
    except Exception as e:
        print(f"Error with standard Qwen: {e}")
    
    # Example 2: Use MLX version (faster on Apple Silicon)
    print("\n" + "="*60)
    print("=== Method 2: Qwen 2.5 VL MLX (Apple Silicon) ===")
    try:
        extracted_text_mlx, doc_mlx = process_image_with_qwen_ocr(image_path, use_mlx=True)
        print("\nExtracted Text (MLX):")
        print("-" * 50)
        print(extracted_text_mlx)
        print("-" * 50)
    except Exception as e:
        print(f"Error with MLX Qwen: {e}")
    
    # Example 3: Use custom Qwen model
    print("\n" + "="*60)
    print("=== Method 3: Custom Qwen Model ===")
    try:
        # You can specify any Qwen 2.5 VL variant here
        custom_model = "Qwen/Qwen2.5-VL-7B-Instruct"  # Larger model for even better accuracy
        extracted_text_custom, doc_custom = process_image_with_qwen_ocr(
            image_path, 
            use_mlx=False, 
            custom_repo_id=custom_model
        )
        print(f"\nExtracted Text (Custom Model: {custom_model}):")
        print("-" * 50)
        print(extracted_text_custom)
        print("-" * 50)
    except Exception as e:
        print(f"Error with custom Qwen model: {e}")


if __name__ == "__main__":
    main()
