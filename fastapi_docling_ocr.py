#!/usr/bin/env python3
"""
FastAPI endpoints for OCR using Docling with VLM models.
Provides PaddleOCR-like output format with two model options:
1. Qwen 2.5 VL - High accuracy for multilingual text (Chinese, English, handwriting)
2. SmolDocling - Fast inference with native spatial understanding
"""

import os
import tempfile
import traceback
from pathlib import Path
from typing import List, Dict, Any, Optional
from io import BytesIO

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

# Docling imports
from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import VlmPipelineOptions
from docling.datamodel.pipeline_options_vlm_model import (
    InlineVlmOptions,
    InferenceFramework,
    TransformersModelType,
    ResponseFormat
)
from docling.datamodel.accelerator_options import AcceleratorDevice
from docling.pipeline.vlm_pipeline import Vlm<PERSON><PERSON>eline
from docling.datamodel import vlm_model_specs

# Import configuration
from config import (
    QWEN_CONFIG,
    SMOLDOCLING_CONFIG,
    API_CONFIG,
    PERFORMANCE_CONFIG,
    get_optimal_qwen_config,
    get_optimal_smoldocling_config
)


# Response models
class BoundingBox(BaseModel):
    left: float
    top: float
    right: float
    bottom: float
    width: float
    height: float


class OCRResult(BaseModel):
    text: str
    confidence: float
    bbox: BoundingBox
    page: int
    label: str
    from_vlm: bool


class OCRResponse(BaseModel):
    success: bool
    message: str
    results: List[OCRResult]
    total_text_elements: int
    processing_time: float
    model_used: str


# Global converters (initialized once for performance)
qwen_converter = None
smoldocling_converter = None


def create_qwen_converter():
    """Create Qwen 2.5 VL converter optimized for multilingual OCR."""

    # Get optimal configuration for current hardware
    config = get_optimal_qwen_config()

    qwen_vlm_options = InlineVlmOptions(
        repo_id=config.repo_id,
        prompt=config.prompt,
        response_format=ResponseFormat.DOCTAGS,
        inference_framework=config.inference_framework,
        transformers_model_type=config.transformers_model_type,
        supported_devices=config.supported_devices,
        scale=config.scale,
        temperature=config.temperature,
        max_new_tokens=config.max_new_tokens,
        trust_remote_code=config.trust_remote_code,
        quantized=config.quantized,
        load_in_8bit=config.load_in_8bit,
    )

    pipeline_options = VlmPipelineOptions(
        vlm_options=qwen_vlm_options,
        generate_page_images=True,
        force_backend_text=True,  # Use backend OCR for precise coordinates
    )

    converter = DocumentConverter(
        format_options={
            InputFormat.IMAGE: PdfFormatOption(
                pipeline_cls=VlmPipeline,
                pipeline_options=pipeline_options,
            ),
            InputFormat.PDF: PdfFormatOption(
                pipeline_cls=VlmPipeline,
                pipeline_options=pipeline_options,
            ),
        }
    )

    return converter


def create_smoldocling_converter():
    """Create SmolDocling converter for fast OCR with native coordinates."""

    # Get optimal configuration for current hardware
    config = get_optimal_smoldocling_config()

    # Choose appropriate SmolDocling model based on configuration
    if config.use_mlx:
        vlm_options = vlm_model_specs.SMOLDOCLING_MLX
    else:
        vlm_options = vlm_model_specs.SMOLDOCLING_TRANSFORMERS

    pipeline_options = VlmPipelineOptions(
        vlm_options=vlm_options,
        generate_page_images=True,
        force_backend_text=config.force_backend_text,
    )

    converter = DocumentConverter(
        format_options={
            InputFormat.IMAGE: PdfFormatOption(
                pipeline_cls=VlmPipeline,
                pipeline_options=pipeline_options,
            ),
            InputFormat.PDF: PdfFormatOption(
                pipeline_cls=VlmPipeline,
                pipeline_options=pipeline_options,
            ),
        }
    )

    return converter


def extract_text_with_coordinates(result, model_name: str) -> List[OCRResult]:
    """
    Extract text with coordinates from Docling result in PaddleOCR-like format.
    
    Args:
        result: Docling conversion result
        model_name: Name of the model used
    
    Returns:
        List of OCRResult objects with text and coordinates
    """
    
    ocr_results = []
    
    # Extract text with coordinates from document items
    for item, level in result.document.iterate_items():
        if hasattr(item, 'text') and hasattr(item, 'prov') and item.prov:
            for prov in item.prov:
                if hasattr(prov, 'bbox') and prov.bbox:
                    
                    # Create bounding box in PaddleOCR format
                    bbox = BoundingBox(
                        left=prov.bbox.l,
                        top=prov.bbox.t,
                        right=prov.bbox.r,
                        bottom=prov.bbox.b,
                        width=prov.bbox.width,
                        height=prov.bbox.height
                    )
                    
                    # Create OCR result
                    ocr_result = OCRResult(
                        text=item.text,
                        confidence=1.0,  # VLM doesn't provide confidence scores
                        bbox=bbox,
                        page=getattr(prov, 'page_no', 1),
                        label=getattr(item, 'label', 'text'),
                        from_vlm=True
                    )
                    
                    ocr_results.append(ocr_result)
    
    return ocr_results


# Initialize FastAPI app
app = FastAPI(
    title="Docling VLM OCR API",
    description="OCR API using Docling with VLM models (Qwen 2.5 VL and SmolDocling)",
    version="1.0.0"
)


@app.on_event("startup")
async def startup_event():
    """Initialize models on startup."""
    global qwen_converter, smoldocling_converter
    
    print("Initializing VLM models...")
    
    try:
        print("Loading Qwen 2.5 VL model...")
        qwen_converter = create_qwen_converter()
        print("✓ Qwen 2.5 VL model loaded successfully")
    except Exception as e:
        print(f"✗ Failed to load Qwen 2.5 VL model: {e}")
        qwen_converter = None
    
    try:
        print("Loading SmolDocling model...")
        smoldocling_converter = create_smoldocling_converter()
        print("✓ SmolDocling model loaded successfully")
    except Exception as e:
        print(f"✗ Failed to load SmolDocling model: {e}")
        smoldocling_converter = None
    
    print("Model initialization complete!")


@app.post("/ocr/qwen", response_model=OCRResponse)
async def ocr_with_qwen(
    file: UploadFile = File(..., description="Image file for OCR"),
    storage_path: Optional[str] = Form(None, description="Optional path to save processed file")
):
    """
    Perform OCR using Qwen 2.5 VL model.
    
    Optimized for:
    - Traditional Chinese (繁體中文)
    - Simplified Chinese (简体中文)
    - English text
    - Handwritten text
    - Mixed language documents
    
    Returns PaddleOCR-like output with text and bounding box coordinates.
    """
    
    if qwen_converter is None:
        raise HTTPException(status_code=503, detail="Qwen 2.5 VL model not available")
    
    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    import time
    start_time = time.time()
    
    try:
        # Read uploaded file
        file_content = await file.read()
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp_file:
            tmp_file.write(file_content)
            tmp_file_path = tmp_file.name
        
        try:
            # Process with Qwen 2.5 VL
            result = qwen_converter.convert(tmp_file_path)
            
            # Extract text with coordinates
            ocr_results = extract_text_with_coordinates(result, "Qwen 2.5 VL")
            
            processing_time = time.time() - start_time
            
            # Optional: Save to storage path
            if storage_path:
                storage_dir = Path(storage_path)
                storage_dir.mkdir(parents=True, exist_ok=True)
                
                # Save original file
                original_path = storage_dir / file.filename
                with open(original_path, 'wb') as f:
                    f.write(file_content)
                
                # Save OCR results
                results_path = storage_dir / f"{Path(file.filename).stem}_qwen_ocr.json"
                with open(results_path, 'w', encoding='utf-8') as f:
                    import json
                    json.dump([result.dict() for result in ocr_results], f, ensure_ascii=False, indent=2)
            
            return OCRResponse(
                success=True,
                message="OCR completed successfully with Qwen 2.5 VL",
                results=ocr_results,
                total_text_elements=len(ocr_results),
                processing_time=processing_time,
                model_used="Qwen 2.5 VL 3B"
            )
            
        finally:
            # Clean up temporary file
            os.unlink(tmp_file_path)
            
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"OCR processing failed: {str(e)}"
        print(f"Error in Qwen OCR: {error_msg}")
        print(traceback.format_exc())
        
        return OCRResponse(
            success=False,
            message=error_msg,
            results=[],
            total_text_elements=0,
            processing_time=processing_time,
            model_used="Qwen 2.5 VL 3B"
        )


@app.post("/ocr/smoldocling", response_model=OCRResponse)
async def ocr_with_smoldocling(
    file: UploadFile = File(..., description="Image file for OCR"),
    storage_path: Optional[str] = Form(None, description="Optional path to save processed file")
):
    """
    Perform OCR using SmolDocling model.
    
    Features:
    - Fast inference with native spatial understanding
    - Good accuracy for most document types
    - Built-in coordinate extraction
    - Optimized for document layout understanding
    
    Returns PaddleOCR-like output with text and bounding box coordinates.
    """
    
    if smoldocling_converter is None:
        raise HTTPException(status_code=503, detail="SmolDocling model not available")
    
    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    import time
    start_time = time.time()
    
    try:
        # Read uploaded file
        file_content = await file.read()
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp_file:
            tmp_file.write(file_content)
            tmp_file_path = tmp_file.name
        
        try:
            # Process with SmolDocling
            result = smoldocling_converter.convert(tmp_file_path)
            
            # Extract text with coordinates
            ocr_results = extract_text_with_coordinates(result, "SmolDocling")
            
            processing_time = time.time() - start_time
            
            # Optional: Save to storage path
            if storage_path:
                storage_dir = Path(storage_path)
                storage_dir.mkdir(parents=True, exist_ok=True)
                
                # Save original file
                original_path = storage_dir / file.filename
                with open(original_path, 'wb') as f:
                    f.write(file_content)
                
                # Save OCR results
                results_path = storage_dir / f"{Path(file.filename).stem}_smoldocling_ocr.json"
                with open(results_path, 'w', encoding='utf-8') as f:
                    import json
                    json.dump([result.dict() for result in ocr_results], f, ensure_ascii=False, indent=2)
            
            return OCRResponse(
                success=True,
                message="OCR completed successfully with SmolDocling",
                results=ocr_results,
                total_text_elements=len(ocr_results),
                processing_time=processing_time,
                model_used="SmolDocling 256M"
            )
            
        finally:
            # Clean up temporary file
            os.unlink(tmp_file_path)
            
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"OCR processing failed: {str(e)}"
        print(f"Error in SmolDocling OCR: {error_msg}")
        print(traceback.format_exc())
        
        return OCRResponse(
            success=False,
            message=error_msg,
            results=[],
            total_text_elements=0,
            processing_time=processing_time,
            model_used="SmolDocling 256M"
        )


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "qwen_available": qwen_converter is not None,
        "smoldocling_available": smoldocling_converter is not None
    }


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Docling VLM OCR API",
        "endpoints": {
            "/ocr/qwen": "OCR with Qwen 2.5 VL (high accuracy, multilingual)",
            "/ocr/smoldocling": "OCR with SmolDocling (fast, good accuracy)",
            "/health": "Health check",
            "/docs": "API documentation"
        },
        "supported_formats": ["JPG", "JPEG", "PNG", "TIFF", "BMP", "WEBP"]
    }


if __name__ == "__main__":
    # Run the server with configuration
    print(f"Starting Docling VLM OCR API on {API_CONFIG.host}:{API_CONFIG.port}")
    print(f"Qwen Model: {get_optimal_qwen_config().repo_id}")
    print(f"SmolDocling MLX: {get_optimal_smoldocling_config().use_mlx}")

    uvicorn.run(
        "fastapi_docling_ocr:app",
        host=API_CONFIG.host,
        port=API_CONFIG.port,
        reload=API_CONFIG.reload,
        workers=API_CONFIG.workers
    )
