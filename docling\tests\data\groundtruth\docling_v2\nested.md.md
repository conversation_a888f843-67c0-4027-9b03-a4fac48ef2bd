# Nesting

A list featuring nesting:

- abc
    - abc123
        - abc1234
            - abc12345
                - a.
                - b.
        - abcd1234：
            - abcd12345：
                - a.
                - b.
- def：
    - def1234：
        - def12345。
- after one empty line
    - foo
- afer two empty lines
    - bar

- changing symbol

A nested HTML list:

- First item
- Second item with subitems:
    - Subitem 1
    - Subitem 2
- Last list item
