{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "example_02", "origin": {"mimetype": "text/html", "binary_hash": 17361433184833793580, "filename": "example_02.html"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/texts/2"}, "children": [{"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/texts/2"}, "children": [{"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}], "content_layer": "body", "name": "ordered list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}], "content_layer": "body", "label": "title", "prov": [], "orig": "Introduction", "text": "Introduction"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "This is the first paragraph of the introduction.", "text": "This is the first paragraph of the introduction."}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/3"}, {"$ref": "#/groups/0"}, {"$ref": "#/groups/1"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Background", "text": "Background", "level": 1}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/texts/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Some background information here.", "text": "Some background information here."}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "First item in unordered list", "text": "First item in unordered list", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Second item in unordered list", "text": "Second item in unordered list", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "First item in ordered list", "text": "First item in ordered list", "enumerated": true, "marker": ""}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Second item in ordered list", "text": "Second item in ordered list", "enumerated": true, "marker": ""}], "pictures": [], "tables": [], "key_value_items": [], "form_items": [], "pages": {}}