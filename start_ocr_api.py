#!/usr/bin/env python3
"""
Startup script for Docling VLM OCR API.
Handles environment setup, dependency checking, and server startup.
"""

import sys
import os
import subprocess
import importlib.util
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version}")


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        'fastapi',
        'uvicorn',
        'docling',
        'torch',
        'transformers',
        'Pillow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        if importlib.util.find_spec(package) is None:
            missing_packages.append(package)
        else:
            print(f"✅ {package} is installed")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install them with:")
        print(f"pip install {' '.join(missing_packages)}")
        print("\nOr install all requirements:")
        print("pip install -r requirements_ocr_api.txt")
        return False
    
    return True


def check_hardware():
    """Check available hardware and provide optimization suggestions."""
    print("\n🔍 Hardware Detection:")
    
    try:
        import torch
        
        # Check CUDA
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✅ CUDA available: {gpu_count} GPU(s)")
            print(f"   Primary GPU: {gpu_name}")
            print("   Recommendation: Use Qwen 2.5 VL with full precision")
        
        # Check Apple Silicon
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            print("✅ Apple Silicon (MPS) detected")
            print("   Recommendation: Use MLX versions for better performance")
            print("   Set SMOLDOCLING_MLX=true in environment")
        
        else:
            print("⚠️  CPU-only mode detected")
            print("   Recommendation: Enable quantization for better performance")
            print("   Set QWEN_QUANTIZED=true and QWEN_8BIT=true")
        
        # Memory check
        if hasattr(torch.cuda, 'get_device_properties'):
            props = torch.cuda.get_device_properties(0)
            memory_gb = props.total_memory / (1024**3)
            print(f"   GPU Memory: {memory_gb:.1f} GB")
            
            if memory_gb < 8:
                print("   ⚠️  Low GPU memory - consider using quantization")
    
    except ImportError:
        print("❌ PyTorch not installed - cannot detect hardware")
        return False
    
    return True


def setup_environment():
    """Set up environment variables for optimal performance."""
    print("\n⚙️  Environment Setup:")
    
    # Set default environment variables if not already set
    env_defaults = {
        'TOKENIZERS_PARALLELISM': 'false',  # Avoid tokenizer warnings
        'TRANSFORMERS_CACHE': './models_cache',  # Cache models locally
        'HF_HOME': './huggingface_cache',  # Hugging Face cache
    }
    
    for key, value in env_defaults.items():
        if key not in os.environ:
            os.environ[key] = value
            print(f"   Set {key}={value}")
        else:
            print(f"   Using existing {key}={os.environ[key]}")


def create_directories():
    """Create necessary directories."""
    directories = [
        './ocr_results',
        './models_cache',
        './huggingface_cache',
        './logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Directory ready: {directory}")


def print_startup_info():
    """Print startup information and usage instructions."""
    print("\n" + "="*60)
    print("🚀 DOCLING VLM OCR API")
    print("="*60)
    print("API Endpoints:")
    print("  • POST /ocr/qwen        - OCR with Qwen 2.5 VL (high accuracy)")
    print("  • POST /ocr/smoldocling - OCR with SmolDocling (fast)")
    print("  • GET  /health          - Health check")
    print("  • GET  /docs            - API documentation")
    print("\nSupported formats: JPG, JPEG, PNG, TIFF, BMP, WEBP")
    print("\nOptimized for:")
    print("  • Traditional Chinese (繁體中文)")
    print("  • Simplified Chinese (简体中文)")
    print("  • English text")
    print("  • Handwritten text")
    print("="*60)


def main():
    """Main startup function."""
    print("🔧 Starting Docling VLM OCR API Setup...")
    
    # Check system requirements
    check_python_version()
    
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        sys.exit(1)
    
    if not check_hardware():
        print("\n⚠️  Hardware detection failed, but continuing...")
    
    # Setup environment
    setup_environment()
    create_directories()
    
    # Print startup information
    print_startup_info()
    
    # Start the API server
    print("\n🚀 Starting API server...")
    try:
        # Import and run the FastAPI app
        from fastapi_docling_ocr import app
        import uvicorn
        from config import API_CONFIG
        
        uvicorn.run(
            app,
            host=API_CONFIG.host,
            port=API_CONFIG.port,
            reload=API_CONFIG.reload,
            workers=API_CONFIG.workers,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server startup failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
