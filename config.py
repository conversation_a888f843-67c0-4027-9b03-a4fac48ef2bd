#!/usr/bin/env python3
"""
Configuration file for Docling VLM OCR API.
Customize model settings, endpoints, and performance options here.
"""

import os
from dataclasses import dataclass
from typing import List, Optional
from docling.datamodel.accelerator_options import AcceleratorDevice
from docling.datamodel.pipeline_options_vlm_model import Inference<PERSON>ramework, TransformersModelType


@dataclass
class QwenModelConfig:
    """Configuration for Qwen 2.5 VL model."""
    
    # Model repository ID - you can change this to use different Qwen variants
    repo_id: str = "Qwen/Qwen2.5-VL-3B-Instruct"  # Options: 3B, 7B, 14B, 72B
    
    # Alternative models for different use cases:
    # "Qwen/Qwen2.5-VL-7B-Instruct"     # Higher accuracy, slower
    # "Qwen/Qwen2.5-VL-14B-Instruct"    # Even higher accuracy, much slower
    # "mlx-community/Qwen2.5-VL-3B-Instruct-bf16"  # MLX version for Apple Silicon
    
    # Inference settings
    inference_framework: InferenceFramework = InferenceFramework.TRANSFORMERS
    transformers_model_type: TransformersModelType = TransformersModelType.AUTOMODEL_VISION2SEQ
    
    # Performance settings
    scale: float = 2.0  # Image resolution scaling (higher = better quality, slower)
    temperature: float = 0.0  # Deterministic output for OCR
    max_new_tokens: int = 4096  # Maximum output length
    
    # Model loading options
    trust_remote_code: bool = True  # Required for Qwen models
    quantized: bool = False  # Set to True for faster inference, lower accuracy
    load_in_8bit: bool = False  # Set to True to reduce memory usage
    
    # Device preferences (will auto-detect available devices)
    supported_devices: List[AcceleratorDevice] = None
    
    # Custom prompt for multilingual OCR
    prompt: str = """
Perform high-accuracy OCR on this image. Extract ALL text including:
- Traditional Chinese (繁體中文)
- Simplified Chinese (简体中文) 
- English text
- Numbers and symbols
- Handwritten text

Output in DocTags format with precise spatial coordinates using <loc_> tags.
Be extremely accurate with character recognition, especially for Chinese characters.
Maintain proper reading order and document structure.
"""
    
    def __post_init__(self):
        if self.supported_devices is None:
            self.supported_devices = [
                AcceleratorDevice.CPU,
                AcceleratorDevice.CUDA,
                AcceleratorDevice.MPS,
            ]


@dataclass
class SmolDoclingConfig:
    """Configuration for SmolDocling model."""
    
    # Model choice - use MLX version for Apple Silicon, Transformers for others
    use_mlx: bool = False  # Set to True for Apple Silicon (M1/M2/M3)
    
    # Force backend text extraction for more precise coordinates
    force_backend_text: bool = False  # Set to True for hybrid VLM+OCR approach


@dataclass
class APIConfig:
    """Configuration for FastAPI server."""
    
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False  # Set to True for development
    workers: int = 1  # Keep at 1 to avoid model loading issues
    
    # Request limits
    max_file_size: int = 50 * 1024 * 1024  # 50MB max file size
    request_timeout: int = 300  # 5 minutes timeout
    
    # Storage settings
    default_storage_path: Optional[str] = "./ocr_results"
    save_original_files: bool = True
    save_ocr_results: bool = True


@dataclass
class PerformanceConfig:
    """Performance optimization settings."""
    
    # Memory management
    clear_cache_after_request: bool = True  # Clear GPU cache after each request
    
    # Batch processing (not implemented yet, for future use)
    enable_batch_processing: bool = False
    max_batch_size: int = 4
    
    # Model caching
    cache_models: bool = True  # Keep models in memory between requests
    
    # Logging
    log_processing_times: bool = True
    log_memory_usage: bool = False


# Global configuration instances
QWEN_CONFIG = QwenModelConfig()
SMOLDOCLING_CONFIG = SmolDoclingConfig()
API_CONFIG = APIConfig()
PERFORMANCE_CONFIG = PerformanceConfig()


# Environment variable overrides
def load_config_from_env():
    """Load configuration from environment variables."""
    
    # Qwen model configuration
    if os.getenv("QWEN_MODEL_ID"):
        QWEN_CONFIG.repo_id = os.getenv("QWEN_MODEL_ID")
    
    if os.getenv("QWEN_QUANTIZED"):
        QWEN_CONFIG.quantized = os.getenv("QWEN_QUANTIZED").lower() == "true"
    
    if os.getenv("QWEN_8BIT"):
        QWEN_CONFIG.load_in_8bit = os.getenv("QWEN_8BIT").lower() == "true"
    
    if os.getenv("QWEN_SCALE"):
        QWEN_CONFIG.scale = float(os.getenv("QWEN_SCALE"))
    
    # SmolDocling configuration
    if os.getenv("SMOLDOCLING_MLX"):
        SMOLDOCLING_CONFIG.use_mlx = os.getenv("SMOLDOCLING_MLX").lower() == "true"
    
    if os.getenv("SMOLDOCLING_BACKEND_TEXT"):
        SMOLDOCLING_CONFIG.force_backend_text = os.getenv("SMOLDOCLING_BACKEND_TEXT").lower() == "true"
    
    # API configuration
    if os.getenv("API_HOST"):
        API_CONFIG.host = os.getenv("API_HOST")
    
    if os.getenv("API_PORT"):
        API_CONFIG.port = int(os.getenv("API_PORT"))
    
    if os.getenv("API_WORKERS"):
        API_CONFIG.workers = int(os.getenv("API_WORKERS"))
    
    if os.getenv("STORAGE_PATH"):
        API_CONFIG.default_storage_path = os.getenv("STORAGE_PATH")


# Load environment overrides
load_config_from_env()


# Helper functions
def get_optimal_qwen_config():
    """Get optimal Qwen configuration based on available hardware."""
    
    import torch
    
    config = QwenModelConfig()
    
    # Detect available hardware and optimize accordingly
    if torch.cuda.is_available():
        print("🚀 CUDA detected - using GPU acceleration")
        config.quantized = False  # Use full precision on GPU
        config.load_in_8bit = False
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        print("🍎 Apple Silicon detected - optimizing for MPS")
        config.repo_id = "mlx-community/Qwen2.5-VL-3B-Instruct-bf16"
        config.inference_framework = InferenceFramework.MLX
        config.supported_devices = [AcceleratorDevice.MPS]
    else:
        print("💻 CPU-only mode - enabling optimizations")
        config.quantized = True  # Use quantization for CPU
        config.load_in_8bit = True
        config.scale = 1.5  # Reduce resolution for faster CPU processing
    
    return config


def get_optimal_smoldocling_config():
    """Get optimal SmolDocling configuration based on available hardware."""
    
    import torch
    
    config = SmolDoclingConfig()
    
    # Use MLX on Apple Silicon for better performance
    if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        config.use_mlx = True
        print("🍎 Using SmolDocling MLX for Apple Silicon")
    else:
        config.use_mlx = False
        print("🔧 Using SmolDocling Transformers")
    
    return config


if __name__ == "__main__":
    # Print current configuration
    print("Current Configuration:")
    print("=" * 50)
    print(f"Qwen Model: {QWEN_CONFIG.repo_id}")
    print(f"Qwen Framework: {QWEN_CONFIG.inference_framework}")
    print(f"SmolDocling MLX: {SMOLDOCLING_CONFIG.use_mlx}")
    print(f"API Host: {API_CONFIG.host}:{API_CONFIG.port}")
    print(f"Storage Path: {API_CONFIG.default_storage_path}")
    print("=" * 50)
