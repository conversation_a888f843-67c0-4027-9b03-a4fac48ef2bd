<doctag><text><loc_235><loc_18><loc_342><loc_32>Front cover</text>
<picture><loc_419><loc_16><loc_479><loc_35></picture>
<section_header_level_1><loc_29><loc_53><loc_478><loc_105>Row and Column Access Control Support in IBM DB2 for i</section_header_level_1>
<picture><loc_27><loc_185><loc_478><loc_443></picture>
<picture><loc_259><loc_448><loc_475><loc_489></picture>
<page_footer><loc_30><loc_474><loc_134><loc_483>ibm.com /redbooks</page_footer>
<page_break>
<section_header_level_1><loc_53><loc_47><loc_138><loc_61>Contents</section_header_level_1>
<otsl><loc_111><loc_83><loc_447><loc_452><rhed>Notices<fcel>. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . vii<nl><rhed>Trademarks<fcel>. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . viii<nl><rhed>DB2 for i Center of Excellence<fcel>. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . ix<nl><rhed>Preface<fcel>. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . xi<nl><rhed>Authors . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . xi<ecel><nl><rhed>Now you can become a published author, too! . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>xiii<nl><rhed>Comments welcome. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>xiii<nl><rhed>Stay connected to IBM Redbooks<fcel>. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . xiv<nl><rhed>Chapter 1. Securing and protecting IBM DB2 data<fcel>. . . . . . . . . . . . . . . . . . . . . . . . . . . . . 1<nl><rhed>1.1 Security fundamentals. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 2<ecel><nl><rhed>1.2 Current state of IBM i security . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>2<nl><rhed>1.3 DB2 for i security controls . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 3<ecel><nl><rhed>1.3.1 Existing row and column control . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>4<nl><rhed>1.3.2 New controls: Row and Column Access Control. . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>5<nl><rhed>Chapter 2. Roles and separation of duties . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>7<nl><rhed>2.1 Roles . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>8<nl><rhed>2.1.1 DDM and DRDA application server access: QIBM_DB_DDMDRDA . . . . . . . . . . .<fcel>8<nl><rhed>2.1.2 Toolbox application server access: QIBM_DB_ZDA. . . . . . . . . . . . . . . . . . . . . . . .<fcel>8<nl><rhed>2.1.3 Database Administrator function: QIBM_DB_SQLADM . . . . . . . . . . . . . . . . . . . . .<fcel>9<nl><rhed>2.1.4 Database Information function: QIBM_DB_SYSMON<fcel>. . . . . . . . . . . . . . . . . . . . . . 9<nl><rhed>2.1.5 Security Administrator function: QIBM_DB_SECADM . . . . . . . . . . . . . . . . . . . . . .<fcel>9<nl><rhed>2.1.6 Change Function Usage CL command . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>10<nl><rhed>2.1.7 Verifying function usage IDs for RCAC with the FUNCTION_USAGE view . . . . .<fcel>10<nl><rhed>2.2 Separation of duties . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 10<ecel><nl><rhed>Chapter 3. Row and Column Access Control<fcel>. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 13<nl><rhed>3.1 Explanation of RCAC and the concept of access control . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>14<nl><rhed>3.1.1 Row permission and column mask definitions<fcel>14<nl><rhed>3.1.2 Enabling and activating RCAC . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>16<nl><rhed>3.2 Special registers and built-in global variables . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>18<nl><rhed>3.2.1 Special registers . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>18<nl><rhed>3.2.2 Built-in global variables . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>19<nl><rhed>3.3 VERIFY_GROUP_FOR_USER function . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>20<nl><rhed>3.4 Establishing and controlling accessibility by using the RCAC rule text . . . . . . . . . . . . .<fcel>21<nl><rhed>Human resources example . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<ecel><nl><rhed>3.6<fcel>22<nl><rhed>3.6.1 Assigning the QIBM_DB_SECADM function ID to the consultants. . . . . . . . . . . .<fcel>23 23<nl><rhed>3.6.2 Creating group profiles for the users and their roles . . . . . . . . . . . . . . . . . . . . . . .<ecel><nl><rhed>3.6.3 Demonstrating data access without RCAC . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>24<nl><rhed>3.6.4 Defining and creating row permissions . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>25<nl><rhed>3.6.5 Defining and creating column masks<fcel>. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 26<nl><rhed>3.6.6 Activating RCAC . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<fcel>28<nl><rhed>3.6.8 Demonstrating data access with a view and RCAC . . . . . . . . . . . . . . . . . . . . . . .<fcel>32<nl></otsl>
<page_footer><loc_53><loc_477><loc_210><loc_482>' Copyright IBM Corp. 2014. All rights reserved.</page_footer>
<page_footer><loc_440><loc_477><loc_447><loc_482>iii</page_footer>
<page_break>
<text><loc_53><loc_47><loc_193><loc_54>DB2 for i Center of Excellence</text>
<text><loc_77><loc_87><loc_191><loc_98>Solution Brief IBM Systems Lab Services and Training</text>
<picture><loc_117><loc_171><loc_147><loc_180></picture>
<section_header_level_1><loc_118><loc_207><loc_154><loc_213>Highlights</section_header_level_1>
<unordered_list><list_item><loc_118><loc_218><loc_198><loc_226>GLYPH<g115>GLYPH<g3> GLYPH<g40>GLYPH<g81>GLYPH<g75>GLYPH<g68>GLYPH<g81>GLYPH<g70>GLYPH<g72>GLYPH<g3> GLYPH<g87>GLYPH<g75>GLYPH<g72>GLYPH<g3> GLYPH<g83>GLYPH<g72>GLYPH<g85>GLYPH<g73>GLYPH<g82>GLYPH<g85>GLYPH<g80>GLYPH<g68>GLYPH<g81>GLYPH<g70>GLYPH<g72>GLYPH<g3> GLYPH<g82>GLYPH<g73>GLYPH<g3> GLYPH<g92>GLYPH<g82>GLYPH<g88>GLYPH<g85> GLYPH<g3> GLYPH<g71>GLYPH<g68>GLYPH<g87>GLYPH<g68>GLYPH<g69>GLYPH<g68>GLYPH<g86>GLYPH<g72>GLYPH<g3> GLYPH<g82>GLYPH<g83>GLYPH<g72>GLYPH<g85>GLYPH<g68>GLYPH<g87>GLYPH<g76>GLYPH<g82>GLYPH<g81>GLYPH<g86></list_item>
<list_item><loc_118><loc_232><loc_212><loc_246>GLYPH<g115>GLYPH<g3> GLYPH<g40>GLYPH<g68>GLYPH<g85> GLYPH<g81>GLYPH<g3> GLYPH<g74>GLYPH<g85>GLYPH<g72>GLYPH<g68>GLYPH<g87>GLYPH<g72>GLYPH<g85>GLYPH<g3> GLYPH<g85>GLYPH<g72>GLYPH<g87>GLYPH<g88>GLYPH<g85> GLYPH<g81>GLYPH<g3> GLYPH<g82>GLYPH<g81>GLYPH<g3> GLYPH<g44>GLYPH<g55>GLYPH<g3> GLYPH<g83>GLYPH<g85>GLYPH<g82>GLYPH<g77>GLYPH<g72>GLYPH<g70>GLYPH<g87>GLYPH<g86> GLYPH<g3> GLYPH<g87>GLYPH<g75>GLYPH<g85>GLYPH<g82>GLYPH<g88>GLYPH<g74>GLYPH<g75>GLYPH<g3> GLYPH<g80>GLYPH<g82>GLYPH<g71>GLYPH<g72>GLYPH<g85> GLYPH<g81>GLYPH<g76>GLYPH<g93>GLYPH<g68>GLYPH<g87>GLYPH<g76>GLYPH<g82>GLYPH<g81>GLYPH<g3> GLYPH<g82>GLYPH<g73>GLYPH<g3> GLYPH<g71>GLYPH<g68>GLYPH<g87>GLYPH<g68>GLYPH<g69>GLYPH<g68>GLYPH<g86>GLYPH<g72>GLYPH<g3> GLYPH<g68>GLYPH<g81>GLYPH<g71> GLYPH<g3> GLYPH<g68>GLYPH<g83>GLYPH<g83>GLYPH<g79>GLYPH<g76>GLYPH<g70>GLYPH<g68>GLYPH<g87>GLYPH<g76>GLYPH<g82>GLYPH<g81>GLYPH<g86></list_item>
<list_item><loc_118><loc_252><loc_204><loc_260>GLYPH<g115>GLYPH<g3> GLYPH<g53>GLYPH<g72>GLYPH<g79>GLYPH<g92>GLYPH<g3> GLYPH<g82>GLYPH<g81>GLYPH<g3> GLYPH<g44>GLYPH<g37>GLYPH<g48>GLYPH<g3> GLYPH<g72>GLYPH<g91>GLYPH<g83>GLYPH<g72>GLYPH<g85>GLYPH<g87>GLYPH<g3> GLYPH<g70>GLYPH<g82>GLYPH<g81>GLYPH<g86>GLYPH<g88>GLYPH<g79>GLYPH<g87>GLYPH<g76>GLYPH<g81>GLYPH<g74>GLYPH<g15>GLYPH<g3> GLYPH<g86>GLYPH<g78>GLYPH<g76>GLYPH<g79>GLYPH<g79>GLYPH<g86> GLYPH<g3> GLYPH<g86>GLYPH<g75>GLYPH<g68>GLYPH<g85>GLYPH<g76>GLYPH<g81>GLYPH<g74>GLYPH<g3> GLYPH<g68>GLYPH<g81>GLYPH<g71>GLYPH<g3> GLYPH<g85>GLYPH<g72>GLYPH<g81>GLYPH<g82>GLYPH<g90>GLYPH<g81>GLYPH<g3> GLYPH<g86>GLYPH<g72>GLYPH<g85>GLYPH<g89>GLYPH<g76>GLYPH<g70>GLYPH<g72>GLYPH<g86></list_item>
<list_item><loc_118><loc_266><loc_191><loc_274>GLYPH<g115>GLYPH<g3> GLYPH<g55> GLYPH<g68>GLYPH<g78>GLYPH<g72>GLYPH<g3> GLYPH<g68>GLYPH<g71>GLYPH<g89>GLYPH<g68>GLYPH<g81>GLYPH<g87>GLYPH<g68>GLYPH<g74>GLYPH<g72>GLYPH<g3> GLYPH<g82>GLYPH<g73>GLYPH<g3> GLYPH<g68>GLYPH<g70>GLYPH<g70>GLYPH<g72>GLYPH<g86>GLYPH<g86>GLYPH<g3> GLYPH<g87>GLYPH<g82>GLYPH<g3> GLYPH<g68> GLYPH<g3> GLYPH<g90>GLYPH<g82>GLYPH<g85>GLYPH<g79>GLYPH<g71>GLYPH<g90>GLYPH<g76>GLYPH<g71>GLYPH<g72>GLYPH<g3> GLYPH<g86>GLYPH<g82>GLYPH<g88>GLYPH<g85>GLYPH<g70>GLYPH<g72>GLYPH<g3> GLYPH<g82>GLYPH<g73>GLYPH<g3> GLYPH<g72>GLYPH<g91>GLYPH<g83>GLYPH<g72>GLYPH<g85>GLYPH<g87>GLYPH<g76>GLYPH<g86>GLYPH<g72></list_item>
</unordered_list>
<picture><loc_52><loc_381><loc_211><loc_434></picture>
<text><loc_377><loc_87><loc_414><loc_92>Power Services</text>
<section_header_level_1><loc_229><loc_151><loc_379><loc_175>DB2 for i Center of Excellence</section_header_level_1>
<text><loc_229><loc_175><loc_395><loc_181>Expert help to achieve your business requirements</text>
<section_header_level_1><loc_229><loc_199><loc_362><loc_205>We build confident, satisfied clients</section_header_level_1>
<text><loc_229><loc_207><loc_399><loc_218>No one else has the vast consulting experiences, skills sharing and renown service offerings to do what we can do for you.</text>
<text><loc_229><loc_226><loc_301><loc_230>Because no one else is IBM.</text>
<text><loc_229><loc_238><loc_409><loc_268>With combined experiences and direct access to development groups, we're the experts in IBM DB2® for i. The DB2 for i Center of Excellence (CoE) can help you achieve-perhaps reexamine and exceed-your business requirements and gain more confidence and satisfaction in IBM product data management products and solutions.</text>
<section_header_level_1><loc_229><loc_276><loc_355><loc_282>Who we are, some of what we do</section_header_level_1>
<text><loc_229><loc_284><loc_355><loc_288>Global CoE engagements cover topics including:</text>
<unordered_list><list_item><loc_229><loc_296><loc_328><loc_301>r Database performance and scalability</list_item>
<list_item><loc_229><loc_303><loc_347><loc_307>r Advanced SQL knowledge and skills transfer</list_item>
<list_item><loc_229><loc_309><loc_320><loc_314>r Business intelligence and analytics</list_item>
<list_item><loc_229><loc_315><loc_278><loc_320>r DB2 Web Query</list_item>
<list_item><loc_229><loc_322><loc_412><loc_327>r Query/400 modernization for better reporting and analysis capabilities</list_item>
<list_item><loc_229><loc_328><loc_346><loc_333>r Database modernization and re-engineering</list_item>
<list_item><loc_229><loc_335><loc_327><loc_339>r Data-centric architecture and design</list_item>
<list_item><loc_229><loc_341><loc_381><loc_346>r Extremely large database and overcoming limits to growth</list_item>
<list_item><loc_229><loc_348><loc_312><loc_352>r ISV education and enablement</list_item>
</unordered_list>
<page_break>
<section_header_level_1><loc_53><loc_47><loc_124><loc_61>Preface</section_header_level_1>
<text><loc_112><loc_84><loc_447><loc_127>This IBMfi Redpaper™ publication provides information about the IBM i 7.2 feature of IBM DB2fi for i Row and Column Access Control (RCAC). It offers a broad description of the function and advantages of controlling access to data in a comprehensive and transparent way. This publication helps you understand the capabilities of RCAC and provides examples of defining, creating, and implementing the row permissions and column masks in a relational database environment.</text>
<text><loc_112><loc_135><loc_446><loc_164>This paper is intended for database engineers, data-centric application developers, and security officers who want to design and implement RCAC as a part of their data control and governance policy. A solid background in IBM i object level security, DB2 for i relational database concepts, and SQL is assumed.</text>
<text><loc_112><loc_202><loc_447><loc_216>This paper was produced by the IBM DB2 for i Center of Excellence team in partnership with the International Technical Support Organization (ITSO), Rochester, Minnesota US.</text>
<picture><loc_116><loc_237><loc_205><loc_318></picture>
<text><loc_215><loc_237><loc_442><loc_326>Jim Bainbridge is a senior DB2 consultant on the DB2 for i Center of Excellence team in the IBM Lab Services and Training organization. His primary role is training and implementation services for IBM DB2 Web Query for i and business analytics. Jim began his career with IBM 30 years ago in the IBM Rochester Development Lab, where he developed cooperative processing products that paired IBM PCs with IBM S/36 and AS/.400 systems. In the years since, Jim has held numerous technical roles, including independent software vendors technical support on a broad range of IBM technologies and products, and supporting customers in the IBM Executive Briefing Center and IBM Project Office.</text>
<picture><loc_119><loc_333><loc_206><loc_401></picture>
<text><loc_215><loc_333><loc_442><loc_430>Hernando Bedoya is a Senior IT Specialist at STG Lab Services and Training in Rochester, Minnesota. He writes extensively and teaches IBM classes worldwide in all areas of DB2 for i. Before joining STG Lab Services, he worked in the ITSO for nine years writing multiple IBM Redbooksfi publications. He also worked for IBM Colombia as an IBM AS/400fi IT Specialist doing presales support for the Andean countries. He has 28 years of experience in the computing field and has taught database classes in Colombian universities. He holds a Master's degree in Computer Science from EAFIT, Colombia. His areas of expertise are database technology, performance, and data warehousing. Hernando can be <NAME_EMAIL> .</text>
<section_header_level_1><loc_53><loc_182><loc_102><loc_191>Authors</section_header_level_1>
<page_footer><loc_53><loc_477><loc_210><loc_482>' Copyright IBM Corp. 2014. All rights reserved.</page_footer>
<page_footer><loc_440><loc_477><loc_447><loc_482>xi</page_footer>
<page_break>
<picture><loc_26><loc_45><loc_196><loc_150></picture>
<text><loc_409><loc_59><loc_427><loc_82>1</text>
<text><loc_66><loc_170><loc_94><loc_174>Chapter 1.</text>
<section_header_level_1><loc_112><loc_161><loc_447><loc_196>Securing and protecting IBM DB2 data</section_header_level_1>
<text><loc_112><loc_220><loc_447><loc_271>Recent news headlines are filled with reports of data breaches and cyber-attacks impacting global businesses of all sizes. The Identity Theft Resource Center$^{1}$ reports that almost 5000 data breaches have occurred since 2005, exposing over 600 million records of data. The financial cost of these data breaches is skyrocketing. Studies from the Ponemon Institute$^{2}$ revealed that the average cost of a data breach increased in 2013 by 15% globally and resulted in a brand equity loss of $9.4 million per attack. The average cost that is incurred for each lost record containing sensitive information increased more than 9% to $145 per record.</text>
<text><loc_112><loc_279><loc_431><loc_308>Businesses must make a serious effort to secure their data and recognize that securing information assets is a cost of doing business. In many parts of the world and in many industries, securing the data is required by law and subject to audits. Data security is no longer an option; it is a requirement.</text>
<text><loc_112><loc_316><loc_447><loc_329>This chapter describes how you can secure and protect data in DB2 for i. The following topics are covered in this chapter:</text>
<unordered_list><list_item><loc_112><loc_334><loc_204><loc_340>GLYPH<SM590000> Security fundamentals</list_item>
<list_item><loc_112><loc_342><loc_231><loc_348>GLYPH<SM590000> Current state of IBM i security</list_item>
<list_item><loc_112><loc_350><loc_216><loc_355>GLYPH<SM590000> DB2 for i security controls</list_item>
</unordered_list>
<footnote><loc_112><loc_453><loc_211><loc_458>$^{1 }$http://www.idtheftcenter.org</footnote>
<footnote><loc_112><loc_459><loc_191><loc_464>$^{2 }$http://www.ponemon.org /</footnote>
<page_footer><loc_53><loc_477><loc_210><loc_482>' Copyright IBM Corp. 2014. All rights reserved.</page_footer>
<page_footer><loc_443><loc_477><loc_447><loc_482>1</page_footer>
<page_break>
<section_header_level_1><loc_53><loc_47><loc_218><loc_56>1.1 Security fundamentals</section_header_level_1>
<text><loc_112><loc_67><loc_445><loc_81>Before reviewing database security techniques, there are two fundamental steps in securing information assets that must be described:</text>
<unordered_list><list_item><loc_112><loc_85><loc_447><loc_114>GLYPH<SM590000> First, and most important, is the definition of a company's security policy . Without a security policy, there is no definition of what are acceptable practices for using, accessing, and storing information by who, what, when, where, and how. A security policy should minimally address three things: confidentiality, integrity, and availability.</list_item>
<list_item><loc_124><loc_119><loc_447><loc_170>The monitoring and assessment of adherence to the security policy determines whether your security strategy is working. Often, IBM security consultants are asked to perform security assessments for companies without regard to the security policy. Although these assessments can be useful for observing how the system is defined and how data is being accessed, they cannot determine the level of security without a security policy. Without a security policy, it really is not an assessment as much as it is a baseline for monitoring the changes in the security settings that are captured.</list_item>
</unordered_list>
<text><loc_124><loc_175><loc_443><loc_181>A security policy is what defines whether the system and its settings are secure (or not).</text>
<unordered_list><list_item><loc_112><loc_186><loc_447><loc_237>GLYPH<SM590000> The second fundamental in securing data assets is the use of resource security . If implemented properly, resource security prevents data breaches from both internal and external intrusions. Resource security controls are closely tied to the part of the security policy that defines who should have access to what information resources. A hacker might be good enough to get through your company firewalls and sift his way through to your system, but if they do not have explicit access to your database, the hacker cannot compromise your information assets.</list_item>
</unordered_list>
<text><loc_112><loc_245><loc_437><loc_259>With your eyes now open to the importance of securing information assets, the rest of this chapter reviews the methods that are available for securing database resources on IBM i.</text>
<section_header_level_1><loc_53><loc_277><loc_264><loc_286>1.2 Current state of IBM i security</section_header_level_1>
<text><loc_112><loc_297><loc_447><loc_326>Because of the inherently secure nature of IBM i, many clients rely on the default system settings to protect their business data that is stored in DB2 for i. In most cases, this means no data protection because the default setting for the Create default public authority (QCRTAUT) system value is *CHANGE.</text>
<text><loc_112><loc_334><loc_447><loc_370>Even more disturbing is that many IBM i clients remain in this state, despite the news headlines and the significant costs that are involved with databases being compromised. This default security configuration makes it quite challenging to implement basic security policies. A tighter implementation is required if you really want to protect one of your company's most valuable assets, which is the data.</text>
<text><loc_112><loc_378><loc_447><loc_429>Traditionally, IBM i applications have employed menu-based security to counteract this default configuration that gives all users access to the data. The theory is that data is protected by the menu options controlling what database operations that the user can perform. This approach is ineffective, even if the user profile is restricted from running interactive commands. The reason is that in today's connected world there are a multitude of interfaces into the system, from web browsers to PC clients, that bypass application menus. If there are no object-level controls, users of these newer interfaces have an open door to your data.</text>
<page_footer><loc_53><loc_477><loc_59><loc_482>2</page_footer>
<page_footer><loc_72><loc_477><loc_269><loc_482>Row and Column Access Control Support in IBM DB2 for i</page_footer>
<page_break>
<text><loc_112><loc_45><loc_445><loc_96>Many businesses are trying to limit data access to a need-to-know basis. This security goal means that users should be given access only to the minimum set of data that is required to perform their job. Often, users with object-level access are given access to row and column values that are beyond what their business task requires because that object-level security provides an all-or-nothing solution. For example, object-level controls allow a manager to access data about all employees. Most security policies limit a manager to accessing data only for the employees that they manage.</text>
<section_header_level_1><loc_53><loc_109><loc_246><loc_117>1.3.1 Existing row and column control</section_header_level_1>
<text><loc_112><loc_126><loc_442><loc_162>Some IBM i clients have tried augmenting the all-or-nothing object-level security with SQL views (or logical files) and application logic, as shown in Figure 1-2. However, application-based logic is easy to bypass with all of the different data access interfaces that are provided by the IBM i operating system, such as Open Database Connectivity (ODBC) and System i Navigator.</text>
<text><loc_112><loc_170><loc_447><loc_199>Using SQL views to limit access to a subset of the data in a table also has its own set of challenges. First, there is the complexity of managing all of the SQL view objects that are used for securing data access. Second, scaling a view-based security solution can be difficult as the amount of data grows and the number of users increases.</text>
<text><loc_112><loc_207><loc_447><loc_228>Even if you are willing to live with these performance and management issues, a user with *ALLOBJ access still can directly access all of the data in the underlying DB2 table and easily bypass the security controls that are built into an SQL view.</text>
<picture><loc_111><loc_237><loc_446><loc_435><caption><loc_112><loc_437><loc_259><loc_442>Figure 1-2 Existing row and column controls</caption></picture>
<page_footer><loc_53><loc_477><loc_59><loc_482>4</page_footer>
<page_footer><loc_72><loc_477><loc_269><loc_482>Row and Column Access Control Support in IBM DB2 for i</page_footer>
<page_break>
<section_header_level_1><loc_53><loc_45><loc_274><loc_53>2.1.6 Change Function Usage CL command</section_header_level_1>
<text><loc_112><loc_62><loc_447><loc_67>The following CL commands can be used to work with, display, or change function usage IDs:</text>
<unordered_list><list_item><loc_112><loc_72><loc_246><loc_78>GLYPH<SM590000> Work Function Usage ( WRKFCNUSG )</list_item>
<list_item><loc_112><loc_80><loc_256><loc_86>GLYPH<SM590000> Change Function Usage ( CHGFCNUSG )</list_item>
<list_item><loc_112><loc_87><loc_254><loc_93>GLYPH<SM590000> Display Function Usage ( DSPFCNUSG )</list_item>
</unordered_list>
<text><loc_112><loc_101><loc_419><loc_115>For example, the following CHGFCNUSG command shows granting authorization to user HBEDOYA to administer and manage RCAC rules:</text>
<text><loc_112><loc_120><loc_361><loc_125>CHGFCNUSG FCNID(QIBM_DB_SECADM) USER(HBEDOYA) USAGE(*ALLOWED)</text>
<section_header_level_1><loc_53><loc_138><loc_445><loc_146>2.1.7 Verifying function usage IDs for RCAC with the FUNCTION_USAGE view</section_header_level_1>
<text><loc_112><loc_155><loc_424><loc_168>The FUNCTION_USAGE view contains function usage configuration details. Table 2-1 describes the columns in the FUNCTION_USAGE view.</text>
<otsl><loc_111><loc_183><loc_446><loc_279><ched>Column name<ched>Data type<ched>Description<nl><fcel>FUNCTION_ID<fcel>VARCHAR(30)<fcel>ID of the function.<nl><fcel>USER_NAME<fcel>VARCHAR(10)<fcel>Name of the user profile that has a usage setting for this  function.<nl><fcel>USAGE<fcel>VARCHAR(7)<fcel>Usage setting: GLYPH<SM590000> ALLOWED: The user profile is allowed to use the function. GLYPH<SM590000> DENIED: The user profile is not allowed to use the function.<nl><fcel>USER_TYPE<fcel>VARCHAR(5)<fcel>Type of user profile: GLYPH<SM590000> USER: The user profile is a user. GLYPH<SM590000> GROUP: The user profile is a group.<nl><caption><loc_112><loc_176><loc_232><loc_182>Table 2-1 FUNCTION_USAGE view</caption></otsl>
<text><loc_112><loc_286><loc_447><loc_299>To discover who has authorization to define and manage RCAC, you can use the query that is shown in Example 2-1.</text>
<caption><loc_112><loc_307><loc_378><loc_312>Example 2-1 Query to determine who has authority to define and manage RCAC</caption>
<text><loc_112><loc_318><loc_140><loc_324>SELECT</text>
<text><loc_149><loc_318><loc_206><loc_324>function_id,</text>
<text><loc_136><loc_326><loc_197><loc_331>user_name,</text>
<text><loc_140><loc_333><loc_181><loc_339>usage,</text>
<text><loc_137><loc_341><loc_193><loc_346>user_type</text>
<text><loc_112><loc_348><loc_131><loc_354>FROM</text>
<text><loc_146><loc_348><loc_214><loc_354>function_usage</text>
<text><loc_112><loc_356><loc_133><loc_361>WHERE</text>
<text><loc_145><loc_356><loc_271><loc_361>function_id=’QIBM_DB_SECADM’</text>
<text><loc_112><loc_363><loc_146><loc_369>ORDER BY</text>
<text><loc_155><loc_363><loc_197><loc_369>user_name;</text>
<section_header_level_1><loc_53><loc_392><loc_204><loc_401>2.2 Separation of duties</section_header_level_1>
<text><loc_112><loc_412><loc_447><loc_448>Separation of duties helps businesses comply with industry regulations or organizational requirements and simplifies the management of authorities. Separation of duties is commonly used to prevent fraudulent activities or errors by a single person. It provides the ability for administrative functions to be divided across individuals without overlapping responsibilities, so that one user does not possess unlimited authority, such as with the *ALLOBJ authority.</text>
<page_footer><loc_53><loc_477><loc_64><loc_482>10</page_footer>
<page_footer><loc_76><loc_477><loc_273><loc_482>Row and Column Access Control Support in IBM DB2 for i</page_footer>
<page_break>
<text><loc_112><loc_45><loc_443><loc_89>For example, assume that a business has assigned the duty to manage security on IBM i to Theresa. Before release IBM i 7.2, to grant privileges, Theresa had to have the same privileges Theresa was granting to others. Therefore, to grant *USE privileges to the PAYROLL table, Theresa had to have *OBJMGT and *USE authority (or a higher level of authority, such as *ALLOBJ). This requirement allowed Theresa to access the data in the PAYROLL table even though Theresa's job description was only to manage its security.</text>
<text><loc_112><loc_97><loc_447><loc_125>In IBM i 7.2, the QIBM_DB_SECADM function usage grants authorities, revokes authorities, changes ownership, or changes the primary group without giving access to the object or, in the case of a database table, to the data that is in the table or allowing other operations on the table.</text>
<text><loc_112><loc_134><loc_440><loc_147>QIBM_DB_SECADM function usage can be granted only by a user with *SECADM special authority and can be given to a user or a group.</text>
<text><loc_112><loc_155><loc_446><loc_176>QIBM_DB_SECADM also is responsible for administering RCAC, which restricts which rows a user is allowed to access in a table and whether a user is allowed to see information in certain columns of a table.</text>
<text><loc_112><loc_184><loc_441><loc_213>A preferred practice is that the RCAC administrator has the QIBM_DB_SECADM function usage ID, but absolutely no other data privileges. The result is that the RCAC administrator can deploy and maintain the RCAC constructs, but cannot grant themselves unauthorized access to data itself.</text>
<text><loc_112><loc_221><loc_444><loc_234>Table 2-2 shows a comparison of the different function usage IDs and *JOBCTL authority to the different CL commands and DB2 for i tools.</text>
<otsl><loc_53><loc_248><loc_447><loc_456><fcel>User action<ched>*JOBCTL<ched>QIBM_DB_SECADM<ched>QIBM_DB_SQLADM<ched>QIBM_DB_SYSMON<ched>No Authority<nl><rhed>SET CURRENT DEGREE  (SQL statement)<fcel>X<ecel><fcel>X<ecel><ecel><nl><rhed>CHGQRYA  command targeting a different user’s job<fcel>X<ecel><fcel>X<ecel><ecel><nl><rhed>STRDBMON  or  ENDDBMON  commands targeting a different user’s job<fcel>X<ecel><fcel>X<ecel><ecel><nl><rhed>STRDBMON  or  ENDDBMON  commands targeting a job that matches the current user<fcel>X<ecel><fcel>X<fcel>X<fcel>X<nl><rhed>QUSRJOBI() API format 900 or System i Navigator’s SQL Details for Job<fcel>X<ecel><fcel>X<fcel>X<ecel><nl><rhed>Visual Explain within Run SQL scripts<fcel>X<ecel><fcel>X<fcel>X<fcel>X<nl><rhed>Visual Explain outside of Run SQL scripts<fcel>X<ecel><fcel>X<ecel><ecel><nl><rhed>ANALYZE PLAN CACHE procedure<fcel>X<ecel><fcel>X<ecel><ecel><nl><rhed>DUMP PLAN CACHE procedure<fcel>X<ecel><fcel>X<ecel><ecel><nl><rhed>MODIFY PLAN CACHE procedure<fcel>X<ecel><fcel>X<ecel><ecel><nl><rhed>MODIFY PLAN CACHE PROPERTIES procedure (currently does not check authority)<fcel>X<ecel><fcel>X<ecel><ecel><nl><rhed>CHANGE PLAN CACHE SIZE procedure (currently does not check authority)<fcel>X<ecel><fcel>X<ecel><ecel><nl><caption><loc_53><loc_242><loc_320><loc_247>Table 2-2 Comparison of the different function usage IDs and *JOBCTL authority</caption></otsl>
<page_footer><loc_290><loc_477><loc_428><loc_482>Chapter 2. Roles and separation of duties</page_footer>
<page_footer><loc_438><loc_477><loc_447><loc_482>11</page_footer>
<page_break>
<caption><loc_112><loc_45><loc_432><loc_59>The SQL CREATE PERMISSION statement that is shown in Figure 3-1 is used to define and initially enable or disable the row access rules.</caption>
<picture><loc_111><loc_68><loc_446><loc_259><caption><loc_112><loc_261><loc_279><loc_267>Figure 3-1 CREATE PERMISSION SQL statement</caption></picture>
<section_header_level_1><loc_112><loc_278><loc_176><loc_285>Column mask</section_header_level_1>
<text><loc_112><loc_287><loc_443><loc_316>A column mask is a database object that manifests a column value access control rule for a specific column in a specific table. It uses a CASE expression that describes what you see when you access the column. For example, a teller can see only the last four digits of a tax identification number.</text>
<page_footer><loc_282><loc_477><loc_428><loc_482>Chapter 3. Row and Column Access Control</page_footer>
<page_footer><loc_438><loc_477><loc_447><loc_482>15</page_footer>
<page_break>
<caption><loc_112><loc_45><loc_337><loc_51>Table 3-1 summarizes these special registers and their values.</caption>
<otsl><loc_110><loc_65><loc_443><loc_129><ched>Special register<ched>Corresponding value<nl><fcel>USER or SESSION_USER<fcel>The effective user of the thread excluding adopted authority.<nl><fcel>CURRENT_USER<fcel>The effective user of the thread including adopted authority. When no adopted  authority is present, this has the same value as USER.<nl><fcel>SYSTEM_USER<fcel>The authorization ID that initiated the connection.<nl><caption><loc_112><loc_59><loc_304><loc_64>Table 3-1 Special registers and their corresponding values</caption></otsl>
<text><loc_112><loc_135><loc_440><loc_149>Figure 3-5 shows the difference in the special register values when an adopted authority is used:</text>
<unordered_list><list_item><loc_112><loc_154><loc_336><loc_160>GLYPH<SM590000> A user connects to the server using the user profile ALICE.</list_item>
<list_item><loc_112><loc_164><loc_370><loc_170>GLYPH<SM590000> USER and CURRENT USER initially have the same value of ALICE.</list_item>
<list_item><loc_112><loc_175><loc_442><loc_189>GLYPH<SM590000> ALICE calls an SQL procedure that is named proc1, which is owned by user profile JOE and was created to adopt JOE's authority when it is called.</list_item>
<list_item><loc_112><loc_194><loc_447><loc_214>GLYPH<SM590000> While the procedure is running, the special register USER still contains the value of ALICE because it excludes any adopted authority. The special register CURRENT USER contains the value of JOE because it includes any adopted authority.</list_item>
<list_item><loc_112><loc_219><loc_447><loc_233>GLYPH<SM590000> When proc1 ends, the session reverts to its original state with both USER and CURRENT USER having the value of ALICE.</list_item>
</unordered_list>
<picture><loc_111><loc_243><loc_246><loc_375><caption><loc_112><loc_377><loc_279><loc_382>Figure 3-5 Special registers and adopted authority</caption></picture>
<section_header_level_1><loc_53><loc_395><loc_202><loc_402>3.2.2 Built-in global variables</section_header_level_1>
<text><loc_112><loc_411><loc_423><loc_425>Built-in global variables are provided with the database manager and are used in SQL statements to retrieve scalar values that are associated with the variables.</text>
<text><loc_112><loc_433><loc_435><loc_454>IBM DB2 for i supports nine different built-in global variables that are read only and maintained by the system. These global variables can be used to identify attributes of the database connection and used as part of the RCAC logic.</text>
<page_footer><loc_282><loc_477><loc_428><loc_482>Chapter 3. Row and Column Access Control</page_footer>
<page_footer><loc_438><loc_477><loc_447><loc_482>19</page_footer>
<page_break>
<text><loc_112><loc_45><loc_280><loc_51>Table 3-2 lists the nine built-in global variables.</text>
<otsl><loc_52><loc_66><loc_448><loc_187><ched>Global variable<ched>Type<ched>Description<nl><fcel>CLIENT_HOST<fcel>VARCHAR(255)<fcel>Host name of the current client as returned by the system<nl><fcel>CLIENT_IPADDR<fcel>VARCHAR(128)<fcel>IP address of the current client as returned by the system<nl><fcel>CLIENT_PORT<fcel>INTEGER<fcel>Port used by the current client to communicate with the server<nl><fcel>PACKAGE_NAME<fcel>VARCHAR(128)<fcel>Name of the currently running package<nl><fcel>PACKAGE_SCHEMA<fcel>VARCHAR(128)<fcel>Schema name of the currently running package<nl><fcel>PACKAGE_VERSION<fcel>VARCHAR(64)<fcel>Version identifier of the currently running package<nl><fcel>ROUTINE_SCHEMA<fcel>VARCHAR(128)<fcel>Schema name of the currently running routine<nl><fcel>ROUTINE_SPECIFIC_NAME<fcel>VARCHAR(128)<fcel>Name of the currently running routine<nl><fcel>ROUTINE_TYPE<fcel>CHAR(1)<fcel>Type of the currently running routine<nl><caption><loc_53><loc_59><loc_164><loc_64>Table 3-2 Built-in global variables</caption></otsl>
<section_header_level_1><loc_53><loc_203><loc_314><loc_213>3.3 VERIFY_GROUP_FOR_USER function</section_header_level_1>
<text><loc_112><loc_224><loc_447><loc_275>The VERIFY_GROUP_FOR_USER function was added in IBM i 7.2. Although it is primarily intended for use with RCAC permissions and masks, it can be used in other SQL statements. The first parameter must be one of these three special registers: SESSION_USER, USER, or CURRENT_USER. The second and subsequent parameters are a list of user or group profiles. Each of these values must be 1 - 10 characters in length. These values are not validated for their existence, which means that you can specify the names of user profiles that do not exist without receiving any kind of error.</text>
<text><loc_112><loc_283><loc_447><loc_304>If a special register value is in the list of user profiles or it is a member of a group profile included in the list, the function returns a long integer value of 1. Otherwise, it returns a value of 0. It never returns the null value.</text>
<text><loc_112><loc_312><loc_375><loc_318>Here is an example of using the VERIFY_GROUP_FOR_USER function:</text>
<unordered_list><list_item><loc_112><loc_323><loc_332><loc_329>There are user profiles for MGR, JANE, JUDY, and TONY.</list_item>
<list_item><loc_112><loc_334><loc_324><loc_339>The user profile JANE specifies a group profile of MGR.</list_item>
<list_item><loc_112><loc_344><loc_438><loc_358>If a user is connected to the server using user profile JANE, all of the following function invocations return a value of 1:</list_item>
</unordered_list>
<code><loc_124><loc_363><loc_368><loc_405><_unknown_>VERIFY_GROUP_FOR_USER (CURRENT_USER, 'MGR') VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JANE', 'MGR') VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JANE', 'MGR', 'STEVE') The following function invocation returns a value of 0: VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JUDY', 'TONY')</code>
<page_footer><loc_53><loc_477><loc_64><loc_482>20</page_footer>
<page_footer><loc_76><loc_477><loc_273><loc_482>Row and Column Access Control Support in IBM DB2 for i</page_footer>
<page_break>
<text><loc_112><loc_45><loc_136><loc_51>RETURN</text>
<text><loc_112><loc_53><loc_128><loc_58>CASE</text>
<code><loc_112><loc_60><loc_426><loc_164><_unknown_>WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'HR', 'EMP' ) = 1 THEN EMPLOYEES . DATE_OF_BIRTH WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER = EMPLOYEES . USER_ID THEN EMPLOYEES . DATE_OF_BIRTH WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER <> EMPLOYEES . USER_ID THEN ( 9999 || '-' || MONTH ( EMPLOYEES . DATE_OF_BIRTH ) || '-' || DAY (EMPLOYEES.DATE_OF_BIRTH )) ELSE NULL END ENABLE ;</code>
<unordered_list><list_item><loc_112><loc_174><loc_447><loc_187>The other column to mask in this example is the TAX_ID information. In this example, the rules to enforce include the following ones:</list_item>
<list_item><loc_124><loc_192><loc_383><loc_198>-Human Resources can see the unmasked TAX_ID of the employees.</list_item>
<list_item><loc_124><loc_203><loc_330><loc_209>-Employees can see only their own unmasked TAX_ID.</list_item>
<list_item><loc_124><loc_214><loc_445><loc_227>-Managers see a masked version of TAX_ID with the first five characters replaced with the X character (for example, XXX-XX-1234).</list_item>
<list_item><loc_124><loc_232><loc_433><loc_238>-Any other person sees the entire TAX_ID as masked, for example, XXX-XX-XXXX.</list_item>
<list_item><loc_124><loc_243><loc_433><loc_249>To implement this column mask, run the SQL statement that is shown in Example 3-9.</list_item>
</unordered_list>
<code><loc_112><loc_267><loc_430><loc_432><_unknown_>CREATE MASK HR_SCHEMA.MASK_TAX_ID_ON_EMPLOYEES ON HR_SCHEMA.EMPLOYEES AS EMPLOYEES FOR COLUMN TAX_ID RETURN CASE WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'HR' ) = 1 THEN EMPLOYEES . TAX_ID WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER = EMPLOYEES . USER_ID THEN EMPLOYEES . TAX_ID WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER <> EMPLOYEES . USER_ID THEN ( 'XXX-XX-' CONCAT QSYS2 . SUBSTR ( EMPLOYEES . TAX_ID , 8 , 4 ) ) WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'EMP' ) = 1 THEN EMPLOYEES . TAX_ID ELSE 'XXX-XX-XXXX' END ENABLE ;<caption><loc_112><loc_257><loc_288><loc_262>Example 3-9 Creating a mask on the TAX_ID column</caption></code>
<page_footer><loc_282><loc_477><loc_428><loc_482>Chapter 3. Row and Column Access Control</page_footer>
<page_footer><loc_438><loc_477><loc_447><loc_482>27</page_footer>
<page_break>
<unordered_list><list_item><loc_112><loc_45><loc_368><loc_51>Figure 3-10 shows the masks that are created in the HR_SCHEMA.</list_item>
</unordered_list>
<picture><loc_52><loc_60><loc_447><loc_107><caption><loc_53><loc_110><loc_239><loc_115>Figure 3-10 Column masks shown in System i Navigator</caption></picture>
<section_header_level_1><loc_53><loc_128><loc_167><loc_135>3.6.6 Activating RCAC</section_header_level_1>
<text><loc_112><loc_144><loc_447><loc_165>Now that you have created the row permission and the two column masks, RCAC must be activated. The row permission and the two column masks are enabled (last clause in the scripts), but now you must activate RCAC on the table. To do so, complete the following steps:</text>
<unordered_list><list_item><loc_112><loc_170><loc_335><loc_176>Run the SQL statements that are shown in Example 3-10.</list_item>
</unordered_list>
<section_header_level_1><loc_112><loc_184><loc_307><loc_189>Example 3-10 Activating RCAC on the EMPLOYEES table</section_header_level_1>
<unordered_list><list_item><loc_112><loc_195><loc_308><loc_200>/* Active Row Access Control (permissions) */</list_item>
<list_item><loc_112><loc_202><loc_290><loc_208>/* Active Column Access Control (masks)</list_item>
</unordered_list>
<text><loc_299><loc_202><loc_308><loc_208>*/</text>
<text><loc_112><loc_210><loc_238><loc_216>ALTER TABLE HR_SCHEMA.EMPLOYEES</text>
<text><loc_112><loc_218><loc_222><loc_223>ACTIVATE ROW ACCESS CONTROL</text>
<text><loc_112><loc_225><loc_238><loc_231>ACTIVATE COLUMN ACCESS CONTROL;</text>
<unordered_list><list_item><loc_112><loc_240><loc_442><loc_261>Look at the definition of the EMPLOYEE table, as shown in Figure 3-11. To do this, from the main navigation pane of System i Navigator, click Schemas  HR_SCHEMA  Tables , right-click the EMPLOYEES table, and click Definition .</list_item>
</unordered_list>
<picture><loc_52><loc_270><loc_433><loc_408><caption><loc_53><loc_410><loc_284><loc_415>Figure 3-11 Selecting the EMPLOYEES table from System i Navigator</caption></picture>
<page_footer><loc_53><loc_477><loc_64><loc_482>28</page_footer>
<page_footer><loc_76><loc_477><loc_273><loc_482>Row and Column Access Control Support in IBM DB2 for i</page_footer>
<page_break>
<unordered_list><list_item><loc_112><loc_45><loc_420><loc_66>Figure 4-68 shows the Visual Explain of the same SQL statement, but with RCAC enabled. It is clear that the implementation of the SQL statement is more complex because the row permission rule becomes part of the WHERE clause.</list_item>
<list_item><loc_112><loc_320><loc_447><loc_341>Compare the advised indexes that are provided by the Optimizer without RCAC and with RCAC enabled. Figure 4-69 shows the index advice for the SQL statement without RCAC enabled. The index being advised is for the ORDER BY clause.</list_item>
</unordered_list>
<picture><loc_112><loc_75><loc_446><loc_301><caption><loc_112><loc_303><loc_267><loc_309>Figure 4-68 Visual Explain with RCAC enabled</caption></picture>
<picture><loc_53><loc_349><loc_414><loc_419><caption><loc_53><loc_421><loc_186><loc_427>Figure 4-69 Index advice with no RCAC</caption></picture>
<page_footer><loc_175><loc_477><loc_428><loc_482>Chapter 4. Implementing Row and Column Access Control: Banking example</page_footer>
<page_footer><loc_438><loc_477><loc_447><loc_482>77</page_footer>
<page_break>
<code><loc_53><loc_45><loc_409><loc_446><_unknown_>THEN C . CUSTOMER_TAX_ID WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'TELLER' ) = 1 THEN ( 'XXX-XX-' CONCAT QSYS2 . SUBSTR ( C . CUSTOMER_TAX_ID , 8 , 4 ) ) WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_TAX_ID ELSE 'XXX-XX-XXXX' END ENABLE ; CREATE MASK BANK_SCHEMA.MASK_DRIVERS_LICENSE_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_DRIVERS_LICENSE_NUMBER RETURN CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'TELLER' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER ELSE '*************' END ENABLE ; CREATE MASK BANK_SCHEMA.MASK_LOGIN_ID_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_LOGIN_ID RETURN CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_LOGIN_ID WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_LOGIN_ID ELSE '*****' END ENABLE ; CREATE MASK BANK_SCHEMA.MASK_SECURITY_QUESTION_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_SECURITY_QUESTION RETURN CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION ELSE '*****' END ENABLE ; CREATE MASK BANK_SCHEMA.MASK_SECURITY_QUESTION_ANSWER_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_SECURITY_QUESTION_ANSWER RETURN CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION_ANSWER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION_ANSWER ELSE '*****' END ENABLE ; ALTER TABLE BANK_SCHEMA.CUSTOMERS ACTIVATE ROW ACCESS CONTROL ACTIVATE COLUMN ACCESS CONTROL ;</code>
<page_footer><loc_53><loc_477><loc_69><loc_482>124</page_footer>
<page_footer><loc_81><loc_477><loc_278><loc_482>Row and Column Access Control Support in IBM DB2 for i</page_footer>
<page_break>
<text><loc_235><loc_18><loc_338><loc_32>Back cover</text>
<section_header_level_1><loc_22><loc_46><loc_365><loc_89>Row and Column Access Control Support in IBM DB2 for i</section_header_level_1>
<text><loc_22><loc_153><loc_104><loc_168>Implement roles and separation of duties</text>
<text><loc_22><loc_179><loc_98><loc_204>Leverage row permissions on the database</text>
<text><loc_125><loc_153><loc_338><loc_204>This IBM Redpaper publication provides information about the IBM i 7.2 feature of IBM DB2 for i Row and Column Access Control (RCAC). It offers a broad description of the function and advantages of controlling access to data in a comprehensive and transparent way. This publication helps you understand the capabilities of RCAC and provides examples of defining, creating, and implementing the row permissions and column masks in a relational database environment.</text>
<text><loc_22><loc_215><loc_99><loc_239>Protect columns by defining column masks</text>
<text><loc_125><loc_209><loc_338><loc_245>This paper is intended for database engineers, data-centric application developers, and security officers who want to design and implement RCAC as a part of their data control and governance policy. A solid background in IBM i object level security, DB2 for i relational database concepts, and SQL is assumed.</text>
<picture><loc_396><loc_16><loc_463><loc_34></picture>
<picture><loc_388><loc_51><loc_484><loc_120></picture>
<text><loc_382><loc_156><loc_457><loc_191>INTERNATIONAL TECHNICAL SUPPORT ORGANIZATION</text>
<text><loc_382><loc_222><loc_480><loc_244>BUILDING TECHNICAL INFORMATION BASED ON PRACTICAL EXPERIENCE</text>
<text><loc_382><loc_252><loc_480><loc_342>IBM Redbooks are developed by the IBM International Technical Support Organization. Experts from IBM, Customers and Partners from around the world create timely technical information based on realistic scenarios. Specific recommendations are provided to help you implement IT solutions more effectively in your environment.</text>
<text><loc_382><loc_365><loc_466><loc_380>For more information: ibm.com /redbooks</text>
<page_footer><loc_140><loc_399><loc_189><loc_404>REDP-5110-00</page_footer>
</doctag>