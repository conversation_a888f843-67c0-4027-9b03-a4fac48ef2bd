# Test Table Slide

With footnote

|    | Class1          | Class1          | Class1   | Class2   | Class2   | Class2   |
|----|-----------------|-----------------|----------|----------|----------|----------|
|    | A merged with B | A merged with B | C        | A        | B        | C        |
| R1 | True            | False           |          | False    | True     | True     |
| R2 |                 |                 | True     | False    |          |          |
| R3 | False           |                 |          |          | False    |          |
| R3 |                 | True            |          | True     |          |          |
| R4 |                 |                 | False    |          | False    |          |
| R4 |                 | True            |          | True     | False    | False    |
| R4 | True            | False           | True     | False    | True     | False    |

# Second slide title

Let’s introduce a list

With foo

Bar

And baz things

A rectangle shape with this text inside.

1. List item4
2. List item5
3. List item6

- I1
- I2
- I3
- I4

Some info:

- Item A
- Item B

Maybe a list?

1. List1
2. List2
3. List3

- l1 
- l2
- l3