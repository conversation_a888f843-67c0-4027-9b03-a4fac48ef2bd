{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "word_tables", "origin": {"mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "binary_hash": 8379738677198259833, "filename": "word_tables.docx"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/groups/0"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/0"}], "content_layer": "body", "name": "header-0", "label": "section"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/groups/0"}, "children": [{"$ref": "#/texts/1"}, {"$ref": "#/tables/0"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/tables/1"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}, {"$ref": "#/tables/2"}, {"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}, {"$ref": "#/tables/3"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/tables/4"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Test with tables", "text": "Test with tables", "level": 1}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "A uniform table", "text": "A uniform table", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "A non-uniform table with horizontal spans", "text": "A non-uniform table with horizontal spans", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "A non-uniform table with horizontal spans in inner columns", "text": "A non-uniform table with horizontal spans in inner columns", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "A non-uniform table with vertical spans", "text": "A non-uniform table with vertical spans", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "A non-uniform table with all kinds of spans and empty cells", "text": "A non-uniform table with all kinds of spans and empty cells", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}], "pictures": [], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "table", "prov": [], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 0.0", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Header 0.1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Header 0.2", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 1.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Cell 1.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 1.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 2.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Cell 2.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 2.2", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 3, "num_cols": 3, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 0.0", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Header 0.1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Header 0.2", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 1.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Cell 1.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 1.2", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 2.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Cell 2.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 2.2", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "table", "prov": [], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 0.0", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Header 0.1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Header 0.2", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 1.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 1.1 1.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 2.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 2.1 2.2", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 3, "num_cols": 3, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 0.0", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Header 0.1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Header 0.2", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 1.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 1.1 1.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 1.1 1.2", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 2.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 2.1 2.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 2.1 2.2", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}, {"self_ref": "#/tables/2", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "table", "prov": [], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 0.0", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Header 0.1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Header 0.2", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "Header 0.3", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 1.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 1.1 1.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "Cell 1.3", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 2.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 2.1 2.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "Cell 2.3", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 3, "num_cols": 4, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 0.0", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Header 0.1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Header 0.2", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "Header 0.3", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 1.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 1.1 1.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 1.1 1.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "Cell 1.3", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 2.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 2.1 2.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "Merged Cell 2.1 2.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "Cell 2.3", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}, {"self_ref": "#/tables/3", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "table", "prov": [], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 0.0", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Header 0.1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Header 0.2", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 1.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 1.1 2.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 1.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 2.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 2.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 3.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 3.1 4.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 3.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 4.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 4.2", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 5, "num_cols": 3, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 0.0", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Header 0.1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Header 0.2", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 1.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 1.1 2.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 1.2", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 2.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 1.1 2.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 2.2", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 3.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 3.1 4.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 3.2", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 4.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 3.1 4.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 4.2", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}, {"self_ref": "#/tables/4", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "table", "prov": [], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 0.0", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Header 0.1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Header 0.2", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 1.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 1.1 2.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 1.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 2.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 2.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 3.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 3.1 4.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 3.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 3, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 4.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 4.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "Merged Cell 4.4 5.4", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 5, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "Cell 8.4", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 9, "num_cols": 5, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Header 0.0", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Header 0.1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Header 0.2", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 1.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 1.1 2.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 1.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 2.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 1.1 2.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 2.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 3.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 3.1 4.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 3.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 3, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cell 4.0", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Merged Cell 3.1 4.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Cell 4.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 3, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "Merged Cell 4.4 5.4", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 3, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "Merged Cell 4.4 5.4", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 5, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 5, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 5, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 5, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 5, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "Cell 8.4", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {}}