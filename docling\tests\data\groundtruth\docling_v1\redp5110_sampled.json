{"_name": "", "type": "pdf-document", "description": {"title": null, "abstract": null, "authors": null, "affiliations": null, "subjects": null, "keywords": null, "publication_date": null, "languages": null, "license": null, "publishers": null, "url_refs": null, "references": null, "publication": null, "reference_count": null, "citation_count": null, "citation_date": null, "advanced": null, "analytics": null, "logs": [], "collection": null, "acquisition": null}, "file-info": {"filename": "redp5110_sampled.pdf", "filename-prov": null, "document-hash": "bbf706f95c6042a4bcfa73a17f1472d798886d79065340ed2772992ea399a12f", "#-pages": 18, "collection-name": null, "description": null, "page-hashes": [{"hash": "8633d627a4ae407aaaede920b471d3058de656dab15d04e7d469632352890d4f", "model": "default", "page": 1}, {"hash": "ffca227099d3b581b935322e37806bb2115d104b59824ae47123f96c3717d8d4", "model": "default", "page": 2}, {"hash": "8450a101294966dc3666779e939d5d1c42dc98d2def2ac182e6a4742db908373", "model": "default", "page": 3}, {"hash": "6adc167183cbcf48541b4c076619508c0bb4a29d700308bc2a6e25a03ee35187", "model": "default", "page": 4}, {"hash": "3432d7c0892def70d3f5c2f4370fdcd728318810b6dc4a6c518f67ae1b2447cc", "model": "default", "page": 5}, {"hash": "cbddc773b2827bccbef5ffbd40190cb8eae1ee4956e57d02e0c98aff49cb7649", "model": "default", "page": 6}, {"hash": "02469ba69dff12aa1f322353cceb8b6a77a4bd92f457db01588f2bdc5f4290fe", "model": "default", "page": 7}, {"hash": "07c07b53d33e143a39b310225d156917a90398dcdf1d703f1556f952202d1474", "model": "default", "page": 8}, {"hash": "79fd01636330ea2c4b059ef3be69cfe67456924c319b3843c2cf01610f191354", "model": "default", "page": 9}, {"hash": "beac1aa99a1b5263bd18cab36cda3b4bb32308c70f8a80f5e4d088fb4feb5d41", "model": "default", "page": 10}, {"hash": "cebcf723129cb1260b0aaf0de2c9441d5b733bf9a5b494f0121b46d9e99cf6e9", "model": "default", "page": 11}, {"hash": "9d262e1cb504e092ecd989740dff6d2483a202fc36c4707230e3cdca094d2b38", "model": "default", "page": 12}, {"hash": "19a4949dea604878e01bb7fd5cc3fc0d719735fabdef3b0d43928af5c8b1730c", "model": "default", "page": 13}, {"hash": "b557acf2a8fd0918b3dc7f8c220b64f841e799325fd85796b2557c969fc1e1d0", "model": "default", "page": 14}, {"hash": "79b6d9cc327fde220894a8e04ffd5787dd8d862377e3deea082166e7aaa55a2f", "model": "default", "page": 15}, {"hash": "568a57bc2161bbb06ed17d48180e72826cdf7c8281e4914b9079c63c6373ad73", "model": "default", "page": 16}, {"hash": "3a1997b2253e42313f5b6c9eecf2f2f09f36a2b92da062d3972d13d06a6b0c8c", "model": "default", "page": 17}, {"hash": "8f11363a05bd1a5ba2ad45f12697bd8113576602c302f34ba6b50e8fc6f43047", "model": "default", "page": 18}]}, "main-text": [{"prov": [{"bbox": [287.82001, 741.25195, 418.83356, 763.45197], "page": 1, "span": [0, 11], "__ref_s3_data": null}], "text": "Front cover", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/0"}, {"prov": [{"bbox": [35.700001, 626.15887, 584.64288, 707.4134500000001], "page": 1, "span": [0, 54], "__ref_s3_data": null}], "text": "Row and Column Access Control Support in IBM DB2 for i", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/1"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/2"}, {"prov": [{"bbox": [64.800003, 695.95197, 168.73441, 718.15198], "page": 2, "span": [0, 8], "__ref_s3_data": null}], "text": "Contents", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"name": "Document Index", "type": "table-of-contents", "$ref": "#/tables/0"}, {"prov": [{"bbox": [64.800003, 706.41602, 235.8624, 717.51605], "page": 3, "span": [0, 30], "__ref_s3_data": null}], "text": "DB2 for i Center of Excellence", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [94.132698, 636.66357, 233.99973, 653.54987], "page": 3, "span": [0, 52], "__ref_s3_data": null}], "text": "Solution Brief IBM Systems Lab Services and Training", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/3"}, {"prov": [{"bbox": [144.88921, 455.18594, 188.74681, 464.53836000000007], "page": 3, "span": [0, 10], "__ref_s3_data": null}], "text": "Highlights", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [144.88921, 433.31058, 242.87389000000002, 446.78293], "page": 3, "span": [0, 532], "__ref_s3_data": null}], "text": "GLYPH<g115>GLYPH<g3> GLYPH<g40>GLYPH<g81>GLYPH<g75>GLYPH<g68>GLYPH<g81>GLYPH<g70>GLYPH<g72>GLYPH<g3> GLYPH<g87>GLYPH<g75>GLYPH<g72>GLYPH<g3> GLYPH<g83>GLYPH<g72>GLYPH<g85>GLYPH<g73>GLYPH<g82>GLYPH<g85>GLYPH<g80>GLYPH<g68>GLYPH<g81>GLYPH<g70>GLYPH<g72>GLYPH<g3> GLYPH<g82>GLYPH<g73>GLYPH<g3> GLYPH<g92>GLYPH<g82>GLYPH<g88>GLYPH<g85> GLYPH<g3> GLYPH<g71>GLYPH<g68>GLYPH<g87>GLYPH<g68>GLYPH<g69>GLYPH<g68>GLYPH<g86>GLYPH<g72>GLYPH<g3> GLYPH<g82>GLYPH<g83>GLYPH<g72>GLYPH<g85>GLYPH<g68>GLYPH<g87>GLYPH<g76>GLYPH<g82>GLYPH<g81>GLYPH<g86>", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [144.88921, 402.7627, 259.2287, 424.06781], "page": 3, "span": [0, 876], "__ref_s3_data": null}], "text": "GLYPH<g115>GLYPH<g3> GLYPH<g40>GLYPH<g68>GLYPH<g85> GLYPH<g81>GLYPH<g3> GLYPH<g74>GLYPH<g85>GLYPH<g72>GLYPH<g68>GLYPH<g87>GLYPH<g72>GLYPH<g85>GLYPH<g3> GLYPH<g85>GLYPH<g72>GLYPH<g87>GLYPH<g88>GLYPH<g85> GLYPH<g81>GLYPH<g3> GLYPH<g82>GLYPH<g81>GLYPH<g3> GLYPH<g44>GLYPH<g55>GLYPH<g3> GLYPH<g83>GLYPH<g85>GLYPH<g82>GLYPH<g77>GLYPH<g72>GLYPH<g70>GLYPH<g87>GLYPH<g86> GLYPH<g3> GLYPH<g87>GLYPH<g75>GLYPH<g85>GLYPH<g82>GLYPH<g88>GLYPH<g74>GLYPH<g75>GLYPH<g3> GLYPH<g80>GLYPH<g82>GLYPH<g71>GLYPH<g72>GLYPH<g85> GLYPH<g81>GLYPH<g76>GLYPH<g93>GLYPH<g68>GLYPH<g87>GLYPH<g76>GLYPH<g82>GLYPH<g81>GLYPH<g3> GLYPH<g82>GLYPH<g73>GLYPH<g3> GLYPH<g71>GLYPH<g68>GLYPH<g87>GLYPH<g68>GLYPH<g69>GLYPH<g68>GLYPH<g86>GLYPH<g72>GLYPH<g3> GLYPH<g68>GLYPH<g81>GLYPH<g71> GLYPH<g3> GLYPH<g68>GLYPH<g83>GLYPH<g83>GLYPH<g79>GLYPH<g76>GLYPH<g70>GLYPH<g68>GLYPH<g87>GLYPH<g76>GLYPH<g82>GLYPH<g81>GLYPH<g86>", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [144.88921, 380.04745, 249.83562, 393.51981], "page": 3, "span": [0, 672], "__ref_s3_data": null}], "text": "GLYPH<g115>GLYPH<g3> GLYPH<g53>GLYPH<g72>GLYPH<g79>GLYPH<g92>GLYPH<g3> GLYPH<g82>GLYPH<g81>GLYPH<g3> GLYPH<g44>GLYPH<g37>GLYPH<g48>GLYPH<g3> GLYPH<g72>GLYPH<g91>GLYPH<g83>GLYPH<g72>GLYPH<g85>GLYPH<g87>GLYPH<g3> GLYPH<g70>GLYPH<g82>GLYPH<g81>GLYPH<g86>GLYPH<g88>GLYPH<g79>GLYPH<g87>GLYPH<g76>GLYPH<g81>GLYPH<g74>GLYPH<g15>GLYPH<g3> GLYPH<g86>GLYPH<g78>GLYPH<g76>GLYPH<g79>GLYPH<g79>GLYPH<g86> GLYPH<g3> GLYPH<g86>GLYPH<g75>GLYPH<g68>GLYPH<g85>GLYPH<g76>GLYPH<g81>GLYPH<g74>GLYPH<g3> GLYPH<g68>GLYPH<g81>GLYPH<g71>GLYPH<g3> GLYPH<g85>GLYPH<g72>GLYPH<g81>GLYPH<g82>GLYPH<g90>GLYPH<g81>GLYPH<g3> GLYPH<g86>GLYPH<g72>GLYPH<g85>GLYPH<g89>GLYPH<g76>GLYPH<g70>GLYPH<g72>GLYPH<g86>", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [144.88921, 357.33237, 234.25163000000003, 370.8047199999999], "page": 3, "span": [0, 613], "__ref_s3_data": null}], "text": "GLYPH<g115>GLYPH<g3> GLYPH<g55> GLYPH<g68>GLYPH<g78>GLYPH<g72>GLYPH<g3> GLYPH<g68>GLYPH<g71>GLYPH<g89>GLYPH<g68>GLYPH<g81>GLYPH<g87>GLYPH<g68>GLYPH<g74>GLYPH<g72>GLYPH<g3> GLYPH<g82>GLYPH<g73>GLYPH<g3> GLYPH<g68>GLYPH<g70>GLYPH<g70>GLYPH<g72>GLYPH<g86>GLYPH<g86>GLYPH<g3> GLYPH<g87>GLYPH<g82>GLYPH<g3> GLYPH<g68> GLYPH<g3> GLYPH<g90>GLYPH<g82>GLYPH<g85>GLYPH<g79>GLYPH<g71>GLYPH<g90>GLYPH<g76>GLYPH<g71>GLYPH<g72>GLYPH<g3> GLYPH<g86>GLYPH<g82>GLYPH<g88>GLYPH<g85>GLYPH<g70>GLYPH<g72>GLYPH<g3> GLYPH<g82>GLYPH<g73>GLYPH<g3> GLYPH<g72>GLYPH<g91>GLYPH<g83>GLYPH<g72>GLYPH<g85>GLYPH<g87>GLYPH<g76>GLYPH<g86>GLYPH<g72>", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/4"}, {"prov": [{"bbox": [461.08859000000007, 646.57819, 506.26178, 653.59247], "page": 3, "span": [0, 14], "__ref_s3_data": null}], "text": "Power Services", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [280.24011, 515.37946, 463.80942, 552.65735], "page": 3, "span": [0, 30], "__ref_s3_data": null}], "text": "DB2 for i Center of Excellence", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [280.24011, 504.54041, 483.29572, 514.40973], "page": 3, "span": [0, 49], "__ref_s3_data": null}], "text": "Expert help to achieve your business requirements", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [280.24011, 467.10434, 443.28210000000007, 476.11838000000006], "page": 3, "span": [0, 37], "__ref_s3_data": null}], "text": "We build confident, satisfied clients", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [280.24011, 447.0405, 488.15466, 464.62405], "page": 3, "span": [0, 122], "__ref_s3_data": null}], "text": "No one else has the vast consulting experiences, skills sharing and renown service offerings to do what we can do for you.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [280.24011, 427.26999, 367.86023, 434.6739799999999], "page": 3, "span": [0, 27], "__ref_s3_data": null}], "text": "Because no one else is IBM.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [280.24011, 366.77972000000005, 500.32104000000004, 414.90198000000004], "page": 3, "span": [0, 318], "__ref_s3_data": null}], "text": "With combined experiences and direct access to development groups, we're the experts in IBM DB2® for i. The DB2 for i Center of Excellence (CoE) can help you achieve-perhaps reexamine and exceed-your business requirements and gain more confidence and satisfaction in IBM product data management products and solutions.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [280.24011, 345.13193, 434.83205999999996, 354.14597], "page": 3, "span": [0, 30], "__ref_s3_data": null}], "text": "Who we are, some of what we do", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [280.24011, 335.24777, 434.56316999999996, 342.65175999999997], "page": 3, "span": [0, 46], "__ref_s3_data": null}], "text": "Global CoE engagements cover topics including:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [280.24011, 315.47775, 401.56412, 322.88174], "page": 3, "span": [0, 38], "__ref_s3_data": null}], "text": "r Database performance and scalability", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [280.24011, 305.29504, 424.99646, 312.69904], "page": 3, "span": [0, 44], "__ref_s3_data": null}], "text": "r Advanced SQL knowledge and skills transfer", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [280.24011, 295.11246, 392.15845, 302.51645], "page": 3, "span": [0, 37], "__ref_s3_data": null}], "text": "r Business intelligence and analytics", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [280.24011, 284.92975, 339.94354, 292.33374], "page": 3, "span": [0, 15], "__ref_s3_data": null}], "text": "r DB2 Web Query", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [280.24011, 274.74716, 504.19314999999995, 282.15115], "page": 3, "span": [0, 72], "__ref_s3_data": null}], "text": "r Query/400 modernization for better reporting and analysis capabilities", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [280.24011, 264.5644500000001, 423.0022, 271.96844], "page": 3, "span": [0, 43], "__ref_s3_data": null}], "text": "r Database modernization and re-engineering", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [280.24011, 254.38187000000005, 399.65173, 261.78585999999996], "page": 3, "span": [0, 38], "__ref_s3_data": null}], "text": "r Data-centric architecture and design", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [280.24011, 244.19925999999998, 466.77881, 251.60325999999998], "page": 3, "span": [0, 58], "__ref_s3_data": null}], "text": "r Extremely large database and overcoming limits to growth", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [280.24011, 234.01656000000003, 382.20956, 241.42054999999993], "page": 3, "span": [0, 30], "__ref_s3_data": null}], "text": "r ISV education and enablement", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [64.800003, 695.95197, 151.46161, 718.15198], "page": 4, "span": [0, 7], "__ref_s3_data": null}], "text": "Preface", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.79984, 590.13928, 547.30823, 659.35138], "page": 4, "span": [0, 469], "__ref_s3_data": null}], "text": "This IBMfi Redpaper™ publication provides information about the IBM i 7.2 feature of IBM DB2fi for i Row and Column Access Control (RCAC). It offers a broad description of the function and advantages of controlling access to data in a comprehensive and transparent way. This publication helps you understand the capabilities of RCAC and provides examples of defining, creating, and implementing the row permissions and column masks in a relational database environment.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79987, 532.18005, 546.4657, 577.39252], "page": 4, "span": [0, 309], "__ref_s3_data": null}], "text": "This paper is intended for database engineers, data-centric application developers, and security officers who want to design and implement RCAC as a part of their data control and governance policy. A solid background in IBM i object level security, DB2 for i relational database concepts, and SQL is assumed.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 450.15848, 547.23669, 471.37128000000007], "page": 4, "span": [0, 172], "__ref_s3_data": null}], "text": "This paper was produced by the IBM DB2 for i Center of Excellence team in partnership with the International Technical Support Organization (ITSO), Rochester, Minnesota US.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/5"}, {"prov": [{"bbox": [263.39957, 275.14026, 541.25079, 416.35123], "page": 4, "span": [0, 684], "__ref_s3_data": null}], "text": "<PERSON> is a senior DB2 consultant on the DB2 for i Center of Excellence team in the IBM Lab Services and Training organization. His primary role is training and implementation services for IBM DB2 Web Query for i and business analytics. <PERSON> began his career with IBM 30 years ago in the IBM Rochester Development Lab, where he developed cooperative processing products that paired IBM PCs with IBM S/36 and AS/.400 systems. In the years since, <PERSON> has held numerous technical roles, including independent software vendors technical support on a broad range of IBM technologies and products, and supporting customers in the IBM Executive Briefing Center and IBM Project Office.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/6"}, {"prov": [{"bbox": [263.3996, 111.16283999999996, 541.27374, 264.37347], "page": 4, "span": [0, 726], "__ref_s3_data": null}], "text": "<PERSON><PERSON><PERSON> is a Senior IT Specialist at STG Lab Services and Training in Rochester, Minnesota. He writes extensively and teaches IBM classes worldwide in all areas of DB2 for i. Before joining STG Lab Services, he worked in the ITSO for nine years writing multiple IBM Redbooksfi publications. He also worked for IBM Colombia as an IBM AS/400fi IT Specialist doing presales support for the Andean countries. He has 28 years of experience in the computing field and has taught database classes in Colombian universities. He holds a Master's degree in Computer Science from EAFIT, Colombia. His areas of expertise are database technology, performance, and data warehousing. Hernan<PERSON> can be <NAME_EMAIL> .", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [64.800003, 488.9364, 125.36661, 503.6994], "page": 4, "span": [0, 7], "__ref_s3_data": null}], "text": "Authors", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/7"}, {"prov": [{"bbox": [500.39999, 661.86829, 522.61774, 698.8312999999999], "page": 5, "span": [0, 1], "__ref_s3_data": null}], "text": "1", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [81.0, 517.01929, 115.13253, 523.45728], "page": 5, "span": [0, 10], "__ref_s3_data": null}], "text": "Chapter 1.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 482.1218, 547.30475, 537.11365], "page": 5, "span": [0, 36], "__ref_s3_data": null}], "text": "Securing and protecting IBM DB2 data", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.79965, 362.07886, 547.25403, 443.29129], "page": 5, "span": [0, 648], "__ref_s3_data": null}], "text": "Recent news headlines are filled with reports of data breaches and cyber-attacks impacting global businesses of all sizes. The Identity Theft Resource Center$^{1}$ reports that almost 5000 data breaches have occurred since 2005, exposing over 600 million records of data. The financial cost of these data breaches is skyrocketing. Studies from the Ponemon Institute$^{2}$ revealed that the average cost of a data breach increased in 2013 by 15% globally and resulted in a brand equity loss of $9.4 million per attack. The average cost that is incurred for each lost record containing sensitive information increased more than 9% to $145 per record.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.80023, 304.05984, 527.2063, 349.2722800000001], "page": 5, "span": [0, 304], "__ref_s3_data": null}], "text": "Businesses must make a serious effort to secure their data and recognize that securing information assets is a cost of doing business. In many parts of the world and in many industries, securing the data is required by law and subject to audits. Data security is no longer an option; it is a requirement.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.80025, 270.10022000000004, 547.15515, 291.31302], "page": 5, "span": [0, 122], "__ref_s3_data": null}], "text": "This chapter describes how you can secure and protect data in DB2 for i. The following topics are covered in this chapter:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.80025, 253.06064000000003, 250.23166999999998, 262.27365], "page": 5, "span": [0, 37], "__ref_s3_data": null}], "text": "GLYPH<SM590000> Security fundamentals", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.80025, 241.06083999999998, 282.98114, 250.27382999999998], "page": 5, "span": [0, 47], "__ref_s3_data": null}], "text": "GLYPH<SM590000> Current state of IBM i security", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.80025, 229.06104000000005, 264.88187, 238.27403000000004], "page": 5, "span": [0, 43], "__ref_s3_data": null}], "text": "GLYPH<SM590000> DB2 for i security controls", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.8, 67.219559, 258.36255, 74.24993900000004], "page": 5, "span": [0, 35], "__ref_s3_data": null}], "text": "$^{1 }$http://www.idtheftcenter.org", "type": "footnote", "payload": null, "name": "Footnote", "font": null}, {"prov": [{"bbox": [136.8, 57.02823999999998, 234.05881, 64.40973699999995], "page": 5, "span": [0, 31], "__ref_s3_data": null}], "text": "$^{2 }$http://www.ponemon.org /", "type": "footnote", "payload": null, "name": "Footnote", "font": null}, {"prov": [{"bbox": [64.800003, 702.8963, 267.40582, 717.6593], "page": 6, "span": [0, 25], "__ref_s3_data": null}], "text": "1.1 Security fundamentals", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.8, 664.17847, 545.00482, 685.3913], "page": 6, "span": [0, 133], "__ref_s3_data": null}], "text": "Before reviewing database security techniques, there are two fundamental steps in securing information assets that must be described:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 611.13892, 547.16425, 656.87512], "page": 6, "span": [0, 361], "__ref_s3_data": null}], "text": "GLYPH<SM590000> First, and most important, is the definition of a company's security policy . Without a security policy, there is no definition of what are acceptable practices for using, accessing, and storing information by who, what, when, where, and how. A security policy should minimally address three things: confidentiality, integrity, and availability.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [151.19946, 522.16022, 547.26086, 603.37213], "page": 6, "span": [0, 587], "__ref_s3_data": null}], "text": "The monitoring and assessment of adherence to the security policy determines whether your security strategy is working. Often, IBM security consultants are asked to perform security assessments for companies without regard to the security policy. Although these assessments can be useful for observing how the system is defined and how data is being accessed, they cannot determine the level of security without a security policy. Without a security policy, it really is not an assessment as much as it is a baseline for monitoring the changes in the security settings that are captured.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [151.19946, 505.18042, 541.992, 514.39343], "page": 6, "span": [0, 90], "__ref_s3_data": null}], "text": "A security policy is what defines whether the system and its settings are secure (or not).", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.7993, 416.1394, 547.15826, 497.87503000000004], "page": 6, "span": [0, 573], "__ref_s3_data": null}], "text": "GLYPH<SM590000> The second fundamental in securing data assets is the use of resource security . If implemented properly, resource security prevents data breaches from both internal and external intrusions. Resource security controls are closely tied to the part of the security policy that defines who should have access to what information resources. A hacker might be good enough to get through your company firewalls and sift his way through to your system, but if they do not have explicit access to your database, the hacker cannot compromise your information assets.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.80022, 382.17978, 535.36169, 403.39258], "page": 6, "span": [0, 179], "__ref_s3_data": null}], "text": "With your eyes now open to the importance of securing information assets, the rest of this chapter reviews the methods that are available for securing database resources on IBM i.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [64.800003, 338.93628, 323.38391, 353.69928], "page": 6, "span": [0, 35], "__ref_s3_data": null}], "text": "1.2 Current state of IBM i security", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.8, 276.15884000000005, 547.31824, 321.37128], "page": 6, "span": [0, 306], "__ref_s3_data": null}], "text": "Because of the inherently secure nature of IBM i, many clients rely on the default system settings to protect their business data that is stored in DB2 for i. In most cases, this means no data protection because the default setting for the Create default public authority (QCRTAUT) system value is *CHANGE.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 206.14005999999995, 547.28442, 263.35226], "page": 6, "span": [0, 405], "__ref_s3_data": null}], "text": "Even more disturbing is that many IBM i clients remain in this state, despite the news headlines and the significant costs that are involved with databases being compromised. This default security configuration makes it quite challenging to implement basic security policies. A tighter implementation is required if you really want to protect one of your company's most valuable assets, which is the data.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 112.12167, 547.28326, 193.33349999999996], "page": 6, "span": [0, 640], "__ref_s3_data": null}], "text": "Traditionally, IBM i applications have employed menu-based security to counteract this default configuration that gives all users access to the data. The theory is that data is protected by the menu options controlling what database operations that the user can perform. This approach is ineffective, even if the user profile is restricted from running interactive commands. The reason is that in today's connected world there are a multitude of interfaces into the system, from web browsers to PC clients, that bypass application menus. If there are no object-level controls, users of these newer interfaces have an open door to your data.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 639.27942, 544.30334, 720.49133], "page": 7, "span": [0, 589], "__ref_s3_data": null}], "text": "Many businesses are trying to limit data access to a need-to-know basis. This security goal means that users should be given access only to the minimum set of data that is required to perform their job. Often, users with object-level access are given access to row and column values that are beyond what their business task requires because that object-level security provides an all-or-nothing solution. For example, object-level controls allow a manager to access data about all employees. Most security policies limit a manager to accessing data only for the employees that they manage.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [64.800003, 606.67725, 301.46902, 618.66528], "page": 7, "span": [0, 37], "__ref_s3_data": null}], "text": "1.3.1 Existing row and column control", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.79999, 535.29901, 541.56738, 592.51129], "page": 7, "span": [0, 377], "__ref_s3_data": null}], "text": "Some IBM i clients have tried augmenting the all-or-nothing object-level security with SQL views (or logical files) and application logic, as shown in Figure 1-2. However, application-based logic is easy to bypass with all of the different data access interfaces that are provided by the IBM i operating system, such as Open Database Connectivity (ODBC) and System i Navigator.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79999, 477.27997, 547.4408, 522.49243], "page": 7, "span": [0, 340], "__ref_s3_data": null}], "text": "Using SQL views to limit access to a subset of the data in a table also has its own set of challenges. First, there is the complexity of managing all of the SQL view objects that are used for securing data access. Second, scaling a view-based security solution can be difficult as the amount of data grows and the number of users increases.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79999, 431.26077, 547.23267, 464.47339], "page": 7, "span": [0, 247], "__ref_s3_data": null}], "text": "Even if you are willing to live with these performance and management issues, a user with *ALLOBJ access still can directly access all of the data in the underlying DB2 table and easily bypass the security controls that are built into an SQL view.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/8"}, {"prov": [{"bbox": [136.8, 91.85700199999997, 316.44727, 100.18200000000002], "page": 7, "span": [0, 43], "__ref_s3_data": null}], "text": "Figure 1-2 Existing row and column controls", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [64.800003, 708.67725, 335.49551, 720.66528], "page": 8, "span": [0, 38], "__ref_s3_data": null}], "text": "2.1.6 Change Function Usage CL command", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.8, 685.2982799999999, 547.28442, 694.51129], "page": 8, "span": [0, 90], "__ref_s3_data": null}], "text": "The following CL commands can be used to work with, display, or change function usage IDs:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 668.25873, 301.51749, 677.47174], "page": 8, "span": [0, 49], "__ref_s3_data": null}], "text": "GLYPH<SM590000> Work Function Usage ( WRKFCNUSG )", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.80099, 656.25891, 313.39777, 665.47192], "page": 8, "span": [0, 51], "__ref_s3_data": null}], "text": "GLYPH<SM590000> Change Function Usage ( CHGFCNUSG )", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.80098, 644.25909, 310.81711, 653.47211], "page": 8, "span": [0, 52], "__ref_s3_data": null}], "text": "GLYPH<SM590000> Display Function Usage ( DSPFCNUSG )", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.79997, 610.2995, 512.53802, 631.51233], "page": 8, "span": [0, 126], "__ref_s3_data": null}], "text": "For example, the following CHGFCNUSG command shows granting authorization to user HBEDOYA to administer and manage RCAC rules:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.80096, 593.54877, 441.59686, 602.32355], "page": 8, "span": [0, 61], "__ref_s3_data": null}], "text": "CHGFCNUSG FCNID(QIBM_DB_SECADM) USER(HBEDOYA) USAGE(*ALLOWED)", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [64.800003, 560.65729, 544.47546, 572.64532], "page": 8, "span": [0, 72], "__ref_s3_data": null}], "text": "2.1.7 Verifying function usage IDs for RCAC with the FUNCTION_USAGE view", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.8, 525.2785, 519.51794, 546.49133], "page": 8, "span": [0, 130], "__ref_s3_data": null}], "text": "The FUNCTION_USAGE view contains function usage configuration details. Table 2-1 describes the columns in the FUNCTION_USAGE view.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/1"}, {"prov": [{"bbox": [136.8, 504.11699999999996, 283.96805, 512.44202], "page": 8, "span": [0, 29], "__ref_s3_data": null}], "text": "Table 2-1 FUNCTION_USAGE view", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [136.8, 318.27847, 547.2804, 339.49127000000004], "page": 8, "span": [0, 112], "__ref_s3_data": null}], "text": "To discover who has authorization to define and manage RCAC, you can use the query that is shown in Example 2-1.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 297.117, 462.35419, 305.44202], "page": 8, "span": [0, 74], "__ref_s3_data": null}], "text": "Example 2-1 Query to determine who has authority to define and manage RCAC", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [136.8, 279.56719999999996, 171.26956, 288.34198], "page": 8, "span": [0, 6], "__ref_s3_data": null}], "text": "SELECT", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [182.75941, 279.56719999999996, 251.69853, 288.34198], "page": 8, "span": [0, 12], "__ref_s3_data": null}], "text": "function_id,", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [166.78244, 267.56737999999996, 241.73852999999997, 276.3421599999999], "page": 8, "span": [0, 10], "__ref_s3_data": null}], "text": "user_name,", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [170.75961, 255.56758000000002, 221.69901999999996, 264.34235], "page": 8, "span": [0, 6], "__ref_s3_data": null}], "text": "usage,", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [167.53809, 243.56777999999997, 236.69878, 252.34253], "page": 8, "span": [0, 9], "__ref_s3_data": null}], "text": "user_type", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 231.56798000000003, 160.59396, 240.34272999999996], "page": 8, "span": [0, 4], "__ref_s3_data": null}], "text": "FROM", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [178.43944, 231.56798000000003, 261.71829, 240.34272999999996], "page": 8, "span": [0, 14], "__ref_s3_data": null}], "text": "function_usage", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 219.56817999999998, 162.44176, 228.34293000000002], "page": 8, "span": [0, 5], "__ref_s3_data": null}], "text": "WHERE", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [177.8268, 219.56817999999998, 331.67731, 228.34293000000002], "page": 8, "span": [0, 28], "__ref_s3_data": null}], "text": "function_id=’QIBM_DB_SECADM’", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 207.56836999999996, 178.77542, 216.34312], "page": 8, "span": [0, 8], "__ref_s3_data": null}], "text": "ORDER BY", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [189.26929, 207.56836999999996, 241.73856, 216.34312], "page": 8, "span": [0, 10], "__ref_s3_data": null}], "text": "user_name;", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [64.800003, 157.01637000000005, 249.59605000000002, 171.77936999999997], "page": 8, "span": [0, 24], "__ref_s3_data": null}], "text": "2.2 Separation of duties", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.8, 82.23904400000004, 547.22345, 139.45128], "page": 8, "span": [0, 463], "__ref_s3_data": null}], "text": "Separation of duties helps businesses comply with industry regulations or organizational requirements and simplifies the management of authorities. Separation of duties is commonly used to prevent fraudulent activities or errors by a single person. It provides the ability for administrative functions to be divided across individuals without overlapping responsibilities, so that one user does not possess unlimited authority, such as with the *ALLOBJ authority.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79959, 651.27887, 542.69434, 720.49097], "page": 9, "span": [0, 516], "__ref_s3_data": null}], "text": "For example, assume that a business has assigned the duty to manage security on IBM i to <PERSON>. Before release IBM i 7.2, to grant privileges, <PERSON> had to have the same privileges <PERSON> was granting to others. Therefore, to grant *USE privileges to the PAYROLL table, <PERSON> had to have *OBJMGT and *USE authority (or a higher level of authority, such as *ALLOBJ). This requirement allowed <PERSON> to access the data in the PAYROLL table even though <PERSON>'s job description was only to manage its security.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79959, 593.25983, 547.30396, 638.47229], "page": 9, "span": [0, 285], "__ref_s3_data": null}], "text": "In IBM i 7.2, the QIBM_DB_SECADM function usage grants authorities, revokes authorities, changes ownership, or changes the primary group without giving access to the object or, in the case of a database table, to the data that is in the table or allowing other operations on the table.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79959, 559.30023, 538.65076, 580.51306], "page": 9, "span": [0, 129], "__ref_s3_data": null}], "text": "QIBM_DB_SECADM function usage can be granted only by a user with *SECADM special authority and can be given to a user or a group.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79959, 513.28101, 545.79602, 546.49365], "page": 9, "span": [0, 204], "__ref_s3_data": null}], "text": "QIBM_DB_SECADM also is responsible for administering RCAC, which restricts which rows a user is allowed to access in a table and whether a user is allowed to see information in certain columns of a table.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79959, 455.26199, 539.80713, 500.47443], "page": 9, "span": [0, 285], "__ref_s3_data": null}], "text": "A preferred practice is that the RCAC administrator has the QIBM_DB_SECADM function usage ID, but absolutely no other data privileges. The result is that the RCAC administrator can deploy and maintain the RCAC constructs, but cannot grant themselves unauthorized access to data itself.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79959, 421.30236999999994, 543.06714, 442.51517], "page": 9, "span": [0, 136], "__ref_s3_data": null}], "text": "Table 2-2 shows a comparison of the different function usage IDs and *JOBCTL authority to the different CL commands and DB2 for i tools.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/2"}, {"prov": [{"bbox": [64.800003, 400.13699, 391.75464, 408.4620100000001], "page": 9, "span": [0, 78], "__ref_s3_data": null}], "text": "Table 2-2 Comparison of the different function usage IDs and *JOBCTL authority", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [136.79956, 699.27814, 528.73059, 720.49097], "page": 10, "span": [0, 135], "__ref_s3_data": null}], "text": "The SQL CREATE PERMISSION statement that is shown in Figure 3-1 is used to define and initially enable or disable the row access rules.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/9"}, {"prov": [{"bbox": [136.8, 369.53699, 341.97659, 377.862], "page": 10, "span": [0, 42], "__ref_s3_data": null}], "text": "Figure 3-1 CREATE PERMISSION SQL statement", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [136.8, 340.95599, 215.37601, 352.05600000000004], "page": 10, "span": [0, 11], "__ref_s3_data": null}], "text": "Column mask", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.8, 291.69885, 542.76648, 336.91128999999995], "page": 10, "span": [0, 297], "__ref_s3_data": null}], "text": "A column mask is a database object that manifests a column value access control rule for a specific column in a specific table. It uses a CASE expression that describes what you see when you access the column. For example, a teller can see only the last four digits of a tax identification number.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79959, 711.2779500000001, 412.20758, 720.49097], "page": 11, "span": [0, 62], "__ref_s3_data": null}], "text": "Table 3-1 summarizes these special registers and their values.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/3"}, {"prov": [{"bbox": [136.8, 690.177, 372.60364, 698.50195], "page": 11, "span": [0, 58], "__ref_s3_data": null}], "text": "Table 3-1 Special registers and their corresponding values", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [136.8, 556.29846, 538.4939, 577.51129], "page": 11, "span": [0, 97], "__ref_s3_data": null}], "text": "Figure 3-5 shows the difference in the special register values when an adopted authority is used:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 539.25891, 411.36139, 548.47192], "page": 11, "span": [0, 75], "__ref_s3_data": null}], "text": "GLYPH<SM590000> A user connects to the server using the user profile ALICE.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.8, 522.27911, 453.2580899999999, 531.49213], "page": 11, "span": [0, 77], "__ref_s3_data": null}], "text": "GLYPH<SM590000> USER and CURRENT USER initially have the same value of ALICE.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.8, 493.2995, 541.44983, 514.51233], "page": 11, "span": [0, 160], "__ref_s3_data": null}], "text": "GLYPH<SM590000> ALICE calls an SQL procedure that is named proc1, which is owned by user profile JOE and was created to adopt JOE's authority when it is called.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.8, 452.26028, 547.21674, 485.4729], "page": 11, "span": [0, 253], "__ref_s3_data": null}], "text": "GLYPH<SM590000> While the procedure is running, the special register USER still contains the value of ALICE because it excludes any adopted authority. The special register CURRENT USER contains the value of JOE because it includes any adopted authority.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.80101, 423.28066999999993, 547.35406, 444.49347], "page": 11, "span": [0, 133], "__ref_s3_data": null}], "text": "GLYPH<SM590000> When proc1 ends, the session reverts to its original state with both USER and CURRENT USER having the value of ALICE.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/10"}, {"prov": [{"bbox": [136.8, 186.95709, 341.25662, 195.2821], "page": 11, "span": [0, 50], "__ref_s3_data": null}], "text": "Figure 3-5 Special registers and adopted authority", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [64.800003, 154.45727999999997, 247.02536, 166.44528000000003], "page": 11, "span": [0, 31], "__ref_s3_data": null}], "text": "3.2.2 Built-in global variables", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.8, 119.07847000000004, 518.00116, 140.29128000000003], "page": 11, "span": [0, 161], "__ref_s3_data": null}], "text": "Built-in global variables are provided with the database manager and are used in SQL statements to retrieve scalar values that are associated with the variables.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 73.05927999999994, 532.3385, 106.27189999999996], "page": 11, "span": [0, 233], "__ref_s3_data": null}], "text": "IBM DB2 for i supports nine different built-in global variables that are read only and maintained by the system. These global variables can be used to identify attributes of the database connection and used as part of the RCAC logic.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 711.27832, 342.54773, 720.49133], "page": 12, "span": [0, 51], "__ref_s3_data": null}], "text": "Table 3-2 lists the nine built-in global variables.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/4"}, {"prov": [{"bbox": [64.800003, 690.177, 201.18147, 698.50195], "page": 12, "span": [0, 35], "__ref_s3_data": null}], "text": "Table 3-2 Built-in global variables", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [64.800003, 455.03628999999995, 384.36389, 469.79929], "page": 12, "span": [0, 34], "__ref_s3_data": null}], "text": "3.3 VERIFY_GROUP_FOR_USER function", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.8, 356.25939999999997, 547.23474, 437.47128], "page": 12, "span": [0, 576], "__ref_s3_data": null}], "text": "The VERIFY_GROUP_FOR_USER function was added in IBM i 7.2. Although it is primarily intended for use with RCAC permissions and masks, it can be used in other SQL statements. The first parameter must be one of these three special registers: SESSION_USER, USER, or CURRENT_USER. The second and subsequent parameters are a list of user or group profiles. Each of these values must be 1 - 10 characters in length. These values are not validated for their existence, which means that you can specify the names of user profiles that do not exist without receiving any kind of error.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.80002, 310.29996, 547.25739, 343.51257], "page": 12, "span": [0, 235], "__ref_s3_data": null}], "text": "If a special register value is in the list of user profiles or it is a member of a group profile included in the list, the function returns a long integer value of 1. Otherwise, it returns a value of 0. It never returns the null value.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.80002, 288.2804, 458.44525000000004, 297.49338], "page": 12, "span": [0, 63], "__ref_s3_data": null}], "text": "Here is an example of using the VERIFY_GROUP_FOR_USER function:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.80002, 271.24081, 406.07751, 280.4538], "page": 12, "span": [0, 54], "__ref_s3_data": null}], "text": "1. There are user profiles for MGR, JANE, JUDY, and TONY.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.80002, 254.26099999999997, 396.98816, 263.47399999999993], "page": 12, "span": [0, 55], "__ref_s3_data": null}], "text": "2. The user profile JANE specifies a group profile of MGR.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.80002, 225.28139, 536.5686, 246.49419], "page": 12, "span": [0, 124], "__ref_s3_data": null}], "text": "3. If a user is connected to the server using user profile JANE, all of the following function invocations return a value of 1:", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [151.20018, 150.57143999999994, 451.01605, 217.30542000000003], "page": 12, "span": [0, 265], "__ref_s3_data": null}], "text": "VERIFY_GROUP_FOR_USER (CURRENT_USER, 'MGR') VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JAN<PERSON>', 'MGR') VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JAN<PERSON>', 'MGR', 'STEVE') The following function invocation returns a value of 0: VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JUDY', 'TONY')", "type": "paragraph", "payload": null, "name": "Code", "font": null}, {"prov": [{"bbox": [136.79959, 711.56677, 166.73935, 720.34155], "page": 13, "span": [0, 6], "__ref_s3_data": null}], "text": "RETURN", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79959, 699.56696, 156.77934, 708.34174], "page": 13, "span": [0, 4], "__ref_s3_data": null}], "text": "CASE", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.79959, 531.56952, 521.57428, 696.3419199999998], "page": 13, "span": [0, 437], "__ref_s3_data": null}], "text": "WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'HR', 'EMP' ) = 1 THEN EMPLOYEES . DATE_OF_BIRTH WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER = EMPLOYEES . USER_ID THEN EMPLOYEES . DATE_OF_BIRTH WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER <> EMPLOYEES . USER_ID THEN ( 9999 || '-' || MONTH ( EMPLOYEES . DATE_OF_BIRTH ) || '-' || DAY (EMPLOYEES.DATE_OF_BIRTH )) ELSE NULL END ENABLE ;", "type": "paragraph", "payload": null, "name": "Code", "font": null}, {"prov": [{"bbox": [136.79959, 495.28128000000004, 547.21222, 516.49408], "page": 13, "span": [0, 133], "__ref_s3_data": null}], "text": "2. The other column to mask in this example is the TAX_ID information. In this example, the rules to enforce include the following ones:", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [152.0394, 478.30147999999997, 469.1528, 487.51446999999996], "page": 13, "span": [0, 62], "__ref_s3_data": null}], "text": "-Human Resources can see the unmasked TAX_ID of the employees.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [152.0394, 461.2619, 403.95953, 470.47488], "page": 13, "span": [0, 50], "__ref_s3_data": null}], "text": "-Employees can see only their own unmasked TAX_ID.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [152.0394, 432.28229, 545.16846, 453.49509], "page": 13, "span": [0, 129], "__ref_s3_data": null}], "text": "-Managers see a masked version of TAX_ID with the first five characters replaced with the X character (for example, XXX-XX-1234).", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [152.0394, 415.3024899999999, 529.46362, 424.51547], "page": 13, "span": [0, 77], "__ref_s3_data": null}], "text": "-Any other person sees the entire TAX_ID as masked, for example, XXX-XX-XXXX.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [151.19978, 398.26291, 530.0603, 407.47589], "page": 13, "span": [0, 82], "__ref_s3_data": null}], "text": "To implement this column mask, run the SQL statement that is shown in Example 3-9.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.8, 107.55115999999998, 526.55469, 368.32189999999997], "page": 13, "span": [0, 590], "__ref_s3_data": null}], "text": "CREATE MASK HR_SCHEMA.MASK_TAX_ID_ON_EMPLOYEES ON HR_SCHEMA.EMPLOYEES AS EMPLOYEES FOR COLUMN TAX_ID RETURN CASE WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'HR' ) = 1 THEN EMPLOYEES . TAX_ID WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER = EMPLOYEES . USER_ID THEN EMPLOYEES . TAX_ID WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER <> EMPLOYEES . USER_ID THEN ( 'XXX-XX-' CONCAT QSYS2 . SUBSTR ( EMPLOYEES . TAX_ID , 8 , 4 ) ) WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'EMP' ) = 1 THEN EMPLOYEES . TAX_ID ELSE 'XXX-XX-XXXX' END ENABLE ;", "type": "paragraph", "payload": null, "name": "Code", "font": null}, {"prov": [{"bbox": [136.8, 377.15698, 351.9873, 385.48199], "page": 13, "span": [0, 48], "__ref_s3_data": null}], "text": "Example 3-9 Creating a mask on the TAX_ID column", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [136.8, 711.27832, 449.9523899999999, 720.49133], "page": 14, "span": [0, 62], "__ref_s3_data": null}], "text": "3. Figure 3-10 shows the masks that are created in the HR_SCHEMA.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/11"}, {"prov": [{"bbox": [64.800003, 610.13702, 293.13809, 618.46198], "page": 14, "span": [0, 52], "__ref_s3_data": null}], "text": "Figure 3-10 Column masks shown in System i Navigator", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [64.800003, 577.63727, 203.98521, 589.62531], "page": 14, "span": [0, 21], "__ref_s3_data": null}], "text": "3.6.6 Activating RCAC", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.8, 530.25867, 547.22565, 563.47131], "page": 14, "span": [0, 265], "__ref_s3_data": null}], "text": "Now that you have created the row permission and the two column masks, RCAC must be activated. The row permission and the two column masks are enabled (last clause in the scripts), but now you must activate RCAC on the table. To do so, complete the following steps:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 513.27887, 409.47888, 522.49188], "page": 14, "span": [0, 54], "__ref_s3_data": null}], "text": "1. Run the SQL statements that are shown in Example 3-10.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.8, 492.11699999999996, 375.29099, 500.44202], "page": 14, "span": [0, 51], "__ref_s3_data": null}], "text": "Example 3-10 Activating RCAC on the EMPLOYEES table", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [136.8, 474.56711, 376.67661, 483.34189], "page": 14, "span": [0, 45], "__ref_s3_data": null}], "text": "/* Active Row Access Control (permissions) */", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.8, 462.56729, 354.86963, 471.34207], "page": 14, "span": [0, 39], "__ref_s3_data": null}], "text": "/* Active Column Access Control (masks)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [365.77313, 462.56729, 376.67661, 471.34207], "page": 14, "span": [0, 2], "__ref_s3_data": null}], "text": "*/", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 450.56747, 291.7178, 459.34225], "page": 14, "span": [0, 31], "__ref_s3_data": null}], "text": "ALTER TABLE HR_SCHEMA.EMPLOYEES", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 438.56765999999993, 271.67831, 447.34244], "page": 14, "span": [0, 27], "__ref_s3_data": null}], "text": "ACTIVATE ROW ACCESS CONTROL", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 426.56784, 291.7178, 435.34262], "page": 14, "span": [0, 31], "__ref_s3_data": null}], "text": "ACTIVATE COLUMN ACCESS CONTROL;", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [136.8, 378.27979, 540.80145, 411.4924], "page": 14, "span": [0, 228], "__ref_s3_data": null}], "text": "2. Look at the definition of the EMPLOYEE table, as shown in Figure 3-11. To do this, from the main navigation pane of System i Navigator, click Schemas  HR_SCHEMA  Tables , right-click the EMPLOYEES table, and click Definition .", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/12"}, {"prov": [{"bbox": [64.800003, 134.63710000000003, 347.43054, 142.96210999999994], "page": 14, "span": [0, 65], "__ref_s3_data": null}], "text": "Figure 3-11 Selecting the EMPLOYEES table from System i Navigator", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [136.79959, 687.27832, 514.04858, 720.49097], "page": 15, "span": [0, 225], "__ref_s3_data": null}], "text": "2. Figure 4-68 shows the Visual Explain of the same SQL statement, but with RCAC enabled. It is clear that the implementation of the SQL statement is more complex because the row permission rule becomes part of the WHERE clause.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [136.8, 252.21875, 547.23944, 285.43137], "page": 15, "span": [0, 229], "__ref_s3_data": null}], "text": "3. Compare the advised indexes that are provided by the Optimizer without RCAC and with RCAC enabled. Figure 4-69 shows the index advice for the SQL statement without RCAC enabled. The index being advised is for the ORDER BY clause.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/13"}, {"prov": [{"bbox": [136.8, 303.117, 327.09329, 311.44202], "page": 15, "span": [0, 44], "__ref_s3_data": null}], "text": "Figure 4-68 Visual Explain with RCAC enabled", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/14"}, {"prov": [{"bbox": [64.800003, 116.15710000000001, 227.10149, 124.48209999999995], "page": 15, "span": [0, 37], "__ref_s3_data": null}], "text": "Figure 4-69 Index advice with no RCAC", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [64.800308, 85.39238, 500.69727, 720.32703], "page": 16, "span": [0, 1998], "__ref_s3_data": null}], "text": "THEN C . CUSTOMER_TAX_ID WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'TELLER' ) = 1 THEN ( 'XXX-XX-' CONCAT QSYS2 . SUBSTR ( C . CUSTOMER_TAX_ID , 8 , 4 ) ) WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_TAX_ID ELSE 'XXX-XX-XXXX' END ENABLE ; CREATE MASK BANK_SCHEMA.MASK_DRIVERS_LICENSE_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_DRIVERS_LICENSE_NUMBER RETURN CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'TELLER' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER ELSE '*************' END ENABLE ; CREATE MASK BANK_SCHEMA.MASK_LOGIN_ID_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_LOGIN_ID RETURN CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_LOGIN_ID WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_LOGIN_ID ELSE '*****' END ENABLE ; CREATE MASK BANK_SCHEMA.MASK_SECURITY_QUESTION_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_SECURITY_QUESTION RETURN CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION ELSE '*****' END ENABLE ; CREATE MASK BANK_SCHEMA.MASK_SECURITY_QUESTION_ANSWER_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_SECURITY_QUESTION_ANSWER RETURN CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION_ANSWER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION_ANSWER ELSE '*****' END ENABLE ; ALTER TABLE BANK_SCHEMA.CUSTOMERS ACTIVATE ROW ACCESS CONTROL ACTIVATE COLUMN ACCESS CONTROL ;", "type": "paragraph", "payload": null, "name": "Code", "font": null}, {"prov": [{"bbox": [287.22, 741.25195, 414.24481, 763.45197], "page": 18, "span": [0, 10], "__ref_s3_data": null}], "text": "Back cover", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [27.0, 651.53998, 447.36002, 718.*************], "page": 18, "span": [0, 54], "__ref_s3_data": null}], "text": "Row and Column Access Control Support in IBM DB2 for i", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [26.700001, 525.16803, 127.4436, 549.828], "page": 18, "span": [0, 40], "__ref_s3_data": null}], "text": "Implement roles and separation of duties", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [26.700001, 469.12802, 120.**************, 507.82803], "page": 18, "span": [0, 40], "__ref_s3_data": null}], "text": "Leverage row permissions on the database", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [152.94, 468.40811, 414.08423, 549.27142], "page": 18, "span": [0, 464], "__ref_s3_data": null}], "text": "This IBM Redpaper publication provides information about the IBM i 7.2 feature of IBM DB2 for i Row and Column Access Control (RCAC). It offers a broad description of the function and advantages of controlling access to data in a comprehensive and transparent way. This publication helps you understand the capabilities of RCAC and provides examples of defining, creating, and implementing the row permissions and column masks in a relational database environment.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [26.700001, 413.14801, 121.44960000000002, 451.84801999999996], "page": 18, "span": [0, 40], "__ref_s3_data": null}], "text": "Protect columns by defining column masks", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [152.94002, 403.42905, 414.17383, 460.29272], "page": 18, "span": [0, 309], "__ref_s3_data": null}], "text": "This paper is intended for database engineers, data-centric application developers, and security officers who want to design and implement RCAC as a part of their data control and governance policy. A solid background in IBM i object level security, DB2 for i relational database concepts, and SQL is assumed.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/15"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/16"}, {"prov": [{"bbox": [467.3399999999999, 489.83939, 559.80933, 544.28168], "page": 18, "span": [0, 44], "__ref_s3_data": null}], "text": "INTERNATIONAL TECHNICAL SUPPORT ORGANIZATION", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [467.3399999999999, 405.52802, 587.38916, 440.20801], "page": 18, "span": [0, 60], "__ref_s3_data": null}], "text": "BUILDING TECHNICAL INFORMATION BASED ON PRACTICAL EXPERIENCE", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [467.3399999999999, 250.36594000000002, 587.52051, 392.1397099999999], "page": 18, "span": [0, 323], "__ref_s3_data": null}], "text": "IBM Redbooks are developed by the IBM International Technical Support Organization. Experts from IBM, Customers and Partners from around the world create timely technical information based on realistic scenarios. Specific recommendations are provided to help you implement IT solutions more effectively in your environment.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [467.3399999999999, 190.48810000000003, 570.948, 213.16809], "page": 18, "span": [0, 39], "__ref_s3_data": null}], "text": "For more information: ibm.com /redbooks", "type": "paragraph", "payload": null, "name": "Text", "font": null}], "figures": [{"prov": [{"bbox": [513.4560546875, 737.1808471679688, 586.1583251953125, 765.9149017333984], "page": 1, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [33.09040069580078, 89.5469970703125, 585.1502075195312, 498.9671630859375], "page": 1, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [316.9404296875, 17.5740966796875, 581.354736328125, 81.87213134765625], "page": 1, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [143.39866638183594, 506.378662109375, 179.56256103515625, 521.7389221191406], "page": 3, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [64.1669921875, 103.87176513671875, 258.7742919921875, 188.49365234375], "page": 3, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [142.52883911132812, 288.79351806640625, 251.47850036621094, 416.9550476074219], "page": 4, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [145.4144744873047, 156.616943359375, 252.08840942382812, 264.7552490234375], "page": 4, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [32.075252532958984, 554.0420684814453, 239.620361328125, 721.4226226806641], "page": 5, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [135.92466735839844, 103.39019775390625, 546.4456176757812, 416.0727844238281], "page": 7, "span": [0, 43], "__ref_s3_data": null}], "text": "Figure 1-2 Existing row and column controls", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [135.97177124023438, 381.39068603515625, 545.4180908203125, 684.5892486572266], "page": 10, "span": [0, 42], "__ref_s3_data": null}], "text": "Figure 3-1 CREATE PERMISSION SQL statement", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [135.64837646484375, 197.24334716796875, 301.2367248535156, 407.8262939453125], "page": 11, "span": [0, 50], "__ref_s3_data": null}], "text": "Figure 3-5 Special registers and adopted authority", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [63.801902770996094, 621.9678497314453, 547.11474609375, 696.6175842285156], "page": 14, "span": [0, 52], "__ref_s3_data": null}], "text": "Figure 3-10 Column masks shown in System i Navigator", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [63.985130310058594, 145.8603515625, 530.0478515625, 364.09503173828125], "page": 14, "span": [0, 65], "__ref_s3_data": null}], "text": "Figure 3-11 Selecting the EMPLOYEES table from System i Navigator", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [136.5016632080078, 314.4587707519531, 545.4508666992188, 672.7508773803711], "page": 15, "span": [0, 44], "__ref_s3_data": null}], "text": "Figure 4-68 Visual Explain with RCAC enabled", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [64.27847290039062, 127.91290283203125, 506.39263916015625, 238.41851806640625], "page": 15, "span": [0, 37], "__ref_s3_data": null}], "text": "Figure 4-69 Index advice with no RCAC", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [485.1698303222656, 737.8084144592285, 566.2962036132812, 766.7408027648926], "page": 18, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [474.35540771484375, 602.1873931884766, 592.2726440429688, 711.9486846923828], "page": 18, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}], "tables": [{"prov": [{"bbox": [136.1496124267578, 76.34844970703125, 547.5267944335938, 659.9669647216797], "page": 2, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table-of-contents", "payload": null, "#-cols": 2, "#-rows": 42, "data": [[{"bbox": [136.8, 132.64862000000005, 172.89404, 141.86163], "spans": [[0, 0]], "text": "Notices", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 0, "row-header": true, "row-span": [0, 1]}, {"bbox": [175.01952, 132.64862000000005, 547.18982, 141.86163], "spans": [[0, 1]], "text": ". . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . vii", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [136.79901, 145.12847999999997, 189.86537, 154.34149000000002], "spans": [[1, 0]], "text": "Trademarks", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": true, "row-span": [1, 2]}, {"bbox": [195.39685, 145.12847999999997, 547.18286, 154.34149000000002], "spans": [[1, 1]], "text": ". . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . viii", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [136.79901, 167.62811, 279.39731, 176.84113000000002], "spans": [[2, 0]], "text": "DB2 for i Center of Excellence", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [280.61942, 167.62811, 547.1908, 176.84113000000002], "spans": [[2, 1]], "text": ". . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . ix", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [136.79901, 190.12775, 172.84424, 199.34076000000005], "spans": [[3, 0]], "text": "Preface", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [175.01852, 190.12775, 547.18286, 199.34076000000005], "spans": [[3, 1]], "text": ". . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . xi", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [136.79803, 202.60760000000005, 547.18085, 211.82061999999996], "spans": [[4, 0]], "text": "Authors . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . xi", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": null, "spans": [[4, 1]], "text": "", "type": "body"}], [{"bbox": [136.79803, 215.14721999999995, 339.18292, 224.36023], "spans": [[5, 0]], "text": "Now you can become a published author, too! . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [535.53925, 215.14721999999995, 547.13879, 224.36023], "spans": [[5, 1]], "text": "xiii", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [136.79803, 227.62707999999998, 529.99506, 236.84009000000003], "spans": [[6, 0]], "text": "Comments welcome. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [535.5495, 227.62707999999998, 547.19788, 236.84009000000003], "spans": [[6, 1]], "text": "xiii", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}], [{"bbox": [136.79807, 240.10693000000003, 284.02866, 249.31994999999995], "spans": [[7, 0]], "text": "Stay connected to IBM Redbooks", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 7, "row-header": true, "row-span": [7, 8]}, {"bbox": [289.54449, 240.10693000000003, 547.12115, 249.31994999999995], "spans": [[7, 1]], "text": ". . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . xiv", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 7, "row-header": false, "row-span": [7, 8]}], [{"bbox": [136.79807, 262.60657000000003, 373.17566, 271.81958], "spans": [[8, 0]], "text": "Chapter 1. Securing and protecting IBM DB2 data", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 8, "row-header": true, "row-span": [8, 9]}, {"bbox": [375.11798, 262.60657000000003, 547.19781, 271.81958], "spans": [[8, 1]], "text": ". . . . . . . . . . . . . . . . . . . . . . . . . . . . . 1", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 8, "row-header": false, "row-span": [8, 9]}], [{"bbox": [136.79808, 274.60637999999994, 150.88702, 283.8194], "spans": [[9, 0]], "text": "1.1 Security fundamentals. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 2", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 9, "row-header": true, "row-span": [9, 10]}, {"bbox": null, "spans": [[9, 1]], "text": "", "type": "body"}], [{"bbox": [136.79807, 287.14606000000003, 150.62746, 296.35904], "spans": [[10, 0]], "text": "1.2 Current state of IBM i security . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 10, "row-header": true, "row-span": [10, 11]}, {"bbox": [541.66113, 287.14606000000003, 547.19287, 296.35904], "spans": [[10, 1]], "text": "2", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 10, "row-header": false, "row-span": [10, 11]}], [{"bbox": [136.79807, 299.62595, 150.84943, 308.83893], "spans": [[11, 0]], "text": "1.3 DB2 for i security controls . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 3", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 11, "row-header": true, "row-span": [11, 12]}, {"bbox": null, "spans": [[11, 1]], "text": "", "type": "body"}], [{"bbox": [151.1972, 312.1058300000001, 173.38289, 321.3188200000001], "spans": [[12, 0]], "text": "1.3.1 Existing row and column control . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 12, "row-header": true, "row-span": [12, 13]}, {"bbox": [541.6015, 312.1058300000001, 547.14795, 321.3188200000001], "spans": [[12, 1]], "text": "4", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 12, "row-header": false, "row-span": [12, 13]}], [{"bbox": [151.1972, 324.64548, 173.4189, 333.8584599999999], "spans": [[13, 0]], "text": "1.3.2 New controls: Row and Column Access Control. . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 13, "row-header": true, "row-span": [13, 14]}, {"bbox": [541.6355, 324.64548, 547.19092, 333.8584599999999], "spans": [[13, 1]], "text": "5", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 13, "row-header": false, "row-span": [13, 14]}], [{"bbox": [136.79704, 347.14511, 336.82071, 356.35809], "spans": [[14, 0]], "text": "Chapter 2. Roles and separation of duties . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 14, "row-header": true, "row-span": [14, 15]}, {"bbox": [541.64282, 347.14511, 547.19476, 356.35809], "spans": [[14, 1]], "text": "7", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 14, "row-header": false, "row-span": [14, 15]}], [{"bbox": [136.79704, 359.14493, 150.644, 368.35791], "spans": [[15, 0]], "text": "2.1 Roles . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 15, "row-header": true, "row-span": [15, 16]}, {"bbox": [541.66589, 359.14493, 547.20471, 368.35791], "spans": [[15, 1]], "text": "8", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 15, "row-header": false, "row-span": [15, 16]}], [{"bbox": [151.1972, 371.62482, 173.60995, 380.8378000000001], "spans": [[16, 0]], "text": "2.1.1 DDM and DRDA application server access: QIBM_DB_DDMDRDA . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 16, "row-header": true, "row-span": [16, 17]}, {"bbox": [541.55585, 371.62482, 547.15906, 380.8378000000001], "spans": [[16, 1]], "text": "8", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 16, "row-header": false, "row-span": [16, 17]}], [{"bbox": [151.1972, 384.10470999999995, 173.41664, 393.31769], "spans": [[17, 0]], "text": "2.1.2 Toolbox application server access: QIBM_DB_ZDA. . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 17, "row-header": true, "row-span": [17, 18]}, {"bbox": [541.59595, 384.10470999999995, 547.15082, 393.31769], "spans": [[17, 1]], "text": "8", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 17, "row-header": false, "row-span": [17, 18]}], [{"bbox": [151.1972, 396.64435, 173.41859, 405.85733], "spans": [[18, 0]], "text": "2.1.3 Database Administrator function: QIBM_DB_SQLADM . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 18, "row-header": true, "row-span": [18, 19]}, {"bbox": [541.63025, 396.64435, 547.18561, 405.85733], "spans": [[18, 1]], "text": "9", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 18, "row-header": false, "row-span": [18, 19]}], [{"bbox": [151.1972, 409.12424000000004, 173.38629, 418.33722], "spans": [[19, 0]], "text": "2.1.4 Database Information function: QIBM_DB_SYSMON", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 19, "row-header": true, "row-span": [19, 20]}, {"bbox": [416.81775, 409.12424000000004, 536.08411, 418.33722], "spans": [[19, 1]], "text": ". . . . . . . . . . . . . . . . . . . . . . 9", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 19, "row-header": false, "row-span": [19, 20]}], [{"bbox": [151.1972, 421.60413, 173.44926, 430.81711], "spans": [[20, 0]], "text": "2.1.5 Security Administrator function: QIBM_DB_SECADM . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 20, "row-header": true, "row-span": [20, 21]}, {"bbox": [541.59894, 421.60413, 547.16193, 430.81711], "spans": [[20, 1]], "text": "9", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 20, "row-header": false, "row-span": [20, 21]}], [{"bbox": [151.1972, 434.1437700000001, 173.32208, 443.35675], "spans": [[21, 0]], "text": "2.1.6 Change Function Usage CL command . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 21, "row-header": true, "row-span": [21, 22]}, {"bbox": [536.10443, 434.1437700000001, 547.16687, 443.35675], "spans": [[21, 1]], "text": "10", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 21, "row-header": false, "row-span": [21, 22]}], [{"bbox": [151.1972, 446.62366, 173.35822, 455.83663999999993], "spans": [[22, 0]], "text": "2.1.7 Verifying function usage IDs for RCAC with the FUNCTION_USAGE view . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 22, "row-header": true, "row-span": [22, 23]}, {"bbox": [536.0755, 446.62366, 547.15601, 455.83663999999993], "spans": [[22, 1]], "text": "10", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 22, "row-header": false, "row-span": [22, 23]}], [{"bbox": [136.79704, 459.10355, 150.85457, 468.31653], "spans": [[23, 0]], "text": "2.2 Separation of duties . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 10", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 23, "row-header": true, "row-span": [23, 24]}, {"bbox": null, "spans": [[23, 1]], "text": "", "type": "body"}], [{"bbox": [136.79703, 481.60318, 348.68503, 490.81616], "spans": [[24, 0]], "text": "Chapter 3. Row and Column Access Control", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 24, "row-header": true, "row-span": [24, 25]}, {"bbox": [350.09741, 481.60318, 547.1958, 490.81616], "spans": [[24, 1]], "text": ". . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 13", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 24, "row-header": false, "row-span": [24, 25]}], [{"bbox": [136.79703, 493.603, 150.70105, 502.81598], "spans": [[25, 0]], "text": "3.1 Explanation of RCAC and the concept of access control . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 25, "row-header": true, "row-span": [25, 26]}, {"bbox": [536.04248, 493.603, 547.16571, 502.81598], "spans": [[25, 1]], "text": "14", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 25, "row-header": false, "row-span": [25, 26]}], [{"bbox": [151.19719, 506.14264, 173.35429, 515.35562], "spans": [[26, 0]], "text": "3.1.1 Row permission and column mask definitions", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 26, "row-header": true, "row-span": [26, 27]}, {"bbox": [536.07721, 506.14264, 547.15576, 515.35562], "spans": [[26, 1]], "text": "14", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 26, "row-header": false, "row-span": [26, 27]}], [{"bbox": [151.19719, 518.62253, 173.44292, 527.83551], "spans": [[27, 0]], "text": "3.1.2 Enabling and activating RCAC . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 27, "row-header": true, "row-span": [27, 28]}, {"bbox": [535.99622, 518.62253, 547.11908, 527.83551], "spans": [[27, 1]], "text": "16", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 27, "row-header": false, "row-span": [27, 28]}], [{"bbox": [136.79703, 531.1621700000001, 150.64432, 540.37517], "spans": [[28, 0]], "text": "3.2 Special registers and built-in global variables . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 28, "row-header": true, "row-span": [28, 29]}, {"bbox": [536.06702, 531.1621700000001, 547.14484, 540.37517], "spans": [[28, 1]], "text": "18", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 28, "row-header": false, "row-span": [28, 29]}], [{"bbox": [151.19719, 543.64204, 173.41321, 552.8550399999999], "spans": [[29, 0]], "text": "3.2.1 Special registers . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 29, "row-header": true, "row-span": [29, 30]}, {"bbox": [536.05188, 543.64204, 547.15991, 552.8550399999999], "spans": [[29, 1]], "text": "18", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 29, "row-header": false, "row-span": [29, 30]}], [{"bbox": [151.19719, 556.12192, 173.35269, 565.33492], "spans": [[30, 0]], "text": "3.2.2 Built-in global variables . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 30, "row-header": true, "row-span": [30, 31]}, {"bbox": [536.09912, 556.12192, 547.17688, 565.33492], "spans": [[30, 1]], "text": "19", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 30, "row-header": false, "row-span": [30, 31]}], [{"bbox": [136.79703, 568.66156, 150.62514, 577.87456], "spans": [[31, 0]], "text": "3.3 VERIFY_GROUP_FOR_USER function . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 31, "row-header": true, "row-span": [31, 32]}, {"bbox": [536.06152, 568.66156, 547.12402, 577.87456], "spans": [[31, 1]], "text": "20", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 31, "row-header": false, "row-span": [31, 32]}], [{"bbox": [136.79703, 581.14143, 150.63004, 590.35443], "spans": [[32, 0]], "text": "3.4 Establishing and controlling accessibility by using the RCAC rule text . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 32, "row-header": true, "row-span": [32, 33]}, {"bbox": [536.16315, 581.14143, 547.22955, 590.35443], "spans": [[32, 1]], "text": "21", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 32, "row-header": false, "row-span": [32, 33]}], [{"bbox": [156.21107, 606.16095, 530.56512, 615.37395], "spans": [[33, 0]], "text": "Human resources example . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 33, "row-header": true, "row-span": [33, 34]}, {"bbox": null, "spans": [[33, 1]], "text": "", "type": "body"}], [{"bbox": [136.79701, 606.16095, 150.6642, 615.37395], "spans": [[34, 0]], "text": "3.6", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 34, "row-header": true, "row-span": [34, 35]}, {"bbox": [536.112, 606.16095, 547.20575, 615.37395], "spans": [[34, 1]], "text": "22", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 34, "row-header": false, "row-span": [34, 35]}], [{"bbox": [151.19717, 618.64082, 173.41692, 627.85382], "spans": [[35, 0]], "text": "3.6.1 Assigning the QIBM_DB_SECADM function ID to the consultants. . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 35, "row-header": true, "row-span": [35, 36]}, {"bbox": [536.04633, 618.64082, 547.15875, 640.3336899999999], "spans": [[35, 1]], "text": "23 23", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 35, "row-header": false, "row-span": [35, 36]}], [{"bbox": [151.19717, 631.1206999999999, 173.32271, 640.3336899999999], "spans": [[36, 0]], "text": "3.6.2 Creating group profiles for the users and their roles . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 36, "row-header": true, "row-span": [36, 37]}, {"bbox": null, "spans": [[36, 1]], "text": "", "type": "body"}], [{"bbox": [151.19717, 643.66034, 173.32227, 652.87334], "spans": [[37, 0]], "text": "3.6.3 Demonstrating data access without RCAC . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 37, "row-header": true, "row-span": [37, 38]}, {"bbox": [536.0882, 643.66034, 547.15076, 652.87334], "spans": [[37, 1]], "text": "24", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 37, "row-header": false, "row-span": [37, 38]}], [{"bbox": [151.19717, 656.14021, 173.35289, 665.35321], "spans": [[38, 0]], "text": "3.6.4 Defining and creating row permissions . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 38, "row-header": true, "row-span": [38, 39]}, {"bbox": [536.073, 656.14021, 547.15088, 665.35321], "spans": [[38, 1]], "text": "25", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 38, "row-header": false, "row-span": [38, 39]}], [{"bbox": [151.19717, 668.62009, 173.35289, 677.83309], "spans": [[39, 0]], "text": "3.6.5 Defining and creating column masks", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 39, "row-header": true, "row-span": [39, 40]}, {"bbox": [344.98996, 668.62009, 547.16089, 677.83309], "spans": [[39, 1]], "text": ". . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 26", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 39, "row-header": false, "row-span": [39, 40]}], [{"bbox": [151.19717, 681.15973, 173.38359, 690.37273], "spans": [[40, 0]], "text": "3.6.6 Activating RCAC . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 40, "row-header": true, "row-span": [40, 41]}, {"bbox": [536.08765, 681.15973, 547.18085, 690.37273], "spans": [[40, 1]], "text": "28", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 40, "row-header": false, "row-span": [40, 41]}], [{"bbox": [151.19717, 706.119492, 173.44592, 715.332497], "spans": [[41, 0]], "text": "3.6.8 Demonstrating data access with a view and RCAC . . . . . . . . . . . . . . . . . . . . . . .", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 41, "row-header": true, "row-span": [41, 42]}, {"bbox": [535.99847, 706.119492, 547.12286, 715.332497], "spans": [[41, 1]], "text": "32", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 41, "row-header": false, "row-span": [41, 42]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [135.52462768554688, 349.949462890625, 545.8714599609375, 502.2747802734375], "page": 8, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 2-1 FUNCTION_USAGE view", "type": "table", "payload": null, "#-cols": 3, "#-rows": 5, "data": [[{"bbox": [142.8, 296.5379899999999, 202.245, 304.86301], "spans": [[0, 0]], "text": "Column name", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [216.80878999999996, 296.5379899999999, 257.21069, 304.86301], "spans": [[0, 1]], "text": "Data type", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [289.47479, 296.5379899999999, 338.89468, 304.86301], "spans": [[0, 2]], "text": "Description", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [142.8, 315.55771, 203.2323, 323.88272], "spans": [[1, 0]], "text": "FUNCTION_ID", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [216.7854, 315.55771, 276.0036, 323.88272], "spans": [[1, 1]], "text": "VARCHAR(30)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [289.4577, 315.55771, 359.85394, 323.88272], "spans": [[1, 2]], "text": "ID of the function.", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [142.8, 334.51801, 198.6693, 342.84302], "spans": [[2, 0]], "text": "USER_NAME", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [216.74129999999997, 334.51801, 275.92349, 342.84302], "spans": [[2, 1]], "text": "VARCHAR(10)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [289.38208, 334.51801, 515.05359, 342.84302], "spans": [[2, 2]], "text": "Name of the user profile that has a usage setting for this  function.", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [142.79999, 364.51862, 173.98318, 372.84363], "spans": [[3, 0]], "text": "USAGE", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [216.77367999999998, 364.51862, 270.97977, 372.84363], "spans": [[3, 1]], "text": "VARCHAR(7)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [289.41626, 364.51862, 346.88757, 372.84363], "spans": [[3, 2]], "text": "Usage setting: GLYPH<SM590000> ALLOWED: The user profile is allowed to use the function. GLYPH<SM590000> DENIED: The user profile is not allowed to use the function.", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [142.8, 405.55865, 196.2249, 413.88367000000005], "spans": [[4, 0]], "text": "USER_TYPE", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [216.75211, 405.55865, 270.99872, 413.88367000000005], "spans": [[4, 1]], "text": "VARCHAR(5)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [289.43161, 405.55865, 367.8009, 413.88367000000005], "spans": [[4, 2]], "text": "Type of user profile: GLYPH<SM590000> USER: The user profile is a user. GLYPH<SM590000> GROUP: The user profile is a group.", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [64.41139221191406, 70.39208984375, 547.3950805664062, 398.3863830566406], "page": 9, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 2-2 Comparison of the different function usage IDs and *JOBCTL authority", "type": "table", "payload": null, "#-cols": 6, "#-rows": 13, "data": [[{"bbox": [70.800301, 400.51827999999995, 119.78551, 408.84329], "spans": [[0, 0]], "text": "User action", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [424.93805, 447.52255, 433.26297000000005, 487.01999], "spans": [[0, 1]], "text": "*JOBCTL", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [450.13806, 401.6000700000001, 458.46298, 487.01999], "spans": [[0, 2]], "text": "QIBM_DB_SECADM", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [475.93835000000007, 401.53442, 484.26327999999995, 487.01999], "spans": [[0, 3]], "text": "QIBM_DB_SQLADM", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [501.13837, 401.6145, 509.46329, 487.01999], "spans": [[0, 4]], "text": "QIBM_DB_SYSMON", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [526.39862, 432.79944, 534.72357, 487.02005], "spans": [[0, 5]], "text": "No Authority", "type": "col_header", "col": 5, "col-header": true, "col-span": [5, 6], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [70.800003, 498.69299, 151.6794, 506.66699], "spans": [[1, 0]], "text": "SET CURRENT DEGREE  (SQL statement)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": true, "row-span": [1, 2]}, {"bbox": [429.0, 498.55798, 435.00299000000007, 506.883], "spans": [[1, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": null, "spans": [[1, 2]], "text": "", "type": "body"}, {"bbox": [480.00031, 498.55798, 486.0033, 506.883], "spans": [[1, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": null, "spans": [[1, 4]], "text": "", "type": "body"}, {"bbox": null, "spans": [[1, 5]], "text": "", "type": "body"}], [{"bbox": [70.800018, 517.65329, 102.23972, 525.62729], "spans": [[2, 0]], "text": "CHGQRYA  command targeting a different user’s job", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [429.00003, 517.51828, 435.00302000000005, 525.84329], "spans": [[2, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": null, "spans": [[2, 2]], "text": "", "type": "body"}, {"bbox": [480.00034, 517.51828, 486.00333, 525.84329], "spans": [[2, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": null, "spans": [[2, 4]], "text": "", "type": "body"}, {"bbox": null, "spans": [[2, 5]], "text": "", "type": "body"}], [{"bbox": [70.800049, 536.67299, 106.73975, 544.64699], "spans": [[3, 0]], "text": "STRDBMON  or  ENDDBMON  commands targeting a different user’s job", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [429.00003, 536.5379800000001, 435.00302000000005, 544.8629900000001], "spans": [[3, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": null, "spans": [[3, 2]], "text": "", "type": "body"}, {"bbox": [480.00034, 536.5379800000001, 486.00333, 544.8629900000001], "spans": [[3, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": null, "spans": [[3, 4]], "text": "", "type": "body"}, {"bbox": null, "spans": [[3, 5]], "text": "", "type": "body"}], [{"bbox": [70.800049, 555.69269, 106.73975, 563.66669], "spans": [[4, 0]], "text": "STRDBMON  or  ENDDBMON  commands targeting a job that matches the current user", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [429.00003, 555.55768, 435.00302000000005, 563.8826899999999], "spans": [[4, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": null, "spans": [[4, 2]], "text": "", "type": "body"}, {"bbox": [480.00034, 555.55768, 486.00333, 563.8826899999999], "spans": [[4, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [505.26061999999996, 555.55768, 511.26361, 563.8826899999999], "spans": [[4, 4]], "text": "X", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [530.76031, 555.55768, 536.76331, 563.8826899999999], "spans": [[4, 5]], "text": "X", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [70.800049, 574.51797, 359.51736, 582.84299], "spans": [[5, 0]], "text": "QUSRJOBI() API format 900 or System i Navigator’s SQL Details for Job", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [429.0000600000001, 574.51797, 435.00305000000003, 582.84299], "spans": [[5, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": null, "spans": [[5, 2]], "text": "", "type": "body"}, {"bbox": [480.00037, 574.51797, 486.00335999999993, 582.84299], "spans": [[5, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [505.2606799999999, 574.51797, 511.26367, 582.84299], "spans": [[5, 4]], "text": "X", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": null, "spans": [[5, 5]], "text": "", "type": "body"}], [{"bbox": [70.800079, 593.5376699999999, 220.75178999999997, 601.8626899999999], "spans": [[6, 0]], "text": "Visual Explain within Run SQL scripts", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [429.0000600000001, 593.5376699999999, 435.00305000000003, 601.8626899999999], "spans": [[6, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": null, "spans": [[6, 2]], "text": "", "type": "body"}, {"bbox": [480.00037, 593.5376699999999, 486.00335999999993, 601.8626899999999], "spans": [[6, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [505.2606799999999, 593.5376699999999, 511.26367, 601.8626899999999], "spans": [[6, 4]], "text": "X", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [530.76038, 593.5376699999999, 536.76337, 601.8626899999999], "spans": [[6, 5]], "text": "X", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 6, "row-header": false, "row-span": [6, 7]}], [{"bbox": [70.800079, 612.55737, 236.6548, 620.88239], "spans": [[7, 0]], "text": "Visual Explain outside of Run SQL scripts", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 7, "row-header": true, "row-span": [7, 8]}, {"bbox": [429.0000600000001, 612.55737, 435.00305000000003, 620.88239], "spans": [[7, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": null, "spans": [[7, 2]], "text": "", "type": "body"}, {"bbox": [480.00037, 612.55737, 486.00335999999993, 620.88239], "spans": [[7, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": null, "spans": [[7, 4]], "text": "", "type": "body"}, {"bbox": null, "spans": [[7, 5]], "text": "", "type": "body"}], [{"bbox": [70.800079, 631.51767, 213.12968, 639.84268], "spans": [[8, 0]], "text": "ANALYZE PLAN CACHE procedure", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 8, "row-header": true, "row-span": [8, 9]}, {"bbox": [429.0000600000001, 631.51767, 435.00305000000003, 639.84268], "spans": [[8, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": null, "spans": [[8, 2]], "text": "", "type": "body"}, {"bbox": [480.00037, 631.51767, 486.00335999999993, 639.84268], "spans": [[8, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": null, "spans": [[8, 4]], "text": "", "type": "body"}, {"bbox": null, "spans": [[8, 5]], "text": "", "type": "body"}], [{"bbox": [70.800079, 650.53737, 199.87808, 658.86238], "spans": [[9, 0]], "text": "DUMP PLAN CACHE procedure", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 9, "row-header": true, "row-span": [9, 10]}, {"bbox": [429.0000600000001, 650.53737, 435.00305000000003, 658.86238], "spans": [[9, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": null, "spans": [[9, 2]], "text": "", "type": "body"}, {"bbox": [480.00037, 650.53737, 486.00335999999993, 658.86238], "spans": [[9, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": null, "spans": [[9, 4]], "text": "", "type": "body"}, {"bbox": null, "spans": [[9, 5]], "text": "", "type": "body"}], [{"bbox": [70.800079, 669.55708, 208.36777, 677.88207], "spans": [[10, 0]], "text": "MODIFY PLAN CACHE procedure", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 10, "row-header": true, "row-span": [10, 11]}, {"bbox": [429.0000600000001, 669.55708, 435.00305000000003, 677.88207], "spans": [[10, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": null, "spans": [[10, 2]], "text": "", "type": "body"}, {"bbox": [480.00037, 669.55708, 486.00335999999993, 677.88207], "spans": [[10, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": null, "spans": [[10, 4]], "text": "", "type": "body"}, {"bbox": null, "spans": [[10, 5]], "text": "", "type": "body"}], [{"bbox": [70.800079, 688.57677, 411.20264, 696.9017719999999], "spans": [[11, 0]], "text": "MODIFY PLAN CACHE PROPERTIES procedure (currently does not check authority)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 11, "row-header": true, "row-span": [11, 12]}, {"bbox": [429.0000600000001, 688.57677, 435.00305000000003, 696.9017719999999], "spans": [[11, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": null, "spans": [[11, 2]], "text": "", "type": "body"}, {"bbox": [480.00037, 688.57677, 486.00335999999993, 696.9017719999999], "spans": [[11, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 11, "row-header": false, "row-span": [11, 12]}, {"bbox": null, "spans": [[11, 4]], "text": "", "type": "body"}, {"bbox": null, "spans": [[11, 5]], "text": "", "type": "body"}], [{"bbox": [70.800079, 707.537071, 377.12585, 715.862068], "spans": [[12, 0]], "text": "CHANGE PLAN CACHE SIZE procedure (currently does not check authority)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 12, "row-header": true, "row-span": [12, 13]}, {"bbox": [429.0000600000001, 707.537071, 435.00305000000003, 715.862068], "spans": [[12, 1]], "text": "X", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": null, "spans": [[12, 2]], "text": "", "type": "body"}, {"bbox": [480.00037, 707.537071, 486.00335999999993, 715.862068], "spans": [[12, 3]], "text": "X", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 12, "row-header": false, "row-span": [12, 13]}, {"bbox": null, "spans": [[12, 4]], "text": "", "type": "body"}, {"bbox": null, "spans": [[12, 5]], "text": "", "type": "body"}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [134.5462646484375, 587.7283935546875, 542.0460815429688, 688.5811080932617], "page": 11, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 3-1 Special registers and their corresponding values", "type": "table", "payload": null, "#-cols": 2, "#-rows": 4, "data": [[{"bbox": [142.8, 110.53801999999985, 209.67091, 118.86298], "spans": [[0, 0]], "text": "Special register", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [230.18912000000003, 110.53801999999985, 319.93527, 118.86298], "spans": [[0, 1]], "text": "Corresponding value", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [142.80002, 129.49834999999996, 178.26361, 137.82330000000002], "spans": [[1, 0]], "text": "USER or SESSION_USER", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [230.21973000000003, 129.49834999999996, 467.99069000000003, 137.82330000000002], "spans": [[1, 1]], "text": "The effective user of the thread excluding adopted authority.", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [142.80003, 159.55835000000002, 216.63962999999998, 167.88329999999996], "spans": [[2, 0]], "text": "CURRENT_USER", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [230.19814, 159.55835000000002, 535.65082, 167.88329999999996], "spans": [[2, 1]], "text": "The effective user of the thread including adopted authority. When no adopted  authority is present, this has the same value as USER.", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [142.8009, 189.55804, 209.7357, 197.88300000000004], "spans": [[3, 0]], "text": "SYSTEM_USER", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [230.2449, 189.55804, 425.64569, 197.88300000000004], "spans": [[3, 1]], "text": "The authorization ID that initiated the connection.", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [63.55636978149414, 495.77532958984375, 548.5687255859375, 687.7661285400391], "page": 12, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 3-2 Built-in global variables", "type": "table", "payload": null, "#-cols": 3, "#-rows": 10, "data": [[{"bbox": [70.800003, 110.53801999999985, 134.99071, 118.86298], "spans": [[0, 0]], "text": "Global variable", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [202.8894, 110.53801999999985, 223.34641, 118.86298], "spans": [[0, 1]], "text": "Type", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [281.8248, 110.53801999999985, 331.3428, 118.86298], "spans": [[0, 2]], "text": "Description", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [70.800003, 129.49834999999996, 132.7209, 137.82330000000002], "spans": [[1, 0]], "text": "CLIENT_HOST", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [202.89029, 129.49834999999996, 267.07651, 137.82330000000002], "spans": [[1, 1]], "text": "VARCHAR(255)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [281.84732, 129.49834999999996, 510.17548, 137.82330000000002], "spans": [[1, 2]], "text": "Host name of the current client as returned by the system", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [70.800018, 148.51806999999997, 140.66522, 156.84302000000002], "spans": [[2, 0]], "text": "CLIENT_IPADDR", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [202.87231, 148.51806999999997, 267.07739, 156.84302000000002], "spans": [[2, 1]], "text": "VARCHAR(128)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [281.84549, 148.51806999999997, 509.60583, 156.84302000000002], "spans": [[2, 2]], "text": "IP address of the current client as returned by the system", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [70.800018, 167.53778, 134.98264, 175.86273000000006], "spans": [[3, 0]], "text": "CLIENT_PORT", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [202.90294, 167.53778, 242.80084, 175.86273000000006], "spans": [[3, 1]], "text": "INTEGER", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [281.79785, 167.53778, 527.59222, 175.86273000000006], "spans": [[3, 2]], "text": "Port used by the current client to communicate with the server", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [70.800018, 186.5575, 143.50925, 194.88244999999995], "spans": [[4, 0]], "text": "PACKAGE_NAME", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [202.80576, 186.5575, 267.06937, 194.88244999999995], "spans": [[4, 1]], "text": "VARCHAR(128)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [281.85187, 186.5575, 436.57259999999997, 194.88244999999995], "spans": [[4, 2]], "text": "Name of the currently running package", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [70.800018, 205.51782000000003, 156.01654, 213.84276999999997], "spans": [[5, 0]], "text": "PACKAGE_SCHEMA", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [202.83545, 205.51782000000003, 267.08646, 213.84276999999997], "spans": [[5, 1]], "text": "VARCHAR(128)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [281.87076, 205.51782000000003, 470.44678, 213.84276999999997], "spans": [[5, 2]], "text": "Schema name of the currently running package", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [70.800018, 224.53754000000004, 157.89932, 232.86248999999998], "spans": [[6, 0]], "text": "PACKAGE_VERSION", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [202.72472, 224.53754000000004, 261.98254, 232.86248999999998], "spans": [[6, 1]], "text": "VARCHAR(64)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [281.74924, 224.53754000000004, 478.8438100000001, 232.86248999999998], "spans": [[6, 2]], "text": "Version identifier of the currently running package", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}], [{"bbox": [70.800018, 243.55724999999995, 154.41992, 251.8822], "spans": [[7, 0]], "text": "ROUTINE_SCHEMA", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [202.79312, 243.55724999999995, 267.09274, 251.8822], "spans": [[7, 1]], "text": "VARCHAR(128)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [281.87164, 243.55724999999995, 464.26022, 251.8822], "spans": [[7, 2]], "text": "Schema name of the currently running routine", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 7, "row-header": false, "row-span": [7, 8]}], [{"bbox": [70.800018, 262.51757999999995, 188.43991, 270.84253], "spans": [[8, 0]], "text": "ROUTINE_SPECIFIC_NAME", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [202.84441, 262.51757999999995, 267.03693, 270.84253], "spans": [[8, 1]], "text": "VARCHAR(128)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [281.80682, 262.51757999999995, 430.40045, 270.84253], "spans": [[8, 2]], "text": "Name of the currently running routine", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 8, "row-header": false, "row-span": [8, 9]}], [{"bbox": [70.800034, 281.53726, 139.43135, 289.86227], "spans": [[9, 0]], "text": "ROUTINE_TYPE", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [202.74635, 281.53726, 239.28996000000004, 289.86227], "spans": [[9, 1]], "text": "CHAR(1)", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [281.79065, 281.53726, 425.09131, 289.86227], "spans": [[9, 2]], "text": "Type of the currently running routine", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 9, "row-header": false, "row-span": [9, 10]}]], "model": null, "bounding-box": null}], "bitmaps": null, "equations": [], "footnotes": [], "page-dimensions": [{"height": 792.0, "page": 1, "width": 612.0}, {"height": 792.0, "page": 2, "width": 612.0}, {"height": 792.0, "page": 3, "width": 612.0}, {"height": 792.0, "page": 4, "width": 612.0}, {"height": 792.0, "page": 5, "width": 612.0}, {"height": 792.0, "page": 6, "width": 612.0}, {"height": 792.0, "page": 7, "width": 612.0}, {"height": 792.0, "page": 8, "width": 612.0}, {"height": 792.0, "page": 9, "width": 612.0}, {"height": 792.0, "page": 10, "width": 612.0}, {"height": 792.0, "page": 11, "width": 612.0}, {"height": 792.0, "page": 12, "width": 612.0}, {"height": 792.0, "page": 13, "width": 612.0}, {"height": 792.0, "page": 14, "width": 612.0}, {"height": 792.0, "page": 15, "width": 612.0}, {"height": 792.0, "page": 16, "width": 612.0}, {"height": 792.0, "page": 17, "width": 612.0}, {"height": 792.0, "page": 18, "width": 612.0}], "page-footers": [], "page-headers": [], "_s3_data": null, "identifiers": null}